﻿cmake_minimum_required(VERSION 3.20)
project(auth-service VERSION 1.0.0 LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 23)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

find_package(PkgConfig REQUIRED)
find_package(Boost REQUIRED COMPONENTS system program_options)
find_package(OpenSSL REQUIRED)
pkg_check_modules(PQXX REQUIRED libpqxx)
find_package(nlohmann_json REQUIRED)

include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include)

set(SOURCES
    src/main.cpp
    src/auth_service.cpp
    src/database_manager.cpp
    src/security_manager.cpp
    src/config_manager.cpp
    src/http_server.cpp
    src/user_manager.cpp
)

add_executable(auth-service ${SOURCES})

target_link_libraries(auth-service
    ${Boost_LIBRARIES}
    ${PQXX_LIBRARIES}
    OpenSSL::SSL
    OpenSSL::Crypto
    nlohmann_json::nlohmann_json
    pthread
)

target_include_directories(auth-service PRIVATE
    ${Boost_INCLUDE_DIRS}
    ${PQXX_INCLUDE_DIRS}
)

install(TARGETS auth-service RUNTIME DESTINATION bin)

