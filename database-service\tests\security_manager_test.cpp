#include <gtest/gtest.h>
#include "database-service/security/security_manager.hpp"
#include "database-service/security/credential_store.hpp"
#include "database-service/security/jwt.hpp"
#include "database-service/core/connection_manager.hpp"
#include <memory>
#include <thread>
#include <chrono>

namespace dbservice::security {

class SecurityManagerTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create a test connection manager
        connectionString_ = "host=localhost port=5432 dbname=test_db user=test_user password=test_pass";
        sslConfig_.enabled = false;
        connectionManager_ = std::make_shared<core::ConnectionManager>(connectionString_, 5, sslConfig_);
        
        // Create security manager
        securityManager_ = std::make_unique<SecurityManager>(connectionManager_);
        
        // Set test JWT secret
        testJwtSecret_ = "test-jwt-secret-for-unit-tests-only";
        securityManager_->setJwtSecret(testJwtSecret_);
        
        // Set token expiration times (short for testing)
        securityManager_->setTokenExpirationTimes(60, 300); // 1 min access, 5 min refresh
    }

    void TearDown() override {
        if (connectionManager_) {
            connectionManager_->shutdown();
        }
    }

    std::string connectionString_;
    core::SSLConfig sslConfig_;
    std::shared_ptr<core::ConnectionManager> connectionManager_;
    std::unique_ptr<SecurityManager> securityManager_;
    std::string testJwtSecret_;
};

class CredentialStoreTest : public ::testing::Test {
protected:
    void SetUp() override {
        testEncryptionKey_ = "test-encryption-key-32-bytes-long";
    }

    void TearDown() override {
        // Reset credential store
        auto& store = CredentialStore::getInstance();
        // Note: In a real implementation, you might want a reset method
    }

    std::string testEncryptionKey_;
};

// Security Manager Tests
TEST_F(SecurityManagerTest, ConstructorTest) {
    ASSERT_NE(securityManager_, nullptr);
}

TEST_F(SecurityManagerTest, InitializeTest_Failure) {
    // In a test environment without a DB, initialization is expected to fail.
    auto result = securityManager_->initialize();
    ASSERT_FALSE(result.has_value());
    // The specific error could be DatabaseError or InitializationFailed depending on implementation
    EXPECT_TRUE(result.error().type == SecurityErrorType::DatabaseError || result.error().type == SecurityErrorType::InitializationFailed);
}

TEST_F(SecurityManagerTest, JwtSecretTest) {
    std::string newSecret = "new-test-secret";
    EXPECT_NO_THROW(securityManager_->setJwtSecret(newSecret));
}

TEST_F(SecurityManagerTest, TokenExpirationTest) {
    EXPECT_NO_THROW(securityManager_->setTokenExpirationTimes(3600, 86400));
    EXPECT_NO_THROW(securityManager_->setTokenExpirationTimes(1, 1)); // Very short expiration
}

TEST_F(SecurityManagerTest, AuthenticationTest_Failure) {
    // Without a DB and user, authentication should fail.
    auto result = securityManager_->authenticate("testuser", "testpassword");
    ASSERT_FALSE(result.has_value());
    // Expect InvalidCredentials because the user is not found and password check fails.
    EXPECT_EQ(result.error().type, SecurityErrorType::InvalidCredentials);
}

TEST_F(SecurityManagerTest, TokenValidationTest_InvalidToken) {
    // validateToken now returns std::expected<string, SecurityError>
    auto result = securityManager_->validateToken("invalid-token");
    ASSERT_FALSE(result.has_value());
    EXPECT_EQ(result.error().type, SecurityErrorType::TokenValidationFailed);

    auto result_empty = securityManager_->validateToken("");
    ASSERT_FALSE(result_empty.has_value());
    EXPECT_EQ(result_empty.error().type, SecurityErrorType::TokenValidationFailed);
}

TEST_F(SecurityManagerTest, UserCreationTest_Failure) {
    // Without a DB, user creation should fail.
    auto result = securityManager_->createUser("newuser", "newpassword", false);
    ASSERT_FALSE(result.has_value());
    // The specific error could be UserCreationFailure or DatabaseError
    EXPECT_TRUE(result.error().type == SecurityErrorType::UserCreationFailure || result.error().type == SecurityErrorType::DatabaseError);
}

TEST_F(SecurityManagerTest, PermissionTest_Failure) {
    std::string username = "testuser";
    std::string permission = "read_data";

    // Without a DB, all permission operations should fail.
    auto hasPermResult = securityManager_->hasPermission(username, permission);
    ASSERT_FALSE(hasPermResult.has_value());
    EXPECT_TRUE(hasPermResult.error().type == SecurityErrorType::DatabaseError || hasPermResult.error().type == SecurityErrorType::OperationFailed);

    auto grantResult = securityManager_->grantPermission(username, permission);
    ASSERT_FALSE(grantResult.has_value());
    EXPECT_TRUE(grantResult.error().type == SecurityErrorType::DatabaseError || grantResult.error().type == SecurityErrorType::OperationFailed);

    auto revokeResult = securityManager_->revokePermission(username, permission);
    ASSERT_FALSE(revokeResult.has_value());
    EXPECT_TRUE(revokeResult.error().type == SecurityErrorType::DatabaseError || revokeResult.error().type == SecurityErrorType::OperationFailed);
}

// Credential Store Tests
TEST_F(CredentialStoreTest, SingletonTest) {
    auto& store1 = CredentialStore::getInstance();
    auto& store2 = CredentialStore::getInstance();
    
    // Should be the same instance
    EXPECT_EQ(&store1, &store2);
}

TEST_F(CredentialStoreTest, InitializationTest) {
    auto& store = CredentialStore::getInstance();
    
    // Test initialization with valid key
    bool initialized = store.initialize(testEncryptionKey_);
    EXPECT_TRUE(initialized);
    
    // Test initialization with empty key
    bool failedInit = store.initialize("");
    EXPECT_FALSE(failedInit);
}

TEST_F(CredentialStoreTest, CredentialOperationsTest) {
    auto& store = CredentialStore::getInstance();
    store.initialize(testEncryptionKey_);
    
    std::string key = "test-credential";
    std::string value = "secret-value";
    
    // Store credential
    bool stored = store.storeCredential(key, value);
    EXPECT_TRUE(stored);
    
    // Retrieve credential
    std::string retrieved = store.getCredential(key);
    EXPECT_EQ(retrieved, value);
    
    // Remove credential
    bool removed = store.removeCredential(key);
    EXPECT_TRUE(removed);
    
    // Try to retrieve removed credential
    std::string notFound = store.getCredential(key);
    EXPECT_TRUE(notFound.empty());
}

TEST_F(CredentialStoreTest, NonExistentCredentialTest) {
    auto& store = CredentialStore::getInstance();
    store.initialize(testEncryptionKey_);
    
    // Try to get non-existent credential
    std::string notFound = store.getCredential("non-existent-key");
    EXPECT_TRUE(notFound.empty());
    
    // Try to remove non-existent credential
    bool removed = store.removeCredential("non-existent-key");
    EXPECT_FALSE(removed);
}

// JWT Tests
TEST(JWTTest, TokenStructureTest) {
    // Test TokenPair structure
    TokenPair tokens;
    tokens.accessToken = "access-token";
    tokens.refreshToken = "refresh-token";
    
    EXPECT_EQ(tokens.accessToken, "access-token");
    EXPECT_EQ(tokens.refreshToken, "refresh-token");
}

TEST(JWTTest, EmptyTokenTest) {
    TokenPair emptyTokens;
    EXPECT_TRUE(emptyTokens.accessToken.empty());
    EXPECT_TRUE(emptyTokens.refreshToken.empty());
}

// Password Security Tests
TEST(PasswordSecurityTest, PasswordValidationTest) {
    // Test password strength validation (if implemented)
    std::string weakPassword = "123";
    std::string strongPassword = "StrongP@ssw0rd123!";
    
    // These tests would depend on actual password validation implementation
    EXPECT_TRUE(weakPassword.length() < 8); // Weak password is short
    EXPECT_TRUE(strongPassword.length() >= 8); // Strong password is long enough
    
    // Test for various character types
    bool hasUpper = std::any_of(strongPassword.begin(), strongPassword.end(), ::isupper);
    bool hasLower = std::any_of(strongPassword.begin(), strongPassword.end(), ::islower);
    bool hasDigit = std::any_of(strongPassword.begin(), strongPassword.end(), ::isdigit);
    
    EXPECT_TRUE(hasUpper);
    EXPECT_TRUE(hasLower);
    EXPECT_TRUE(hasDigit);
}

// Security Configuration Tests
TEST(SecurityConfigTest, DefaultConfigurationTest) {
    // Test default security settings
    int defaultAccessTokenExpiration = 3600; // 1 hour
    int defaultRefreshTokenExpiration = 86400; // 24 hours
    
    EXPECT_GT(defaultAccessTokenExpiration, 0);
    EXPECT_GT(defaultRefreshTokenExpiration, defaultAccessTokenExpiration);
}

TEST(SecurityConfigTest, ExpirationTimesTest) {
    // Test various expiration time configurations
    int shortExpiration = 60; // 1 minute
    int longExpiration = 604800; // 1 week
    
    EXPECT_GT(shortExpiration, 0);
    EXPECT_GT(longExpiration, shortExpiration);
    
    // Test that refresh token expiration is longer than access token
    EXPECT_GT(longExpiration, shortExpiration);
}

// Thread Safety Tests
TEST(SecurityThreadSafetyTest, ConcurrentAccessTest) {
    // Test concurrent access to credential store
    auto& store = CredentialStore::getInstance();
    std::string testKey = "test-encryption-key-32-bytes-long";
    store.initialize(testKey);
    
    const int numThreads = 10;
    const int operationsPerThread = 100;
    std::vector<std::thread> threads;
    
    // Launch threads that perform concurrent operations
    for (int i = 0; i < numThreads; ++i) {
        threads.emplace_back([&store, i, operationsPerThread]() {
            for (int j = 0; j < operationsPerThread; ++j) {
                std::string key = "key_" + std::to_string(i) + "_" + std::to_string(j);
                std::string value = "value_" + std::to_string(i) + "_" + std::to_string(j);
                
                store.storeCredential(key, value);
                std::string retrieved = store.getCredential(key);
                store.removeCredential(key);
            }
        });
    }
    
    // Wait for all threads to complete
    for (auto& thread : threads) {
        thread.join();
    }
    
    // If we reach here without crashing, the test passes
    SUCCEED();
}

} // namespace dbservice::security
