#pragma once

#include <memory>
#include <chrono>
#include <expected>
#include <string>
#include <format>
#include <atomic>
#include <mutex>
#include <source_location>
#include <span>
#include "database-service/core/connection.hpp"

namespace dbservice::core {

/**
 * @class Transaction
 * @brief RAII wrapper for database transactions with ACID guarantees
 * 
 * The Transaction class provides a scoped, exception-safe way to execute database
 * operations within a transaction. Transactions are automatically rolled back
 * if not explicitly committed when the Transaction object is destroyed.
 * 
 * @note This class is thread-safe for concurrent method calls on different instances,
 *       but individual instances should not be shared between threads.
 * 
 * @example
 *   // Begin a transaction
 *   auto txn = conn.beginTransaction();
 *   try {
 *       // Execute operations within the transaction
 *       txn->getConnection()->executeNonQuery("UPDATE accounts SET balance = balance - 100 WHERE id = 1");
 *       txn->getConnection()->executeNonQuery("UPDATE accounts SET balance = balance + 100 WHERE id = 2");
 *       
 *       // Commit if all operations succeed
 *       if (auto result = txn->commit(); !result) {
 *           utils::Logger::error("Failed to commit transaction: {}", result.error());
 *       }
 *   } catch (const std::exception& e) {
 *       // Transaction will be rolled back automatically when txn goes out of scope
 *       utils::Logger::error("Transaction failed: {}", e.what());
 *   }
 */
class Transaction {
public:
    /**
     * @brief Construct a new Transaction object
     * 
     * @param connection Shared pointer to an active database connection
     * @throws std::invalid_argument if connection is null or not open
     * 
     * @note The transaction begins immediately upon construction
     */
    explicit Transaction(std::shared_ptr<Connection> connection);
    
    // Non-copyable
    Transaction(const Transaction&) = delete;
    Transaction& operator=(const Transaction&) = delete;
    
    // Movable
    Transaction(Transaction&& other) noexcept;
    Transaction& operator=(Transaction&& other) noexcept;
    
    /**
     * @brief Destroy the Transaction object
     * 
     * @note If neither commit() nor rollback() was called, performs an automatic rollback
     */
    ~Transaction() noexcept;

    /**
     * @brief Commit the current transaction
     * 
     * @return std::expected<void, std::string> 
     *         - Success: void
     *         - Error: String describing the failure
     * 
     * @throws std::logic_error if commit() is called after commit() or rollback()
     * 
     * @note After successful commit, the transaction object is no longer active
     * @note This operation is thread-safe
     */
    std::expected<void, std::string> commit(
        const std::source_location& loc = std::source_location::current());

    /**
     * @brief Roll back the current transaction
     * 
     * @return std::expected<void, std::string> 
     *         - Success: void
     *         - Error: String describing the failure
     * 
     * @throws std::logic_error if rollback() is called after commit() or rollback()
     * 
     * @note After rollback, the transaction object is no longer active
     * @note This operation is thread-safe
     */
    std::expected<void, std::string> rollback(
        const std::source_location& loc = std::source_location::current());

    /**
     * @brief Get the underlying database connection
     * 
     * @return std::shared_ptr<Connection> Shared pointer to the connection
     * 
     * @note The returned connection should only be used within the transaction scope
     * @warning Do not call beginTransaction() on the returned connection
     *          while this transaction is active
     */
    std::shared_ptr<Connection> getConnection() const noexcept;
    
    /**
     * @brief Check if the transaction is still active
     * 
     * @return true If the transaction is active (not committed or rolled back)
     * @return false If the transaction has been committed or rolled back
     * 
     * @note This operation is thread-safe
     */
    bool isActive() const noexcept;
    
    /**
     * @brief Get the transaction start time
     * 
     * @return std::chrono::steady_clock::time_point The time when the transaction started
     */
    std::chrono::steady_clock::time_point getStartTime() const noexcept;
    
    /**
     * @brief Get the transaction duration
     * 
     * @return std::chrono::milliseconds The duration since the transaction started
     * 
     * @note If the transaction is complete, returns the total duration
     */
    std::chrono::milliseconds getDuration() const noexcept;

private:
    // Internal state management
    enum class State {
        Active,     ///< Transaction is active
        Committed,  ///< Transaction was committed
        RolledBack  ///< Transaction was rolled back
    };
    
    // Thread-safe state management
    bool transitionState(State expected, State desired) noexcept;
    
    // Connection and state
    std::shared_ptr<Connection> connection_;
    std::atomic<State> state_;
    const std::chrono::steady_clock::time_point startTime_;
    mutable std::mutex mutex_;
};

} // namespace dbservice::core
