# Manage Database Schemas Module
# Provides comprehensive schema management functionality for the Database Service

# Import common module
Import-Module -Name (Join-Path $PSScriptRoot '..\Common.psm1') -Force
# Import Logger module
Import-Module -Name (Join-Path $PSScriptRoot 'Logger/Logger.psm1') -Force

# Safe logging wrapper to prevent empty string errors
function Write-SafeLog {
    param(
        [string]$Message,
        [string]$Level = "Info",
        [string]$Component = "SchemaManager"
    )
    if ([string]::IsNullOrEmpty($Message)) {
        $Message = "[Empty message]"
    }
    Write-Log -Message $Message -Level $Level -Component $Component
}

# Helper function to validate database connection
function Test-DatabaseConnection {
    param(
        [string]$DatabaseName = $null
    )
    
    if ($null -eq $global:Config -or $null -eq $global:Config.database) {
        Write-SafeLog -Message "Database configuration not found. Please configure database settings first." -Level "Error"
        return $false
    }
    
    $dbName = if ($DatabaseName) { $DatabaseName } else { $global:Config.database.name }
    $dbUser = $global:Config.database.user
    
    # Test PostgreSQL connection
    $testCmd = @"
sudo -u postgres psql -d $dbName -c "SELECT 1;" > /dev/null 2>&1 && echo "SUCCESS" || echo "FAILED"
"@
    $result = Invoke-RemoteCommand -Command $testCmd -Silent
    
    if ($result -eq "SUCCESS") {
        Write-SafeLog -Message "Database connection to '$dbName' successful." -Level "Info"
        return $true
    } else {
        Write-SafeLog -Message "Failed to connect to database '$dbName'. Please check database configuration." -Level "Error"
        return $false
    }
}

# Get list of all schemas in the database
function Get-DatabaseSchemas {
    param(
        [string]$DatabaseName = $null
    )
    
    Clear-Host
    Write-SafeLog -Message "========================================================" -Level "Info"
    Write-SafeLog -Message "               Get Database Schemas                   " -Level "Info"
    Write-SafeLog -Message "========================================================" -Level "Info"
    Write-SafeLog -Message " " -Level "Info"
    
    if (-not (Test-DatabaseConnection -DatabaseName $DatabaseName)) {
        Wait-ForUser
        return
    }
    
    $dbName = if ($DatabaseName) { $DatabaseName } else { $global:Config.database.name }
    
    Write-SafeLog -Message "Retrieving schemas from database: $dbName" -Level "Info"
    
    $getSchemasCmd = @"
sudo -u postgres psql -d $dbName -t -c "
SELECT 
    schema_name,
    CASE 
        WHEN schema_name = 'public' THEN 'Default schema'
        WHEN schema_name LIKE 'pg_%' THEN 'System schema'
        ELSE 'User schema'
    END as description
FROM information_schema.schemata 
WHERE schema_name NOT IN ('pg_catalog', 'information_schema', 'pg_toast')
    AND schema_name NOT LIKE 'pg_temp_%' 
    AND schema_name NOT LIKE 'pg_toast_temp_%'
ORDER BY schema_name;
"
"@
    
    $result = Invoke-RemoteCommand -Command $getSchemasCmd
    
    if ([string]::IsNullOrEmpty($result)) {
        Write-SafeLog -Message "No schemas found or query failed." -Level "Warning"
    } else {
        Write-SafeLog -Message "Available schemas in database '$dbName':" -Level "Info"
        Write-SafeLog -Message "----------------------------------------" -Level "Info"
        Write-Host $result -ForegroundColor White
        
        # Also get table counts for each schema
        Write-SafeLog -Message " " -Level "Info"
        Write-SafeLog -Message "Schema table counts:" -Level "Info"
        Write-SafeLog -Message "-------------------" -Level "Info"
        
        $getTableCountsCmd = @"
sudo -u postgres psql -d $dbName -t -c "
SELECT 
    schemaname as schema_name,
    COUNT(*) as table_count
FROM pg_tables 
WHERE schemaname NOT IN ('pg_catalog', 'information_schema')
GROUP BY schemaname
ORDER BY schemaname;
"
"@
        $tableCounts = Invoke-RemoteCommand -Command $getTableCountsCmd
        if (-not [string]::IsNullOrEmpty($tableCounts)) {
            Write-Host $tableCounts -ForegroundColor Cyan
        }
    }
    
    Wait-ForUser
}

# Create a new database schema
function New-DatabaseSchema {
    param(
        [string]$SchemaName = $null,
        [string]$DatabaseName = $null,
        [string]$Owner = $null
    )
    
    Clear-Host
    Write-SafeLog -Message "========================================================" -Level "Info"
    Write-SafeLog -Message "               Create Database Schema                 " -Level "Info"
    Write-SafeLog -Message "========================================================" -Level "Info"
    Write-SafeLog -Message " " -Level "Info"
    
    if (-not (Test-DatabaseConnection -DatabaseName $DatabaseName)) {
        Wait-ForUser
        return
    }
    
    $dbName = if ($DatabaseName) { $DatabaseName } else { $global:Config.database.name }
    
    # Get schema name if not provided
    if ([string]::IsNullOrEmpty($SchemaName)) {
        $SchemaName = Read-Host "Enter schema name"
        if ([string]::IsNullOrEmpty($SchemaName)) {
            Write-SafeLog -Message "Schema name cannot be empty." -Level "Error"
            Wait-ForUser
            return
        }
    }
    
    # Get owner if not provided
    if ([string]::IsNullOrEmpty($Owner)) {
        $Owner = Read-Host "Enter schema owner (default: $($global:Config.database.user))"
        if ([string]::IsNullOrEmpty($Owner)) {
            $Owner = $global:Config.database.user
        }
    }
    
    Write-SafeLog -Message "Creating schema '$SchemaName' in database '$dbName' with owner '$Owner'..." -Level "Info"
    
    # Check if schema already exists
    $checkSchemaCmd = @"
sudo -u postgres psql -d $dbName -t -c "SELECT 1 FROM information_schema.schemata WHERE schema_name = '$SchemaName';" | wc -l
"@
    $schemaExists = Invoke-RemoteCommand -Command $checkSchemaCmd -Silent
    
    if ($schemaExists -gt 0) {
        Write-SafeLog -Message "Schema '$SchemaName' already exists in database '$dbName'." -Level "Warning"
        $overwrite = Read-Host "Do you want to drop and recreate it? (y/n)"
        if ($overwrite -ne "y") {
            Write-SafeLog -Message "Schema creation cancelled." -Level "Info"
            Wait-ForUser
            return
        }
        
        # Drop existing schema
        Write-SafeLog -Message "Dropping existing schema '$SchemaName'..." -Level "Warning"
        $dropCmd = @"
sudo -u postgres psql -d $dbName -c "DROP SCHEMA $SchemaName CASCADE;" 2>&1
"@
        $dropResult = Invoke-RemoteCommand -Command $dropCmd
        if ($dropResult -match "ERROR") {
            Write-SafeLog -Message "Failed to drop existing schema: $dropResult" -Level "Error"
            Wait-ForUser
            return
        }
    }
    
    # Create the schema
    $createSchemaCmd = @"
sudo -u postgres psql -d $dbName -c "CREATE SCHEMA $SchemaName AUTHORIZATION $Owner;" 2>&1
"@
    $result = Invoke-RemoteCommand -Command $createSchemaCmd
    
    if ($result -match "ERROR") {
        Write-SafeLog -Message "Failed to create schema: $result" -Level "Error"
    } else {
        Write-SafeLog -Message "Schema '$SchemaName' created successfully in database '$dbName'!" -Level "Success"
        
        # Grant permissions
        $grantCmd = @"
sudo -u postgres psql -d $dbName -c "GRANT ALL ON SCHEMA $SchemaName TO $Owner;" 2>&1
"@
        Invoke-RemoteCommand -Command $grantCmd -Silent
        Write-SafeLog -Message "Permissions granted to '$Owner' on schema '$SchemaName'." -Level "Info"
    }
    
    Wait-ForUser
}

# Remove/Drop a database schema
function Remove-DatabaseSchema {
    param(
        [string]$SchemaName = $null,
        [string]$DatabaseName = $null,
        [switch]$Force
    )
    
    Clear-Host
    Write-SafeLog -Message "========================================================" -Level "Info"
    Write-SafeLog -Message "               Remove Database Schema                 " -Level "Info"
    Write-SafeLog -Message "========================================================" -Level "Info"
    Write-SafeLog -Message " " -Level "Info"
    
    if (-not (Test-DatabaseConnection -DatabaseName $DatabaseName)) {
        Wait-ForUser
        return
    }
    
    $dbName = if ($DatabaseName) { $DatabaseName } else { $global:Config.database.name }
    
    # Get schema name if not provided
    if ([string]::IsNullOrEmpty($SchemaName)) {
        # Show available schemas first
        Write-SafeLog -Message "Available schemas:" -Level "Info"
        $listSchemasCmd = @"
sudo -u postgres psql -d $dbName -t -c "SELECT schema_name FROM information_schema.schemata WHERE schema_name NOT IN ('pg_catalog', 'information_schema', 'pg_toast') ORDER BY schema_name;"
"@
        $schemas = Invoke-RemoteCommand -Command $listSchemasCmd
        if (-not [string]::IsNullOrEmpty($schemas)) {
            Write-Host $schemas -ForegroundColor Cyan
        }
        
        $SchemaName = Read-Host "Enter schema name to remove"
        if ([string]::IsNullOrEmpty($SchemaName)) {
            Write-SafeLog -Message "Schema name cannot be empty." -Level "Error"
            Wait-ForUser
            return
        }
    }
    
    # Check if schema exists
    $checkSchemaCmd = @"
sudo -u postgres psql -d $dbName -t -c "SELECT 1 FROM information_schema.schemata WHERE schema_name = '$SchemaName';" | wc -l
"@
    $schemaExists = Invoke-RemoteCommand -Command $checkSchemaCmd -Silent
    
    if ($schemaExists -eq 0) {
        Write-SafeLog -Message "Schema '$SchemaName' does not exist in database '$dbName'." -Level "Warning"
        Wait-ForUser
        return
    }
    
    # Safety check for important schemas
    if ($SchemaName -in @("public", "information_schema", "pg_catalog")) {
        Write-SafeLog -Message "Cannot remove system schema '$SchemaName'." -Level "Error"
        Wait-ForUser
        return
    }
    
    # Confirmation unless Force is specified
    if (-not $Force) {
        Write-SafeLog -Message "WARNING: This will permanently delete schema '$SchemaName' and ALL its contents!" -Level "Warning"
        $confirm = Read-Host "Are you sure you want to remove schema '$SchemaName'? Type 'DELETE' to confirm"
        if ($confirm -ne "DELETE") {
            Write-SafeLog -Message "Schema removal cancelled." -Level "Info"
            Wait-ForUser
            return
        }
    }
    
    Write-SafeLog -Message "Removing schema '$SchemaName' from database '$dbName'..." -Level "Warning"
    
    # Drop the schema with CASCADE
    $dropSchemaCmd = @"
sudo -u postgres psql -d $dbName -c "DROP SCHEMA $SchemaName CASCADE;" 2>&1
"@
    $result = Invoke-RemoteCommand -Command $dropSchemaCmd
    
    if ($result -match "ERROR") {
        Write-SafeLog -Message "Failed to remove schema: $result" -Level "Error"
    } else {
        Write-SafeLog -Message "Schema '$SchemaName' removed successfully from database '$dbName'!" -Level "Success"
    }
    
    Wait-ForUser
}

# Get current schema version
function Get-SchemaVersion {
    param(
        [string]$SchemaName = "public",
        [string]$DatabaseName = $null
    )

    Clear-Host
    Write-SafeLog -Message "========================================================" -Level "Info"
    Write-SafeLog -Message "               Get Schema Version                     " -Level "Info"
    Write-SafeLog -Message "========================================================" -Level "Info"
    Write-SafeLog -Message " " -Level "Info"

    if (-not (Test-DatabaseConnection -DatabaseName $DatabaseName)) {
        Wait-ForUser
        return
    }

    $dbName = if ($DatabaseName) { $DatabaseName } else { $global:Config.database.name }

    Write-SafeLog -Message "Checking schema version for '$SchemaName' in database '$dbName'..." -Level "Info"

    # Check if schema_version table exists
    $checkTableCmd = @"
sudo -u postgres psql -d $dbName -t -c "SELECT 1 FROM information_schema.tables WHERE table_name = 'schema_version';" | wc -l
"@
    $tableExists = Invoke-RemoteCommand -Command $checkTableCmd -Silent

    if ($tableExists -eq 0) {
        Write-SafeLog -Message "Schema version table does not exist. No version tracking available." -Level "Warning"
        Wait-ForUser
        return
    }

    # Get current version
    $getVersionCmd = @"
sudo -u postgres psql -d $dbName -t -c "
SELECT
    version,
    description,
    applied_at
FROM schema_version
ORDER BY applied_at DESC
LIMIT 1;
"
"@
    $result = Invoke-RemoteCommand -Command $getVersionCmd

    if ([string]::IsNullOrEmpty($result)) {
        Write-SafeLog -Message "No schema version found in database '$dbName'." -Level "Warning"
    } else {
        Write-SafeLog -Message "Current schema version:" -Level "Info"
        Write-SafeLog -Message "----------------------" -Level "Info"
        Write-Host $result -ForegroundColor Green

        # Also show all versions
        Write-SafeLog -Message " " -Level "Info"
        Write-SafeLog -Message "All schema versions:" -Level "Info"
        Write-SafeLog -Message "-------------------" -Level "Info"
        $getAllVersionsCmd = @"
sudo -u postgres psql -d $dbName -t -c "
SELECT
    version,
    description,
    applied_at
FROM schema_version
ORDER BY applied_at DESC;
"
"@
        $allVersions = Invoke-RemoteCommand -Command $getAllVersionsCmd
        if (-not [string]::IsNullOrEmpty($allVersions)) {
            Write-Host $allVersions -ForegroundColor Cyan
        }
    }

    Wait-ForUser
}

# Apply a schema migration
function Apply-SchemaMigration {
    param(
        [string]$MigrationFile = $null,
        [string]$SchemaName = "public",
        [string]$DatabaseName = $null,
        [string]$Version = $null,
        [string]$Description = $null
    )

    Clear-Host
    Write-SafeLog -Message "========================================================" -Level "Info"
    Write-SafeLog -Message "               Apply Schema Migration                 " -Level "Info"
    Write-SafeLog -Message "========================================================" -Level "Info"
    Write-SafeLog -Message " " -Level "Info"

    if (-not (Test-DatabaseConnection -DatabaseName $DatabaseName)) {
        Wait-ForUser
        return
    }

    $dbName = if ($DatabaseName) { $DatabaseName } else { $global:Config.database.name }
    $installDir = $global:Config.service.install_dir
    if ([string]::IsNullOrEmpty($installDir)) {
        $installDir = "/opt/database-service"
    }

    # Get migration file if not provided
    if ([string]::IsNullOrEmpty($MigrationFile)) {
        # List available migration files
        Write-SafeLog -Message "Available migration files:" -Level "Info"
        $listMigrationsCmd = "ls -la $installDir/schemas/*.sql 2>/dev/null || echo 'No migration files found'"
        $migrations = Invoke-RemoteCommand -Command $listMigrationsCmd
        Write-Host $migrations -ForegroundColor Cyan

        $MigrationFile = Read-Host "Enter migration file name (without path)"
        if ([string]::IsNullOrEmpty($MigrationFile)) {
            Write-SafeLog -Message "Migration file name cannot be empty." -Level "Error"
            Wait-ForUser
            return
        }
    }

    # Ensure .sql extension
    if (-not $MigrationFile.EndsWith(".sql")) {
        $MigrationFile += ".sql"
    }

    $migrationPath = "$installDir/schemas/$MigrationFile"

    # Check if migration file exists
    $checkFileCmd = "test -f $migrationPath && echo 'EXISTS' || echo 'NOT_EXISTS'"
    $fileExists = Invoke-RemoteCommand -Command $checkFileCmd -Silent

    if ($fileExists -ne "EXISTS") {
        Write-SafeLog -Message "Migration file not found: $migrationPath" -Level "Error"
        Wait-ForUser
        return
    }

    # Get version and description if not provided
    if ([string]::IsNullOrEmpty($Version)) {
        $Version = Read-Host "Enter version for this migration (e.g., 1.1.0)"
        if ([string]::IsNullOrEmpty($Version)) {
            $Version = "1.0.0"
        }
    }

    if ([string]::IsNullOrEmpty($Description)) {
        $Description = Read-Host "Enter description for this migration"
        if ([string]::IsNullOrEmpty($Description)) {
            $Description = "Applied migration: $MigrationFile"
        }
    }

    Write-SafeLog -Message "Applying migration '$MigrationFile' to schema '$SchemaName' in database '$dbName'..." -Level "Info"
    Write-SafeLog -Message "Version: $Version" -Level "Info"
    Write-SafeLog -Message "Description: $Description" -Level "Info"

    # Create schema_migrations table if it doesn't exist
    $createMigrationsTableCmd = @"
sudo -u postgres psql -d $dbName -c "
CREATE TABLE IF NOT EXISTS schema_migrations (
    id SERIAL PRIMARY KEY,
    schema_name VARCHAR(255) NOT NULL,
    migration_name VARCHAR(255) NOT NULL,
    applied_at TIMESTAMP NOT NULL DEFAULT NOW(),
    UNIQUE (schema_name, migration_name)
);" 2>&1
"@
    Invoke-RemoteCommand -Command $createMigrationsTableCmd -Silent

    # Check if migration already applied
    $checkAppliedCmd = @"
sudo -u postgres psql -d $dbName -t -c "SELECT 1 FROM schema_migrations WHERE schema_name = '$SchemaName' AND migration_name = '$MigrationFile';" | wc -l
"@
    $alreadyApplied = Invoke-RemoteCommand -Command $checkAppliedCmd -Silent

    if ($alreadyApplied -gt 0) {
        Write-SafeLog -Message "Migration '$MigrationFile' already applied to schema '$SchemaName'." -Level "Warning"
        $reapply = Read-Host "Do you want to reapply it? (y/n)"
        if ($reapply -ne "y") {
            Write-SafeLog -Message "Migration cancelled." -Level "Info"
            Wait-ForUser
            return
        }

        # Remove from migrations table to allow reapplication
        $removeRecordCmd = @"
sudo -u postgres psql -d $dbName -c "DELETE FROM schema_migrations WHERE schema_name = '$SchemaName' AND migration_name = '$MigrationFile';" 2>&1
"@
        Invoke-RemoteCommand -Command $removeRecordCmd -Silent
    }

    # Apply the migration
    $applyMigrationCmd = @"
sudo -u postgres psql -d $dbName -f $migrationPath 2>&1
"@
    $result = Invoke-RemoteCommand -Command $applyMigrationCmd

    if ($result -match "ERROR") {
        Write-SafeLog -Message "Failed to apply migration: $result" -Level "Error"
    } else {
        Write-SafeLog -Message "Migration applied successfully!" -Level "Success"

        # Record the migration
        $recordMigrationCmd = @"
sudo -u postgres psql -d $dbName -c "INSERT INTO schema_migrations (schema_name, migration_name) VALUES ('$SchemaName', '$MigrationFile');" 2>&1
"@
        Invoke-RemoteCommand -Command $recordMigrationCmd -Silent

        # Update schema version if schema_version table exists
        $updateVersionCmd = @"
sudo -u postgres psql -d $dbName -c "INSERT INTO schema_version (version, description) VALUES ('$Version', '$Description') ON CONFLICT DO NOTHING;" 2>&1
"@
        Invoke-RemoteCommand -Command $updateVersionCmd -Silent

        Write-SafeLog -Message "Migration '$MigrationFile' recorded in schema_migrations table." -Level "Info"
        Write-SafeLog -Message "Schema version updated to: $Version" -Level "Info"
    }

    Wait-ForUser
}

# Get list of available migrations
function Get-AvailableMigrations {
    param(
        [string]$DatabaseName = $null
    )

    Clear-Host
    Write-SafeLog -Message "========================================================" -Level "Info"
    Write-SafeLog -Message "               Available Migrations                   " -Level "Info"
    Write-SafeLog -Message "========================================================" -Level "Info"
    Write-SafeLog -Message " " -Level "Info"

    if (-not (Test-DatabaseConnection -DatabaseName $DatabaseName)) {
        Wait-ForUser
        return
    }

    $installDir = $global:Config.service.install_dir
    if ([string]::IsNullOrEmpty($installDir)) {
        $installDir = "/opt/database-service"
    }

    Write-SafeLog -Message "Checking for migration files in: $installDir/schemas/" -Level "Info"

    # List migration files
    $listMigrationsCmd = @"
if [ -d $installDir/schemas ]; then
    echo "Migration files found:"
    echo "====================="
    ls -la $installDir/schemas/*.sql 2>/dev/null | awk '{print $9, $5, $6, $7, $8}' | grep -v "^$" || echo "No .sql files found"
    echo ""
    echo "File contents preview:"
    echo "====================="
    for file in $installDir/schemas/*.sql; do
        if [ -f "\$file" ]; then
            echo "--- \$(basename \$file) ---"
            head -10 "\$file" | grep -E "(-- |CREATE |INSERT |ALTER )" | head -5
            echo ""
        fi
    done
else
    echo "Schemas directory not found: $installDir/schemas"
fi
"@

    $result = Invoke-RemoteCommand -Command $listMigrationsCmd

    if ([string]::IsNullOrEmpty($result)) {
        Write-SafeLog -Message "No migration files found or directory does not exist." -Level "Warning"
    } else {
        Write-Host $result -ForegroundColor Cyan
    }

    # Also check deployment_files for additional migrations
    Write-SafeLog -Message " " -Level "Info"
    Write-SafeLog -Message "Local deployment files:" -Level "Info"
    Write-SafeLog -Message "=======================" -Level "Info"

    # Go up two levels from Modules directory to get to the deployment root, then to deployment_files
    $deploymentRoot = Split-Path -Parent (Split-Path -Parent $PSScriptRoot)
    $localSchemaDir = Join-Path $deploymentRoot "deployment_files\schemas"
    if (Test-Path $localSchemaDir) {
        $localFiles = Get-ChildItem -Path $localSchemaDir -Filter "*.sql"
        foreach ($file in $localFiles) {
            Write-Host "Local: $($file.Name) ($($file.Length) bytes)" -ForegroundColor Yellow
        }
    } else {
        Write-SafeLog -Message "Local deployment_files/schemas directory not found." -Level "Warning"
    }

    Wait-ForUser
}

# Get list of applied migrations
function Get-AppliedMigrations {
    param(
        [string]$SchemaName = $null,
        [string]$DatabaseName = $null
    )

    Clear-Host
    Write-SafeLog -Message "========================================================" -Level "Info"
    Write-SafeLog -Message "               Applied Migrations                     " -Level "Info"
    Write-SafeLog -Message "========================================================" -Level "Info"
    Write-SafeLog -Message " " -Level "Info"

    if (-not (Test-DatabaseConnection -DatabaseName $DatabaseName)) {
        Wait-ForUser
        return
    }

    $dbName = if ($DatabaseName) { $DatabaseName } else { $global:Config.database.name }

    # Check if schema_migrations table exists
    $checkTableCmd = @"
sudo -u postgres psql -d $dbName -t -c "SELECT 1 FROM information_schema.tables WHERE table_name = 'schema_migrations';" | wc -l
"@
    $tableExists = Invoke-RemoteCommand -Command $checkTableCmd -Silent

    if ($tableExists -eq 0) {
        Write-SafeLog -Message "Schema migrations table does not exist. No migration tracking available." -Level "Warning"
        Write-SafeLog -Message "Run a migration first to create the tracking table." -Level "Info"
        Wait-ForUser
        return
    }

    Write-SafeLog -Message "Applied migrations in database '$dbName':" -Level "Info"

    # Build query based on whether schema name is specified
    $whereClause = if ($SchemaName) { "WHERE schema_name = '$SchemaName'" } else { "" }

    $getAppliedCmd = @"
sudo -u postgres psql -d $dbName -t -c "
SELECT
    schema_name,
    migration_name,
    applied_at
FROM schema_migrations
$whereClause
ORDER BY applied_at DESC;
"
"@

    $result = Invoke-RemoteCommand -Command $getAppliedCmd

    if ([string]::IsNullOrEmpty($result)) {
        $schemaMsg = if ($SchemaName) { "for schema '$SchemaName'" } else { "" }
        Write-SafeLog -Message "No applied migrations found $schemaMsg." -Level "Warning"
    } else {
        Write-SafeLog -Message "Applied migrations:" -Level "Info"
        Write-SafeLog -Message "-----------------" -Level "Info"
        Write-Host $result -ForegroundColor Green

        # Show summary
        $countCmd = @"
sudo -u postgres psql -d $dbName -t -c "SELECT COUNT(*) FROM schema_migrations $whereClause;"
"@
        $count = Invoke-RemoteCommand -Command $countCmd -Silent
        Write-SafeLog -Message " " -Level "Info"
        Write-SafeLog -Message "Total applied migrations: $count" -Level "Info"
    }

    Wait-ForUser
}

# Main menu function for schema management
function Show-SchemaManagementMenu {
    while ($true) {
        Clear-Host
        Write-Host "`n=== Database Schema Management Menu ===`n" -ForegroundColor Cyan
        Write-Host "[1] Get Database Schemas"
        Write-Host "[2] Create New Schema"
        Write-Host "[3] Remove Schema"
        Write-Host "[4] Get Schema Version"
        Write-Host "[5] Apply Schema Migration"
        Write-Host "[6] Get Available Migrations"
        Write-Host "[7] Get Applied Migrations"
        Write-Host "[8] Initialize Schema Tracking"
        Write-Host "[Q] Return to Main Menu"
        Write-Host ""

        $choice = Read-Host "Select an option"

        switch ($choice.ToUpper()) {
            "1" { Get-DatabaseSchemas }
            "2" { New-DatabaseSchema }
            "3" { Remove-DatabaseSchema }
            "4" { Get-SchemaVersion }
            "5" { Invoke-SchemaMigration }  # Using approved verb
            "6" { Get-AvailableMigrations }
            "7" { Get-AppliedMigrations }
            "8" { Initialize-SchemaTracking }
            "Q" { return }
            default {
                Write-Host "Invalid option. Please try again." -ForegroundColor Red
                Start-Sleep -Seconds 2
            }
        }
    }
}

# Initialize schema tracking tables
function Initialize-SchemaTracking {
    param(
        [string]$DatabaseName = $null
    )

    Clear-Host
    Write-SafeLog -Message "========================================================" -Level "Info"
    Write-SafeLog -Message "               Initialize Schema Tracking            " -Level "Info"
    Write-SafeLog -Message "========================================================" -Level "Info"
    Write-SafeLog -Message " " -Level "Info"

    if (-not (Test-DatabaseConnection -DatabaseName $DatabaseName)) {
        Wait-ForUser
        return
    }

    $dbName = if ($DatabaseName) { $DatabaseName } else { $global:Config.database.name }

    Write-SafeLog -Message "Initializing schema tracking tables in database '$dbName'..." -Level "Info"

    # Create schema tracking tables
    $initTrackingCmd = @"
sudo -u postgres psql -d $dbName -c "
-- Create schema_version table
CREATE TABLE IF NOT EXISTS schema_version (
    id SERIAL PRIMARY KEY,
    version VARCHAR(50) NOT NULL,
    description TEXT,
    applied_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create schema_migrations table
CREATE TABLE IF NOT EXISTS schema_migrations (
    id SERIAL PRIMARY KEY,
    schema_name VARCHAR(255) NOT NULL,
    migration_name VARCHAR(255) NOT NULL,
    applied_at TIMESTAMP NOT NULL DEFAULT NOW(),
    UNIQUE (schema_name, migration_name)
);

-- Insert initial version if not exists
INSERT INTO schema_version (version, description)
VALUES ('1.0.0', 'Initial schema tracking setup')
ON CONFLICT DO NOTHING;
" 2>&1
"@

    $result = Invoke-RemoteCommand -Command $initTrackingCmd

    if ($result -match "ERROR") {
        Write-SafeLog -Message "Failed to initialize schema tracking: $result" -Level "Error"
    } else {
        Write-SafeLog -Message "Schema tracking tables initialized successfully!" -Level "Success"
        Write-SafeLog -Message "Created tables: schema_version, schema_migrations" -Level "Info"
    }

    Wait-ForUser
}

# Alias for Apply-SchemaMigration using approved verb
function Invoke-SchemaMigration {
    param(
        [string]$MigrationFile = $null,
        [string]$SchemaName = "public",
        [string]$DatabaseName = $null,
        [string]$Version = $null,
        [string]$Description = $null
    )

    Apply-SchemaMigration -MigrationFile $MigrationFile -SchemaName $SchemaName -DatabaseName $DatabaseName -Version $Version -Description $Description
}

# Export functions
Export-ModuleMember -Function Get-DatabaseSchemas, New-DatabaseSchema, Remove-DatabaseSchema, Get-SchemaVersion, Apply-SchemaMigration, Get-AvailableMigrations, Get-AppliedMigrations, Show-SchemaManagementMenu, Initialize-SchemaTracking, Invoke-SchemaMigration
