/**
 * @file connection.cpp
 * @brief Implementation of the database connection class
 * 
 * This file contains the implementation of the Connection class which provides
 * a high-level interface for interacting with PostgreSQL databases. It handles
 * connection management, query execution, and transaction control.
 * 
 * @see connection.hpp for the class interface documentation.
 */

#include "database-service/core/connection.hpp"
#include "database-service/core/transaction.hpp"
#include "database-service/utils/logger.hpp"

// PostgreSQL client library
#include <libpq-fe.h>

// Standard Library Headers
#include <stdexcept>
#include <format>
#include <print>
#include <span>
#include <utility>
#include <atomic>
#include <mutex>
#include <memory>

// Platform-specific headers
#ifdef _WIN32
#  include <winsock2.h>
#  include <ws2tcpip.h>
#else
#  include <netdb.h>
#  include <arpa/inet.h>
#  include <sys/socket.h>
#  include <unistd.h>
#endif

namespace dbservice::core {

Connection::Connection(std::string_view connectionString, bool useSSL)
    : connectionString_(connectionString),
      useSSL_(useSSL),
      connection_(nullptr) {
    
    if (connectionString.empty()) {
        throw std::invalid_argument("Connection string cannot be empty");
    }
    
    // Initialize network subsystem on Windows
    #ifdef _WIN32
    static struct WSAInitializer {
        WSADATA wsaData;
        WSAInitializer() { WSAStartup(MAKEWORD(2, 2), &wsaData); }
        ~WSAInitializer() { WSACleanup(); }
    } wsaInit; // Static ensures one-time initialization
    #endif
}

Connection::~Connection() noexcept {
    try {
        close();
    } catch (const std::exception& e) {
        // Don't throw from destructor, but log the error
        utils::Logger::error(std::format("Error in ~Connection(): {}", e.what()));
    } catch (...) {
        utils::Logger::error("Unknown error in ~Connection()");
    }
}

Connection::Connection(Connection&& other) noexcept
    : connectionString_(std::move(other.connectionString_)),
      useSSL_(other.useSSL_),
      connection_(std::exchange(other.connection_, nullptr)),
      isOpen_(other.isOpen_) {
    // Reset the source object's state
    other.connectionString_.clear();
    other.useSSL_ = false;
    other.isOpen_ = false;
}

Connection& Connection::operator=(Connection&& other) noexcept {
    if (this != &other) {
        // Close current connection if open
        close();
        
        // Transfer ownership of resources
        connectionString_ = std::move(other.connectionString_);
        useSSL_ = other.useSSL_;
        connection_ = std::exchange(other.connection_, nullptr);
        isOpen_ = other.isOpen_;
        
        // Reset the source object
        other.connectionString_.clear();
        other.useSSL_ = false;
        other.isOpen_ = false;
    }
    return *this;
}

bool Connection::open() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    // Return immediately if already connected
    if (isOpen_ && connection_ && 
        PQstatus(static_cast<PGconn*>(connection_)) == CONNECTION_OK) {
        return true;
    }
    
    // Close any existing connection that might be in a bad state
    if (connection_) {
        PQfinish(static_cast<PGconn*>(connection_));
        connection_ = nullptr;
        isOpen_ = false;
    }

    try {
        // Connect to the database
        // Note: SSL configuration is now handled in the connection string
        connection_ = PQconnectdb(connectionString_.c_str());
        
        if (!connection_) {
            throw std::runtime_error("Failed to allocate connection object");
        }

        // Check connection status
        if (PQstatus(static_cast<PGconn*>(connection_)) != CONNECTION_OK) {
            std::string errorMessage = PQerrorMessage(static_cast<PGconn*>(connection_));
            utils::Logger::error(std::format("Failed to connect to database: {}", errorMessage));
            close();
            return false;
        }

        // Verify SSL connection if SSL was requested
        if (useSSL_) {
            // Check if SSL is actually being used
            auto sslInUse = PQsslInUse(static_cast<PGconn*>(connection_));
            if (!sslInUse) {
                utils::Logger::warning("SSL was requested but connection is not using SSL");
                // We don't fail here as the connection might still be usable
                // depending on the security requirements
            } else {
                // Log SSL details
                auto sslAttribute = [this](const char* name) -> std::string {
                    const char* value = PQsslAttribute(static_cast<PGconn*>(connection_), name);
                    return value ? value : "";
                };

                utils::Logger::info(std::format("SSL connection established: protocol={}, cipher={}, bits={}",
                    sslAttribute("protocol"), sslAttribute("cipher"), sslAttribute("key_bits")));
            }
        }

        isOpen_ = true;
        return true;
    } catch (const std::exception& e) {
        std::string error = std::format("Exception during database connection: {}", e.what());
        utils::Logger::error(error);
        close();
        throw std::runtime_error(error);
    } catch (...) {
        utils::Logger::error("Unknown exception during database connection");
        close();
        throw;
    }
}

void Connection::close() noexcept {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (connection_) {
        // Check if we're in a transaction and try to rollback
        PGconn* pgconn = static_cast<PGconn*>(connection_);
        if (PQtransactionStatus(pgconn) == PQTRANS_INTRANS) {
            PGresult* res = PQexec(pgconn, "ROLLBACK");
            if (res) {
                PQclear(res);
            }
        }
        
        // Close the connection
        PQfinish(pgconn);
        connection_ = nullptr;
        isOpen_ = false;
        
        utils::Logger::debug("Database connection closed");
    }
}

bool Connection::isOpen() const noexcept {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!connection_ || !isOpen_) {
        return false;
    }
    
    // Quick check without a roundtrip
    ConnStatusType status = PQstatus(static_cast<PGconn*>(connection_));
    if (status != CONNECTION_OK) {
        return false;
    }
    
    // Optional: Perform a quick ping to verify the connection is still alive
    // This adds a small overhead but ensures the connection is actually usable
    if (PQping(PQhost(static_cast<PGconn*>(connection_))) != PQPING_OK) {
        return false;
    }
    
    return true;
}

std::vector<std::vector<std::string>> 
Connection::executeQuery(std::string_view query, std::span<const std::string> params) {
    std::vector<std::vector<std::string>> result;
    
    if (query.empty()) {
        throw std::invalid_argument("Query string cannot be empty");
    }
    
    std::lock_guard<std::mutex> lock(mutex_);
    validateConnection();
    
    // Convert string_view to string for C API compatibility
    std::string queryStr(query);

    try {
        // Prepare and execute the statement
        std::unique_ptr<PGresult, decltype(&PQclear)> stmt(
            static_cast<PGresult*>(prepareStatement(queryStr, params)),
            PQclear
        );
        
        if (!stmt) {
            throw std::runtime_error("Failed to prepare statement");
        }
        
        // Check for execution errors
        ExecStatusType status = PQresultStatus(stmt.get());
        if (status != PGRES_TUPLES_OK && status != PGRES_COMMAND_OK) {
            throw std::runtime_error(
                std::format("Query execution failed: {}", 
                    PQresultErrorMessage(stmt.get()))
            );
        }

        // Get result set dimensions
        const int rows = PQntuples(stmt.get());
        const int cols = PQnfields(stmt.get());
        
        // Pre-allocate memory for results
        result.reserve(static_cast<size_t>(rows));
        
        // Process each row
        for (int i = 0; i < rows; ++i) {
            std::vector<std::string> row;
            row.reserve(static_cast<size_t>(cols));
            
            // Process each column
            for (int j = 0; j < cols; ++j) {
                if (PQgetisnull(stmt.get(), i, j)) {
                    row.emplace_back("NULL");
                } else {
                    const char* value = PQgetvalue(stmt.get(), i, j);
                    row.emplace_back(value ? value : "");
                }
            }
            
            result.push_back(std::move(row));
        }
        
        return result;
        
    } catch (const std::exception& e) {
        // Log the error and rethrow with additional context
        std::string queryStr = query.length() > 100 ?
            std::string(query.substr(0, 100)) + "..." :
            std::string(query);
        std::string error = std::format("Query execution failed: {} - {}",
            queryStr, e.what());
        utils::Logger::error(error);
        throw std::runtime_error(error);
    } catch (...) {
        utils::Logger::error("Unknown error during query execution");
        throw;
    }
}

void Connection::validateConnection() const {
    if (!isOpen_ || !connection_ ||
        PQstatus(static_cast<PGconn*>(connection_)) != CONNECTION_OK) {
        throw std::runtime_error("Database connection is not open or is in an invalid state");
    }
}

void Connection::executeQueryWithCallback(
    std::string_view query,
    std::function<bool(const std::vector<std::string>&)> callback,
    std::span<const std::string> params) {
    
    if (query.empty()) {
        throw std::invalid_argument("Query string cannot be empty");
    }
    
    if (!callback) {
        throw std::invalid_argument("Callback function cannot be null");
    }
    
    std::lock_guard<std::mutex> lock(mutex_);
    validateConnection();
    
    // Convert string_view to string for C API compatibility
    std::string queryStr(query);

    try {
        // Prepare and execute the statement
        std::unique_ptr<PGresult, decltype(&PQclear)> stmt(
            static_cast<PGresult*>(prepareStatement(queryStr, params)),
            PQclear
        );
        
        if (!stmt) {
            throw std::runtime_error("Failed to prepare statement");
        }
        
        // Check for execution errors
        ExecStatusType status = PQresultStatus(stmt.get());
        if (status != PGRES_TUPLES_OK && status != PGRES_COMMAND_OK) {
            throw std::runtime_error(
                std::format("Query execution failed: {}", 
                    PQresultErrorMessage(stmt.get()))
            );
        }
        
        // Get result set dimensions
        const int rows = PQntuples(stmt.get());
        const int cols = PQnfields(stmt.get());
        
        // Process each row and invoke callback
        for (int i = 0; i < rows; ++i) {
            std::vector<std::string> row;
            row.reserve(static_cast<size_t>(cols));
            
            // Process each column
            for (int j = 0; j < cols; ++j) {
                if (PQgetisnull(stmt.get(), i, j)) {
                    row.emplace_back("NULL");
                } else {
                    const char* value = PQgetvalue(stmt.get(), i, j);
                    row.emplace_back(value ? value : "");
                }
            }
            
            // Invoke callback and check if we should continue
            if (!callback(row)) {
                utils::Logger::debug("Query callback requested early termination");
                break;
            }
        }
    } catch (const std::exception& e) {
        std::string queryStr = query.length() > 100 ?
            std::string(query.substr(0, 100)) + "..." :
            std::string(query);
        std::string error = std::format("Query execution with callback failed: {} - {}",
            queryStr, e.what());
        utils::Logger::error(error);
        throw std::runtime_error(error);
    } catch (...) {
        utils::Logger::error("Unknown error during query execution with callback");
        throw;
    }
}

int Connection::executeNonQuery(std::string_view statement, std::span<const std::string> params) {
    if (statement.empty()) {
        throw std::invalid_argument("Statement string cannot be empty");
    }
    
    std::lock_guard<std::mutex> lock(mutex_);
    validateConnection();
    
    // Convert string_view to string for C API compatibility
    std::string stmtStr(statement);

    try {
        // Prepare and execute the statement
        std::unique_ptr<PGresult, decltype(&PQclear)> stmt(
            static_cast<PGresult*>(prepareStatement(stmtStr, params)),
            PQclear
        );
        
        if (!stmt) {
            throw std::runtime_error("Failed to prepare statement");
        }
        
        // Check for execution errors
        ExecStatusType status = PQresultStatus(stmt.get());
        if (status != PGRES_COMMAND_OK && status != PGRES_TUPLES_OK) {
            throw std::runtime_error(
                std::format("Statement execution failed: {}", 
                    PQresultErrorMessage(stmt.get()))
            );
        }
        
        // Get number of affected rows
        const char* affectedRows = PQcmdTuples(stmt.get());
        if (affectedRows && *affectedRows) {
            try {
                return std::stoi(affectedRows);
            } catch (const std::exception& e) {
                utils::Logger::warning(std::format("Failed to parse affected rows count: {}", e.what()));
                return 0;
            }
        }
        
        return 0; // No rows affected (e.g., DDL statements)
        
    } catch (const std::exception& e) {
        std::string stmtStr = statement.length() > 100 ?
            std::string(statement.substr(0, 100)) + "..." :
            std::string(statement);
        std::string error = std::format("Statement execution failed: {} - {}",
            stmtStr, e.what());
        utils::Logger::error(error);
        throw std::runtime_error(error);
    } catch (...) {
        utils::Logger::error("Unknown error during statement execution");
        throw;
    }
}

std::shared_ptr<Transaction> Connection::beginTransaction() {
    std::lock_guard<std::mutex> lock(mutex_);
    validateConnection();
    
    // Check if we're already in a transaction
    if (PQtransactionStatus(static_cast<PGconn*>(connection_)) != PQTRANS_IDLE) {
        throw std::runtime_error("Cannot begin transaction: already in a transaction");
    }

    try {
        // Start a new transaction
        std::unique_ptr<PGresult, decltype(&PQclear)> res(
            PQexec(static_cast<PGconn*>(connection_), "BEGIN"),
            PQclear
        );
        
        if (!res || PQresultStatus(res.get()) != PGRES_COMMAND_OK) {
            std::string error = "Failed to begin transaction";
            if (res) {
                error += ": " + std::string(PQresultErrorMessage(res.get()));
            }
            throw std::runtime_error(error);
        }
        
        // Create and return a new Transaction object
        return std::make_shared<Transaction>(shared_from_this());
        
    } catch (const std::exception& e) {
        std::string error = std::format("Failed to begin transaction: {}", e.what());
        utils::Logger::error(error);
        throw std::runtime_error(error);
    } catch (...) {
        utils::Logger::error("Unknown error beginning transaction");
        throw;
    }
}

void* Connection::prepareStatement(std::string_view statement, std::span<const std::string> params) {
    /**
     * Prepare a statement.
     *
     * @param statement The statement string.
     * @param params The statement parameters.
     *
     * @return A pointer to the prepared statement.
     *
     * @throws std::runtime_error If the statement preparation fails.
     */
    PGresult* result = nullptr;
    const std::string query(statement);

    try {
        if (params.empty()) {
            // Simple query without parameters
            result = PQexec(static_cast<PGconn*>(connection_), query.c_str());
        } else {
            // Parameterized query
            std::vector<const char*> paramValues;
            std::vector<int> paramLengths;
            std::vector<int> paramFormats;
            
            // Reserve space for parameters
            paramValues.reserve(params.size());
            paramLengths.reserve(params.size());
            paramFormats.reserve(params.size());

            // Prepare parameters
            for (const auto& param : params) {
                paramValues.push_back(param.c_str());
                paramLengths.push_back(static_cast<int>(param.length()));
                paramFormats.push_back(0); // 0 = text, 1 = binary
            }

            // Execute with parameters
            result = PQexecParams(
                static_cast<PGconn*>(connection_),
                query.c_str(),
                static_cast<int>(params.size()),
                nullptr, // param types (use DB default)
                paramValues.data(),
                paramLengths.data(),
                paramFormats.data(),
                0 // result format (0 = text, 1 = binary)
            );
        }

        // Check for errors
        if (!result) {
            throw std::runtime_error("Failed to allocate query result");
        }
        
        ExecStatusType status = PQresultStatus(result);
        if (status != PGRES_COMMAND_OK && status != PGRES_TUPLES_OK) {
            std::string error = PQresultErrorMessage(result);
            PQclear(result);
            throw std::runtime_error(error);
        }
        
        return result;
        
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Exception during statement preparation: {}", e.what()));
        return nullptr;
    }
}

} // namespace dbservice::core
