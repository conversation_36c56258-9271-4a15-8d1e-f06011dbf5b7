// auth_cert_sync_helper.cpp
// Enhanced certificate sync helper for auth-service
// Based on the proven cert_sync_helper system
// 
// COPY TO: D:\Coding_Projects\auth-service\cert_sync_helper_app\
// COMPILE: g++ -std=c++23 -o auth_cert_sync_helper auth_cert_sync_helper.cpp

#include <iostream>
#include <string>
#include <vector>
#include <array>
#include <memory>
#include <filesystem>
#include <stdexcept>
#include <cstring>
#include <cerrno>
#include <unistd.h>
#include <sys/types.h>
#include <pwd.h>
#include <grp.h>
#include <syslog.h>

// Path to the auth-service certificate sync script
constexpr const char* AUTH_SYNC_SCRIPT = "/opt/auth-service/scripts/sync-auth-certificates.sh";

// Server configuration structure
struct ServerConfig {
    std::string hostname;
    std::string user;
    std::string ssh_key;
    std::string cert_path;
    std::string service_name;
    std::string environment;
};

class AuthCertSyncHelper {
private:
    std::vector<ServerConfig> servers_;
    
    // Security check: ensure only authorized users can run this
    bool is_authorized_user() const {
        uid_t uid = getuid();
        
        // Allow root
        if (uid == 0) return true;
        
        // Check if user is auth-service or btaylor-admin
        struct passwd* pw = getpwuid(uid);
        if (pw) {
            std::string username(pw->pw_name);
            if (username == "auth-service" || username == "btaylor-admin") {
                return true;
            }
        }
        
        return false;
    }
    
    // Execute command and capture output
    std::string execute_command(const std::string& command) {
        std::array<char, 4096> buffer;
        std::string result;
        
        std::unique_ptr<FILE, decltype(&pclose)> pipe(
            popen(command.c_str(), "r"), pclose
        );
        
        if (!pipe) {
            throw std::runtime_error("popen() failed: " + std::string(strerror(errno)));
        }
        
        while (fgets(buffer.data(), buffer.size(), pipe.get()) != nullptr) {
            result += buffer.data();
        }
        
        return result;
    }
    
public:
    AuthCertSyncHelper() {
        // Initialize server configurations
        servers_.push_back({
            .hostname = "dev.chcit.org",
            .user = "btaylor-admin",
            .ssh_key = "/home/<USER>/.ssh/id_ed25519",
            .cert_path = "/etc/ssl/certs/auth-service",
            .service_name = "auth-service",
            .environment = "development"
        });
        
        servers_.push_back({
            .hostname = "git.chcit.org", 
            .user = "btaylor-admin",
            .ssh_key = "/home/<USER>/.ssh/id_ed25519",
            .cert_path = "/etc/ssl/certs/auth-service",
            .service_name = "auth-service",
            .environment = "production"
        });
    }
    
    // Log messages to syslog and stdout
    void log_message(const std::string& message, bool is_error = false) const {
        int priority = is_error ? LOG_ERR : LOG_INFO;
        syslog(priority, "%s", message.c_str());
        
        if (is_error) {
            std::cerr << "ERROR: " << message << std::endl;
        } else {
            std::cout << "INFO: " << message << std::endl;
        }
    }
    
    // Verify that the sync script exists and is executable
    bool verify_sync_script() const {
        if (!std::filesystem::exists(AUTH_SYNC_SCRIPT)) {
            log_message("Auth certificate sync script not found: " + std::string(AUTH_SYNC_SCRIPT), true);
            return false;
        }
        
        if (access(AUTH_SYNC_SCRIPT, X_OK) != 0) {
            log_message("Auth certificate sync script is not executable: " + std::string(AUTH_SYNC_SCRIPT), true);
            return false;
        }
        
        return true;
    }
    
    // Sync certificates to all configured auth-service servers
    bool sync_certificates() {
        log_message("Starting auth-service certificate synchronization");
        
        if (!verify_sync_script()) {
            return false;
        }
        
        try {
            // Execute the sync script
            std::string output = execute_command(AUTH_SYNC_SCRIPT);
            log_message("Certificate sync output: " + output);
            
            // Verify certificates on each server
            bool all_success = verify_all_certificates();
            
            if (all_success) {
                log_message("Auth-service certificate synchronization completed successfully");
            } else {
                log_message("Certificate synchronization completed with some errors", true);
            }
            
            return all_success;
            
        } catch (const std::exception& e) {
            log_message("Certificate sync failed: " + std::string(e.what()), true);
            return false;
        }
    }
    
    // Verify certificates are properly installed on a server
    bool verify_server_certificates(const ServerConfig& server) const {
        log_message("Verifying certificates on " + server.hostname + " (" + server.environment + ")");
        
        try {
            // Check if certificate files exist
            std::string verify_cmd = "ssh -i " + server.ssh_key + " " + 
                                   server.user + "@" + server.hostname + " " +
                                   "'test -f " + server.cert_path + "/cert.pem && " +
                                   "test -f " + server.cert_path + "/privkey.pem && " +
                                   "test -f " + server.cert_path + "/chain.pem'";
            
            std::string output = execute_command(verify_cmd);
            
            // Check certificate expiry
            std::string expiry_cmd = "ssh -i " + server.ssh_key + " " +
                                   server.user + "@" + server.hostname + " " +
                                   "'openssl x509 -in " + server.cert_path + "/cert.pem -noout -enddate'";
            
            std::string expiry_output = execute_command(expiry_cmd);
            log_message("Certificate expiry for " + server.hostname + ": " + expiry_output);
            
            return true;
            
        } catch (const std::exception& e) {
            log_message("Certificate verification failed for " + server.hostname + ": " + std::string(e.what()), true);
            return false;
        }
    }
    
    // Verify certificates on all servers
    bool verify_all_certificates() const {
        bool all_success = true;
        for (const auto& server : servers_) {
            if (!verify_server_certificates(server)) {
                all_success = false;
            }
        }
        return all_success;
    }
    
    // Test HTTPS connectivity to auth-service endpoints
    bool test_https_endpoints() const {
        log_message("Testing HTTPS endpoints for auth-service");
        
        bool all_success = true;
        
        for (const auto& server : servers_) {
            try {
                std::string subdomain = (server.environment == "development") ? "auth-dev" : "auth";
                std::string url = "https://" + subdomain + ".chcit.org:8082/health";
                
                std::string test_cmd = "curl -s -o /dev/null -w '%{http_code}' --connect-timeout 10 " + url;
                std::string response = execute_command(test_cmd);
                
                if (response.find("200") != std::string::npos || response.find("404") != std::string::npos) {
                    log_message("HTTPS endpoint test successful for " + url);
                } else {
                    log_message("HTTPS endpoint test failed for " + url + " (response: " + response + ")", true);
                    all_success = false;
                }
                
            } catch (const std::exception& e) {
                log_message("HTTPS endpoint test error for " + server.hostname + ": " + std::string(e.what()), true);
                all_success = false;
            }
        }
        
        return all_success;
    }
};

int main(int argc, char* argv[]) {
    try {
        // Set up syslog
        openlog("auth_cert_sync_helper", LOG_PID | LOG_PERROR, LOG_DAEMON);
        
        AuthCertSyncHelper helper;
        
        // Security check
        if (!helper.is_authorized_user()) {
            helper.log_message("Access denied: Only authorized users can run this helper", true);
            return 1;
        }
        
        // Parse command line arguments
        std::string action = "sync";
        if (argc > 1) {
            action = argv[1];
        }
        
        bool success = false;
        
        if (action == "sync") {
            success = helper.sync_certificates();
        } else if (action == "verify") {
            success = helper.verify_all_certificates();
        } else if (action == "test") {
            success = helper.test_https_endpoints();
        } else {
            helper.log_message("Usage: " + std::string(argv[0]) + " [sync|verify|test]", true);
            return 1;
        }
        
        closelog();
        return success ? 0 : 1;
        
    } catch (const std::exception& e) {
        syslog(LOG_ERR, "Fatal error: %s", e.what());
        std::cerr << "Fatal error: " << e.what() << std::endl;
        closelog();
        return 1;
    }
}
