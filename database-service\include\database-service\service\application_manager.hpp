#pragma once

#include <string>
#include <vector>
#include <memory>
#include <expected>
#include <chrono>
#include <nlohmann/json.hpp>

namespace dbservice::core {
    class ConnectionManager;
}

namespace dbservice::service {

// Error types for application management
enum class ApplicationError {
    INVALID_NAME,
    INVALID_DESCRIPTION,
    APPLICATION_NOT_FOUND,
    APPLICATION_ALREADY_EXISTS,
    API_KEY_GENERATION_FAILED,
    API_KEY_INVALID,
    DATABASE_ERROR,
    PERMISSION_DENIED,
    INVALID_PARAMETERS
};

// Application data structure
struct Application {
    int id;
    std::string name;
    std::string description;
    std::string apiKey;
    std::chrono::system_clock::time_point createdAt;
    std::chrono::system_clock::time_point updatedAt;
    bool active;
    nlohmann::json metadata;
};

// Application registration request
struct ApplicationRequest {
    std::string name;
    std::string description;
    nlohmann::json metadata;
};

// Application-to-database mapping
struct ApplicationDatabase {
    int applicationId;
    int databaseInstanceId;
    std::string schemaName;
    std::chrono::system_clock::time_point createdAt;
    bool active;
};

/**
 * @brief Manages application registration, API keys, and application-to-database mappings
 * 
 * The ApplicationManager is responsible for:
 * - Registering client applications
 * - Generating and validating API keys
 * - Managing application metadata
 * - Linking applications to database instances
 * - Tracking application usage and permissions
 */
class ApplicationManager {
public:
    /**
     * @brief Constructor
     * @param connectionManager Database connection manager
     */
    explicit ApplicationManager(std::shared_ptr<dbservice::core::ConnectionManager> connectionManager);

    /**
     * @brief Destructor
     */
    ~ApplicationManager() = default;

    // Disable copy constructor and assignment operator
    ApplicationManager(const ApplicationManager&) = delete;
    ApplicationManager& operator=(const ApplicationManager&) = delete;

    /**
     * @brief Initialize the application manager
     * @return true if initialization successful, false otherwise
     */
    bool initialize();

    /**
     * @brief Register a new application
     * @param request Application registration request
     * @return Application ID on success, error on failure
     */
    std::expected<int, ApplicationError> registerApplication(const ApplicationRequest& request);

    /**
     * @brief Generate API key for an application
     * @param applicationId Application ID
     * @return API key on success, error on failure
     */
    std::expected<std::string, ApplicationError> generateApiKey(int applicationId);

    /**
     * @brief Validate an API key and return application ID
     * @param apiKey API key to validate
     * @return Application ID on success, error on failure
     */
    std::expected<int, ApplicationError> validateApiKey(const std::string& apiKey);

    /**
     * @brief Get application by ID
     * @param applicationId Application ID
     * @return Application data on success, error on failure
     */
    std::expected<Application, ApplicationError> getApplication(int applicationId);

    /**
     * @brief Get application by name
     * @param name Application name
     * @return Application data on success, error on failure
     */
    std::expected<Application, ApplicationError> getApplicationByName(const std::string& name);

    /**
     * @brief List all applications
     * @return Vector of applications on success, error on failure
     */
    std::expected<std::vector<Application>, ApplicationError> listApplications();

    /**
     * @brief Update application metadata
     * @param applicationId Application ID
     * @param metadata New metadata
     * @return void on success, error on failure
     */
    std::expected<void, ApplicationError> updateApplicationMetadata(int applicationId, const nlohmann::json& metadata);

    /**
     * @brief Deactivate an application
     * @param applicationId Application ID
     * @return void on success, error on failure
     */
    std::expected<void, ApplicationError> deactivateApplication(int applicationId);

    /**
     * @brief Activate an application
     * @param applicationId Application ID
     * @return void on success, error on failure
     */
    std::expected<void, ApplicationError> activateApplication(int applicationId);

    /**
     * @brief Link application to database instance
     * @param applicationId Application ID
     * @param databaseInstanceId Database instance ID
     * @param schemaName Schema name for the application
     * @return void on success, error on failure
     */
    std::expected<void, ApplicationError> linkToDatabase(int applicationId, int databaseInstanceId, const std::string& schemaName);

    /**
     * @brief Unlink application from database instance
     * @param applicationId Application ID
     * @param databaseInstanceId Database instance ID
     * @return void on success, error on failure
     */
    std::expected<void, ApplicationError> unlinkFromDatabase(int applicationId, int databaseInstanceId);

    /**
     * @brief Get database mappings for an application
     * @param applicationId Application ID
     * @return Vector of database mappings on success, error on failure
     */
    std::expected<std::vector<ApplicationDatabase>, ApplicationError> getApplicationDatabases(int applicationId);

    /**
     * @brief Get applications linked to a database instance
     * @param databaseInstanceId Database instance ID
     * @return Vector of applications on success, error on failure
     */
    std::expected<std::vector<Application>, ApplicationError> getApplicationsByDatabase(int databaseInstanceId);

private:
    std::shared_ptr<dbservice::core::ConnectionManager> connectionManager_;
    bool initialized_;

    /**
     * @brief Generate a secure API key
     * @return Generated API key
     */
    std::string generateSecureApiKey();

    /**
     * @brief Validate application name
     * @param name Application name
     * @return true if valid, false otherwise
     */
    bool isValidApplicationName(const std::string& name);

    /**
     * @brief Check if application name already exists
     * @param name Application name
     * @return true if exists, false otherwise
     */
    bool applicationNameExists(const std::string& name);

    /**
     * @brief Convert database row to Application object
     * @param row Database row data
     * @return Application object
     */
    Application rowToApplication(const std::vector<std::string>& row);

    /**
     * @brief Convert database row to ApplicationDatabase object
     * @param row Database row data
     * @return ApplicationDatabase object
     */
    ApplicationDatabase rowToApplicationDatabase(const std::vector<std::string>& row);
};

} // namespace dbservice::service
