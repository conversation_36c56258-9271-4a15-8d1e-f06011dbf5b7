# Initialize All Databases Module
# Creates all application databases for the Database Service ecosystem

# Import common module
Import-Module -Name (Join-Path $PSScriptRoot '..\Common.psm1') -Force
# Import Logger module
Import-Module -Name (Join-Path $PSScriptRoot 'Logger/Logger.psm1') -Force

function Initialize-AllDatabases {
    Clear-Host
    # Enable UI Mode for menu display
    Enable-UIMode
    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message "               Initialize All Databases               " -Level "UI" -ForegroundColor Blue
    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message " " -Level "UI"
    # Disable UI Mode after menu display
    Disable-UIMode

    if ($null -eq $Config -or $null -eq $Config.ssh) {
        Write-Log -Message "SSH configuration is not set up. Please configure SSH settings first." -Level "Error" -Component "Database"
        Wait-ForUser
        & "$PSScriptRoot\Set-Environment.ps1"
        return
    }

    # Check if PostgreSQL is installed and running
    $checkPgCmd = "systemctl is-active postgresql"
    $pgStatus = Invoke-RemoteCommand -Command $checkPgCmd -Silent
    if ($pgStatus -ne "active") {
        Write-Log -Message "PostgreSQL is not running." -Level "Error" -Component "Database"
        Write-Log -Message "Please install and start PostgreSQL first." -Level "Warning" -Component "Database"
        Wait-ForUser
        & "$PSScriptRoot\Install-Dependencies.ps1"
        return
    }

    # Get database configuration
    $dbUser = if ($Config.database.user) { $Config.database.user } else { "database_service_user" }
    $dbPassword = $Config.database.password

    if ([string]::IsNullOrWhiteSpace($dbPassword)) {
        Write-Log -Message "Database password is not set." -Level "Error" -Component "Database"
        $password = Read-Host "Enter database password" -AsSecureString
        $plainPassword = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($password))
        if ([string]::IsNullOrWhiteSpace($plainPassword)) {
            Write-Log -Message "Password cannot be empty." -Level "Error" -Component "Database"
            Wait-ForUser
            Show-MainMenu
            return
        }
        $Config.database.password = $plainPassword
        Save-Configuration
        $dbPassword = $plainPassword
    }

    # Define all databases to create
    $databases = @(
        @{
            Name = "database_service_prod"
            Description = "Database Service metadata database"
            SchemaFiles = @("database_service_schema.sql")
        },
        @{
            Name = "git_dashboard"
            Description = "Git Dashboard application database"
            SchemaFiles = @("initial_schema.sql", "git_repo_schema.sql")
        },
        @{
            Name = "logging_service"
            Description = "Logging Service database"
            SchemaFiles = @("logging_schema.sql")
        }
    )

    Write-Log -Message "Creating all application databases..." -Level "Info" -Component "Database"
    Write-Log -Message "Database user: $dbUser" -Level "Info" -Component "Database"

    # Ensure database user exists
    $checkUserCmd = @"
sudo -u postgres psql -tAc "SELECT 1 FROM pg_roles WHERE rolname='$dbUser'" | wc -l
"@
    $userExists = Invoke-RemoteCommand -Command $checkUserCmd -Silent
    if ($userExists -eq "0") {
        Write-Log -Message "Creating database user $dbUser..." -Level "Info" -Component "Database"
        $createUserCmd = @"
sudo -u postgres psql -c "CREATE USER $dbUser WITH PASSWORD '$dbPassword'" 2>&1
"@
        $result = Invoke-RemoteCommand -Command $createUserCmd
        if ($result -match "ERROR") {
            Write-Log -Message "Failed to create database user: $result" -Level "Error" -Component "Database"
            Wait-ForUser
            Show-MainMenu
            return
        } else {
            Write-Log -Message "Database user $dbUser created successfully!" -Level "Success" -Component "Database"
        }
    } else {
        Write-Log -Message "Database user $dbUser already exists." -Level "Info" -Component "Database"
        # Update the user's password
        $updatePasswordCmd = @"
sudo -u postgres psql -c "ALTER USER $dbUser WITH PASSWORD '$dbPassword'" 2>&1
"@
        $result = Invoke-RemoteCommand -Command $updatePasswordCmd
        if ($result -match "ERROR") {
            Write-Log -Message "Failed to update user password: $result" -Level "Error" -Component "Database"
        } else {
            Write-Log -Message "Updated password for user $dbUser." -Level "Success" -Component "Database"
        }
    }

    $installDir = $Config.service.install_dir
    if ([string]::IsNullOrEmpty($installDir)) {
        $installDir = "/opt/database-service"
    }

    # Create each database
    foreach ($db in $databases) {
        $dbName = $db.Name
        $description = $db.Description
        $schemaFiles = $db.SchemaFiles

        Write-Log -Message " " -Level "Info" -Component "Database"
        Write-Log -Message "Creating database: $dbName" -Level "Info" -Component "Database"
        Write-Log -Message "Description: $description" -Level "Info" -Component "Database"

        # Check if the database exists
        $checkDbCmd = @"
sudo -u postgres psql -tAc "SELECT 1 FROM pg_database WHERE datname='$dbName'" | wc -l
"@
        $dbExists = Invoke-RemoteCommand -Command $checkDbCmd -Silent
        if ($dbExists -eq "0") {
            Write-Log -Message "Creating database $dbName..." -Level "Info" -Component "Database"
            $createDbCmd = @"
sudo -u postgres psql -c "CREATE DATABASE $dbName OWNER $dbUser" 2>&1
"@
            $result = Invoke-RemoteCommand -Command $createDbCmd
            if ($result -match "ERROR") {
                Write-Log -Message "Failed to create database $dbName: $result" -Level "Error" -Component "Database"
                continue
            } else {
                Write-Log -Message "Database $dbName created successfully!" -Level "Success" -Component "Database"
            }
        } else {
            Write-Log -Message "Database $dbName already exists." -Level "Info" -Component "Database"
        }

        # Apply schema files
        foreach ($schemaFileName in $schemaFiles) {
            $schemaFile = "$installDir/schemas/$schemaFileName"
            Write-Log -Message "Applying schema file: $schemaFileName to $dbName" -Level "Info" -Component "Database"

            $runSchemaCmd = @"
if [ -f $schemaFile ]; then
    echo "Running schema file: $schemaFile"
    sudo -u postgres psql -d $dbName -f $schemaFile 2>&1
else
    echo "Schema file not found: $schemaFile"
fi
"@
            $result = Invoke-RemoteCommand -Command $runSchemaCmd
            if ($result -match "ERROR") {
                Write-Log -Message "Failed to apply ${schemaFileName} to ${dbName}: $result" -Level "Error" -Component "Database"
            } else {
                Write-Log -Message "Successfully applied $schemaFileName to $dbName" -Level "Success" -Component "Database"
            }
        }

        # Grant permissions to database user
        Write-Log -Message "Granting permissions to $dbUser on $dbName..." -Level "Info" -Component "Database"
        $grantPermissionsCmd = @"
sudo -u postgres psql -d $dbName -c "GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO $dbUser;" 2>&1 &&
sudo -u postgres psql -d $dbName -c "GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO $dbUser;" 2>&1 &&
sudo -u postgres psql -d $dbName -c "ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO $dbUser;" 2>&1 &&
sudo -u postgres psql -d $dbName -c "ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO $dbUser;" 2>&1
"@
        $permResult = Invoke-RemoteCommand -Command $grantPermissionsCmd
        if ($permResult -match "ERROR") {
            Write-Log -Message "Failed to grant permissions on ${dbName}: $permResult" -Level "Error" -Component "Database"
        } else {
            Write-Log -Message "Database permissions granted successfully on $dbName!" -Level "Success" -Component "Database"
        }
    }

    Write-Log -Message " " -Level "Info" -Component "Database"
    Write-Log -Message "All databases initialization completed!" -Level "Success" -Component "Database"
    Write-Log -Message "Created databases:" -Level "Info" -Component "Database"
    foreach ($db in $databases) {
        Write-Log -Message "  - $($db.Name): $($db.Description)" -Level "Info" -Component "Database"
    }
    
    Wait-ForUser
}

Export-ModuleMember -Function Initialize-AllDatabases
