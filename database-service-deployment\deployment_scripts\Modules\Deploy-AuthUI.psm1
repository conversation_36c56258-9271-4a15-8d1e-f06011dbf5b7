# Deploy-AuthUI.psm1 - Module for deploying the auth service UI

<#
.SYNOPSIS
    Provides functionality for deploying the auth service React/TypeScript UI.

.DESCRIPTION
    This module handles the deployment process for the auth service UI,
    including file transfer, nginx configuration, and service setup.

.NOTES
    File Name      : Deploy-AuthUI.psm1
    Author         : Auth Service Deployment Team
    Prerequisite   : PowerShell 5.1 or later
    Copyright      : (c) 2025 Augment
#>

function Deploy-AuthUI {
    [CmdletBinding()]
    param()

    Write-Host "=== Deploying Auth Service UI ===" -ForegroundColor Cyan
    Write-Host ""

    # Check if configuration is loaded
    if ($null -eq $script:Config) {
        Write-Host "❌ Configuration is not loaded. Please load configuration first." -ForegroundColor Red
        return $false
    }

    # UI source and build directories
    $uiSourceDir = "D:\Coding_Projects\auth-service\auth-service-ui"
    $buildDir = Join-Path $uiSourceDir "build"
    $remoteUIDir = "/opt/auth-service-ui"
    
    Write-Host "UI Source Directory: $uiSourceDir" -ForegroundColor Yellow
    Write-Host "Build Directory: $buildDir" -ForegroundColor Yellow
    Write-Host "Remote UI Directory: $remoteUIDir" -ForegroundColor Yellow
    Write-Host ""

    # Check if build directory exists
    if (-not (Test-Path $buildDir)) {
        Write-Host "❌ Build directory not found: $buildDir" -ForegroundColor Red
        Write-Host "Please run menu option 18 (Build UI) first." -ForegroundColor Yellow
        return $false
    }

    Write-Host "✅ Build directory found" -ForegroundColor Green

    # Create remote UI directory
    Write-Host ""
    Write-Host "Creating remote UI directory..." -ForegroundColor Yellow
    
    try {
        $createDirCmd = "sudo mkdir -p $remoteUIDir && sudo chown -R www-data:www-data $remoteUIDir"
        $sshCommand = "ssh -i `"$($script:Config.ssh.key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$($script:Config.ssh.host) `"$createDirCmd`""
        $result = Invoke-Expression $sshCommand
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Remote UI directory created" -ForegroundColor Green
        } else {
            Write-Host "❌ Failed to create remote UI directory" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ Error creating remote directory: $_" -ForegroundColor Red
        return $false
    }

    # Transfer UI files to server
    Write-Host ""
    Write-Host "Transferring UI files to server..." -ForegroundColor Yellow
    
    try {
        # Use SCP to transfer the build directory contents
        $scpCommand = "scp -r -i `"$($script:Config.ssh.key_path)`" -o StrictHostKeyChecking=no `"$buildDir\*`" $($script:Config.ssh.username)@$($script:Config.ssh.host):$remoteUIDir/"
        
        Write-Host "Executing: $scpCommand" -ForegroundColor Gray
        $scpResult = Invoke-Expression $scpCommand
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ UI files transferred successfully" -ForegroundColor Green
        } else {
            Write-Host "❌ Failed to transfer UI files" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ Error transferring files: $_" -ForegroundColor Red
        return $false
    }

    # Set proper permissions
    Write-Host ""
    Write-Host "Setting file permissions..." -ForegroundColor Yellow
    
    try {
        $permissionsCmd = "sudo chown -R www-data:www-data $remoteUIDir && sudo chmod -R 755 $remoteUIDir"
        $sshCommand = "ssh -i `"$($script:Config.ssh.key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$($script:Config.ssh.host) `"$permissionsCmd`""
        $result = Invoke-Expression $sshCommand
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ File permissions set correctly" -ForegroundColor Green
        } else {
            Write-Host "❌ Failed to set file permissions" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ Error setting permissions: $_" -ForegroundColor Red
        return $false
    }

    # Create nginx configuration
    Write-Host ""
    Write-Host "Creating nginx configuration..." -ForegroundColor Yellow
    
    $nginxConfig = @"
server {
    listen 80;
    server_name auth.$($script:Config.ssh.host);
    
    root $remoteUIDir;
    index index.html;
    
    location / {
        try_files \$uri \$uri/ /index.html;
    }
    
    location /api/ {
        proxy_pass http://localhost:8082/;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
    
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
"@

    try {
        $nginxConfigCmd = "sudo tee /etc/nginx/sites-available/auth-service > /dev/null << 'EOF'`n$nginxConfig`nEOF"
        $sshCommand = "ssh -i `"$($script:Config.ssh.key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$($script:Config.ssh.host) `"$nginxConfigCmd`""
        $result = Invoke-Expression $sshCommand
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Nginx configuration created" -ForegroundColor Green
        } else {
            Write-Host "❌ Failed to create nginx configuration" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ Error creating nginx configuration: $_" -ForegroundColor Red
        return $false
    }

    # Enable nginx site
    Write-Host ""
    Write-Host "Enabling nginx site..." -ForegroundColor Yellow
    
    try {
        $enableSiteCmd = "sudo ln -sf /etc/nginx/sites-available/auth-service /etc/nginx/sites-enabled/ && sudo nginx -t && sudo systemctl reload nginx"
        $sshCommand = "ssh -i `"$($script:Config.ssh.key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$($script:Config.ssh.host) `"$enableSiteCmd`""
        $result = Invoke-Expression $sshCommand
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Nginx site enabled and reloaded" -ForegroundColor Green
        } else {
            Write-Host "❌ Failed to enable nginx site" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ Error enabling nginx site: $_" -ForegroundColor Red
        return $false
    }

    Write-Host ""
    Write-Host "✅ Auth service UI deployment completed successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "UI Access Information:" -ForegroundColor Cyan
    Write-Host "- URL: http://auth.$($script:Config.ssh.host)" -ForegroundColor White
    Write-Host "- Directory: $remoteUIDir" -ForegroundColor White
    Write-Host "- API Proxy: /api/ -> http://localhost:8082/" -ForegroundColor White
    
    return $true
}

# Export the function
Export-ModuleMember -Function Deploy-AuthUI
