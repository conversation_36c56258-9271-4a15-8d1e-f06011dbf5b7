# Step 1: Database Schema Creation

**Phase**: 3 - OAuth 2.0 Core Implementation  
**Step**: 1 of 8  
**Goal**: Create OAuth 2.0 database schema  
**Risk Level**: Low (no code changes)  
**Estimated Time**: 30 minutes  

---

## 🎯 **Objective**

Create the database schema for OAuth 2.0 functionality without modifying any existing C++ code. This allows us to:
- Test database connectivity
- Verify schema creation
- Establish OAuth 2.0 data structure
- Prepare for code implementation

---

## 📋 **What We're Creating**

### **Database Tables for OAuth 2.0**

#### **1. Users Table**
- Store user accounts with secure password hashes
- Support user registration and authentication
- Track user status and metadata

#### **2. OAuth Tokens Table**
- Store JWT access and refresh tokens
- Track token expiration and revocation
- Support token refresh flow

#### **3. OAuth Clients Table** (Future)
- Support multiple client applications
- Client credentials and permissions
- OAuth 2.0 client management

#### **4. <PERSON><PERSON><PERSON> Scopes Table** (Future)
- Define permission scopes
- Support fine-grained access control
- OAuth 2.0 scope management

---

## 🔧 **Implementation Plan**

### **File to Create**
- **`auth_schema.sql`** - Complete OAuth 2.0 database schema

### **Location**
- **Path**: `D:\Coding_Projects\auth-service\auth-service-app\database\auth_schema.sql`
- **Directory**: Create `database` folder in auth-service-app

### **Schema Components**

#### **Core Tables (Step 1)**
1. **`auth_users`** - User accounts
2. **`auth_tokens`** - JWT tokens
3. **`auth_sessions`** - User sessions

#### **Future Tables (Later Steps)**
4. **`auth_clients`** - OAuth 2.0 clients
5. **`auth_scopes`** - Permission scopes
6. **`auth_client_scopes`** - Client-scope relationships

---

## 📝 **Database Schema Design**

### **auth_users Table**
```sql
- user_id (UUID, Primary Key)
- username (VARCHAR, Unique)
- email (VARCHAR, Unique)
- password_hash (VARCHAR) -- Argon2id hash
- salt (VARCHAR) -- Password salt
- created_at (TIMESTAMP)
- updated_at (TIMESTAMP)
- last_login (TIMESTAMP)
- is_active (BOOLEAN)
- email_verified (BOOLEAN)
```

### **auth_tokens Table**
```sql
- token_id (UUID, Primary Key)
- user_id (UUID, Foreign Key)
- token_type (VARCHAR) -- 'access' or 'refresh'
- token_hash (VARCHAR) -- SHA256 of token
- expires_at (TIMESTAMP)
- created_at (TIMESTAMP)
- revoked_at (TIMESTAMP)
- is_revoked (BOOLEAN)
```

### **auth_sessions Table**
```sql
- session_id (UUID, Primary Key)
- user_id (UUID, Foreign Key)
- access_token_id (UUID, Foreign Key)
- refresh_token_id (UUID, Foreign Key)
- ip_address (INET)
- user_agent (TEXT)
- created_at (TIMESTAMP)
- last_activity (TIMESTAMP)
- expires_at (TIMESTAMP)
- is_active (BOOLEAN)
```

---

## 🧪 **Testing Plan**

### **Step 1: Create Schema File**
1. Create database directory
2. Create auth_schema.sql file
3. Verify SQL syntax

### **Step 2: Deploy Schema**
1. Connect to PostgreSQL on dev.chcit.org
2. Create auth_service database (if not exists)
3. Execute schema creation
4. Verify tables created

### **Step 3: Test Database Connectivity**
1. Test connection from auth-service app
2. Verify table access
3. Test basic queries

---

## 📋 **Deployment Commands**

### **Create Schema on Server**
```bash
# SSH to dev.chcit.org
ssh <EMAIL>

# Connect to PostgreSQL
sudo -u postgres psql

# Create database (if not exists)
CREATE DATABASE auth_service;
\c auth_service;

# Execute schema file
\i /path/to/auth_schema.sql

# Verify tables
\dt
\d auth_users
\d auth_tokens
\d auth_sessions
```

### **Test Connectivity**
```bash
# Test from auth-service build directory
cd /home/<USER>/auth-service-build/build
./auth-service --config ../config/auth-service.conf --port 8082
```

---

## ✅ **Success Criteria**

### **Step 1 Complete When**:
- ✅ `auth_schema.sql` file created
- ✅ Database schema deployed successfully
- ✅ All tables created with correct structure
- ✅ Database connectivity verified
- ✅ No compilation errors (no code changes)
- ✅ Existing functionality still works

---

## 🚀 **Next Steps After Step 1**

### **Step 2: Configuration Enhancement**
- Add OAuth 2.0 configuration settings
- Update config_manager.hpp and .cpp
- Test configuration loading

### **Preparation for Step 2**
- Database schema will be ready
- Configuration can reference database tables
- Code changes will be minimal and isolated

---

## 📝 **Notes**

### **Why Start with Database Schema**
1. **No Code Changes**: Zero risk of compilation issues
2. **Foundation First**: Establishes data structure
3. **Easy Testing**: Can verify database independently
4. **Rollback Simple**: Easy to drop tables if needed

### **Security Considerations**
- Password hashes use Argon2id (implemented in Step 3)
- Token storage uses secure hashing
- Session management with IP tracking
- Proper foreign key relationships

### **PostgreSQL 17 Features**
- UUID support for secure identifiers
- INET type for IP address storage
- Timestamp with timezone support
- Proper indexing for performance

---

## 🎯 **Ready to Begin**

**All prerequisites met for Step 1:**
- ✅ PostgreSQL 17 running on dev.chcit.org
- ✅ Database connectivity verified
- ✅ No code changes required
- ✅ Low risk, high value step

**Next Action**: Create auth_schema.sql file 🚀
