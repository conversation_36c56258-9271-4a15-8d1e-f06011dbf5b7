# Initialize-AuthDatabase.psm1 - Module for initializing auth service database

<#
.SYNOPSIS
    Provides functionality for initializing the auth service database.

.DESCRIPTION
    This module handles the database initialization process for the auth service,
    including database creation, user setup, and schema initialization.

.NOTES
    File Name      : Initialize-AuthDatabase.psm1
    Author         : Auth Service Deployment Team
    Prerequisite   : PowerShell 5.1 or later
    Copyright      : (c) 2025 Augment
#>

# Import required modules
try {
    # Import common module
    if (-not (Get-Module -Name "Common")) {
        Import-Module -Name "$PSScriptRoot\..\Common.psm1" -Force -ErrorAction Stop
    }

    # Import Logger module
    if (-not (Get-Module -Name "Logger")) {
        Import-Module -Name "$PSScriptRoot\Logger\Logger.psm1" -Force -ErrorAction Stop
    }

    # Import SSHManager module
    if (-not (Get-Module -Name "SSHManager")) {
        Import-Module -Name "$PSScriptRoot\SSHManager.psm1" -Force -ErrorAction Stop
    }
} catch {
    Write-Host "Error loading required modules: $_" -ForegroundColor Red
}

function Initialize-AuthDatabase {
    [CmdletBinding()]
    param()

    Write-Host "=== Initializing Auth Service Database ===" -ForegroundColor Cyan
    Write-Host ""

    # Get database configuration
    $dbConfig = $script:Config.database
    $dbHost = $dbConfig.host
    $dbPort = $dbConfig.port
    $dbName = $dbConfig.name
    $dbUser = $dbConfig.user
    $dbPassword = $dbConfig.password

    Write-Host "Database Configuration:" -ForegroundColor Yellow
    Write-Host "  Host: $dbHost" -ForegroundColor White
    Write-Host "  Port: $dbPort" -ForegroundColor White
    Write-Host "  Database: $dbName" -ForegroundColor White
    Write-Host "  User: $dbUser" -ForegroundColor White
    Write-Host ""

    # Test database connection
    Write-Host "Testing database connection..." -ForegroundColor Yellow
    
    $testConnectionCmd = "PGPASSWORD='$dbPassword' psql -h $dbHost -p $dbPort -U $dbUser -d postgres -c 'SELECT version();'"
    
    try {
        $sshCommand = "ssh -i `"$($script:Config.ssh.key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$($script:Config.ssh.host) `"$testConnectionCmd`""
        $result = Invoke-Expression $sshCommand
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Database connection successful" -ForegroundColor Green
        } else {
            Write-Host "❌ Database connection failed" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ Error testing database connection: $_" -ForegroundColor Red
        return $false
    }

    # Create database if it doesn't exist
    Write-Host "Creating auth service database..." -ForegroundColor Yellow
    
    $createDbCmd = "PGPASSWORD='$dbPassword' psql -h $dbHost -p $dbPort -U postgres -c `"CREATE DATABASE $dbName OWNER $dbUser;`""
    
    try {
        $sshCommand = "ssh -i `"$($script:Config.ssh.key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$($script:Config.ssh.host) `"$createDbCmd`""
        $result = Invoke-Expression $sshCommand
        
        if ($LASTEXITCODE -eq 0 -or $result -like "*already exists*") {
            Write-Host "✅ Database created or already exists" -ForegroundColor Green
        } else {
            Write-Host "❌ Failed to create database" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ Error creating database: $_" -ForegroundColor Red
        return $false
    }

    # Create auth service tables
    Write-Host "Creating auth service tables..." -ForegroundColor Yellow
    
    $createTablesCmd = @"
PGPASSWORD='$dbPassword' psql -h $dbHost -p $dbPort -U $dbUser -d $dbName -c "
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(255) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    salt VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP
);

CREATE TABLE IF NOT EXISTS user_sessions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS user_roles (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    role_name VARCHAR(100) NOT NULL,
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_sessions_token ON user_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON user_sessions(user_id);
"
"@

    try {
        $sshCommand = "ssh -i `"$($script:Config.ssh.key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$($script:Config.ssh.host) `"$createTablesCmd`""
        $result = Invoke-Expression $sshCommand
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Auth service tables created successfully" -ForegroundColor Green
        } else {
            Write-Host "❌ Failed to create auth service tables" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ Error creating tables: $_" -ForegroundColor Red
        return $false
    }

    Write-Host ""
    Write-Host "✅ Auth service database initialization completed successfully!" -ForegroundColor Green
    return $true
}

# Export the function
Export-ModuleMember -Function Initialize-AuthDatabase
