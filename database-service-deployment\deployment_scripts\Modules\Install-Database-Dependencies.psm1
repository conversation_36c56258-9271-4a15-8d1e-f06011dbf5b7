# Install-Dependencies Module - Enhanced PostgreSQL Detection
Import-Module -Name (Join-Path $PSScriptRoot '..\Common.psm1') -Force
Import-Module -Name (Join-Path $PSScriptRoot 'Logger/Logger.psm1') -Force
Import-Module -Name "$PSScriptRoot\UI.psm1" -Force

function Install-Database-Dependencies {
    Write-Log -Message "Starting PostgreSQL configuration analysis..." -Level "UI" -ForegroundColor Cyan
    Show-MenuDivider

    # Load configuration if needed
    if ($null -eq $script:Config) {
        $configPath = Join-Path -Path $PSScriptRoot -ChildPath "..\config\database-service-development.json"
        try {
            $configContent = Get-Content -Path $configPath -Raw -ErrorAction Stop
            $script:Config = ConvertFrom-Json -InputObject $configContent -ErrorAction Stop
            Write-Log -Message "Configuration loaded successfully." -Level "UI" -ForegroundColor Green
        } catch {
            Write-Log -Message "Failed to load configuration: $_" -Level "UI" -ForegroundColor Red
            return
        }
    }

    # Check SSH configuration
    if (-not ($script:Config.ssh -and $script:Config.ssh.host -and $script:Config.ssh.username)) {
        Write-Log -Message "SSH configuration not found. Cannot proceed." -Level "UI" -ForegroundColor Red
        return
    }

    # Get SSH key path
    $keyPath = $script:Config.ssh.key_path
    if (-not $keyPath -and $script:Config.ssh.local_key_path) {
        $keyPath = $script:Config.ssh.local_key_path
    }

    Write-Log -Message "SSH configuration: $($script:Config.ssh.username)@$($script:Config.ssh.host)" -Level "UI" -ForegroundColor Green

    # Test SSH connection
    Write-Log -Message "Testing SSH connection..." -Level "UI" -ForegroundColor Yellow
    $testArgs = @(
        "-i", "$keyPath",
        "-p", "$($script:Config.ssh.port)",
        "-o", "BatchMode=yes",
        "-o", "StrictHostKeyChecking=accept-new",
        "-o", "ConnectTimeout=10",
        "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
        "echo 'CONNECTION_OK'"
    )

    $output = & ssh @testArgs 2>&1
    if ($LASTEXITCODE -ne 0 -or $output -notlike "*CONNECTION_OK*") {
        Write-Log -Message "SSH connection failed. Cannot proceed." -Level "UI" -ForegroundColor Red
        return
    }
    Write-Log -Message "SSH connection successful." -Level "UI" -ForegroundColor Green

    # Enhanced PostgreSQL Detection
    Write-Log -Message " " -Level "UI"
    Write-Log -Message "=== PostgreSQL Installation Analysis ===" -Level "UI" -ForegroundColor Cyan
    
    # Check PostgreSQL version
    Write-Log -Message "Checking PostgreSQL installation..." -Level "UI" -ForegroundColor Yellow
    $pgCheckArgs = @(
        "-i", "$keyPath",
        "-p", "$($script:Config.ssh.port)",
        "-o", "BatchMode=yes",
        "-o", "StrictHostKeyChecking=accept-new",
        "-o", "ConnectTimeout=10",
        "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
        "psql --version"
    )

    $pgOutput = & ssh @pgCheckArgs 2>&1
    if ($LASTEXITCODE -ne 0) {
        Write-Log -Message "PostgreSQL is not installed." -Level "UI" -ForegroundColor Red
        return
    }

    Write-Log -Message "PostgreSQL is installed: $pgOutput" -Level "UI" -ForegroundColor Green
    
    # Get PostgreSQL service status
    Write-Log -Message " " -Level "UI"
    Write-Log -Message "Checking PostgreSQL service status..." -Level "UI" -ForegroundColor Yellow
    $serviceArgs = @(
        "-i", "$keyPath",
        "-p", "$($script:Config.ssh.port)",
        "-o", "BatchMode=yes",
        "-o", "StrictHostKeyChecking=accept-new",
        "-o", "ConnectTimeout=10",
        "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
        "sudo systemctl is-active postgresql"
    )
    $serviceStatus = & ssh @serviceArgs 2>&1
    if ($serviceStatus -like "*active*") {
        Write-Log -Message "PostgreSQL service is active (running)" -Level "UI" -ForegroundColor Green
    } else {
        Write-Log -Message "PostgreSQL service status: $serviceStatus" -Level "UI" -ForegroundColor Yellow
    }
    
    # Get PostgreSQL configuration file location
    Write-Log -Message " " -Level "UI"
    Write-Log -Message "Locating PostgreSQL configuration..." -Level "UI" -ForegroundColor Yellow
    $configArgs = @(
        "-i", "$keyPath",
        "-p", "$($script:Config.ssh.port)",
        "-o", "BatchMode=yes",
        "-o", "StrictHostKeyChecking=accept-new",
        "-o", "ConnectTimeout=10",
        "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
        "sudo find /etc -name 'postgresql.conf' 2>/dev/null | head -1"
    )
    $configFile = & ssh @configArgs 2>&1
    if ($configFile) {
        Write-Log -Message "PostgreSQL config file: $configFile" -Level "UI" -ForegroundColor Green
    } else {
        Write-Log -Message "PostgreSQL config file not found" -Level "UI" -ForegroundColor Yellow
    }
    
    # Check PostgreSQL port
    Write-Log -Message " " -Level "UI"
    Write-Log -Message "Checking PostgreSQL network status..." -Level "UI" -ForegroundColor Yellow
    $portArgs = @(
        "-i", "$keyPath",
        "-p", "$($script:Config.ssh.port)",
        "-o", "BatchMode=yes",
        "-o", "StrictHostKeyChecking=accept-new",
        "-o", "ConnectTimeout=10",
        "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
        "sudo netstat -tuln | grep ':5432 '"
    )
    $portStatus = & ssh @portArgs 2>&1
    if ($LASTEXITCODE -eq 0 -and $portStatus) {
        Write-Log -Message "PostgreSQL is listening on port 5432" -Level "UI" -ForegroundColor Green
    } else {
        Write-Log -Message "PostgreSQL port 5432 not accessible" -Level "UI" -ForegroundColor Yellow
    }

    # Get current data directory
    Write-Log -Message " " -Level "UI"
    Write-Log -Message "=== PostgreSQL Data Directory Analysis ===" -Level "UI" -ForegroundColor Cyan
    $dataDirArgs = @(
        "-i", "$keyPath",
        "-p", "$($script:Config.ssh.port)",
        "-o", "BatchMode=yes",
        "-o", "StrictHostKeyChecking=accept-new",
        "-o", "ConnectTimeout=10",
        "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
        "sudo -u postgres psql -c 'SHOW data_directory;' | grep -v 'data_directory' | grep -v '(' | grep -v '-' | xargs"
    )

    $currentDataDir = & ssh @dataDirArgs 2>&1
    Write-Log -Message "Current data directory: $currentDataDir" -Level "UI" -ForegroundColor White
    
    # Get data directory disk usage
    if ($currentDataDir) {
        $dataDirUsageArgs = @(
            "-i", "$keyPath",
            "-p", "$($script:Config.ssh.port)",
            "-o", "BatchMode=yes",
            "-o", "StrictHostKeyChecking=accept-new",
            "-o", "ConnectTimeout=10",
            "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
            "df -h '$currentDataDir'"
        )
        $dataDirUsage = & ssh @dataDirUsageArgs 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Log -Message "Current data directory volume:" -Level "UI" -ForegroundColor White
            Write-Log -Message "$dataDirUsage" -Level "UI" -ForegroundColor Gray
        }
    }

    # Check for dedicated volume
    Write-Log -Message " " -Level "UI"
    Write-Log -Message "=== Dedicated Volume Analysis ===" -Level "UI" -ForegroundColor Cyan
    $volumeArgs = @(
        "-i", "$keyPath",
        "-p", "$($script:Config.ssh.port)",
        "-o", "BatchMode=yes",
        "-o", "StrictHostKeyChecking=accept-new",
        "-o", "ConnectTimeout=10",
        "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
        "df -h /pgsql/data 2>/dev/null"
    )

    $volumeOutput = & ssh @volumeArgs 2>&1
    $dedicatedVolumeExists = ($LASTEXITCODE -eq 0)
    
    if ($dedicatedVolumeExists) {
        Write-Log -Message "Found dedicated PostgreSQL volume:" -Level "UI" -ForegroundColor Green
        Write-Log -Message "$volumeOutput" -Level "UI" -ForegroundColor White
        
        # Check mount information
        $mountArgs = @(
            "-i", "$keyPath",
            "-p", "$($script:Config.ssh.port)",
            "-o", "BatchMode=yes",
            "-o", "StrictHostKeyChecking=accept-new",
            "-o", "ConnectTimeout=10",
            "$($script:Config.ssh.username)@$($script:Config.ssh.host)",
            "mount | grep pgsql"
        )
        $mountInfo = & ssh @mountArgs 2>&1
        if ($LASTEXITCODE -eq 0 -and $mountInfo) {
            Write-Log -Message "Volume mount information:" -Level "UI" -ForegroundColor White
            Write-Log -Message "$mountInfo" -Level "UI" -ForegroundColor Gray
        }
    } else {
        Write-Log -Message "No dedicated PostgreSQL volume found at /pgsql/data" -Level "UI" -ForegroundColor Yellow
    }

    # Configuration analysis
    Write-Log -Message " " -Level "UI"
    Write-Log -Message "=== Configuration Analysis ===" -Level "UI" -ForegroundColor Cyan
    
    $isUsingDedicatedVolume = ($currentDataDir -like "*pgsql/data*")
    
    if ($isUsingDedicatedVolume) {
        Write-Log -Message "OPTIMAL CONFIGURATION: PostgreSQL is using dedicated volume" -Level "UI" -ForegroundColor Green
        Write-Log -Message "Current location: $currentDataDir" -Level "UI" -ForegroundColor White
        Write-Log -Message "No migration needed." -Level "UI" -ForegroundColor Green
    } elseif ($dedicatedVolumeExists) {
        Write-Log -Message "MIGRATION RECOMMENDED: PostgreSQL is using system volume" -Level "UI" -ForegroundColor Yellow
        Write-Log -Message "Current location: $currentDataDir" -Level "UI" -ForegroundColor White
        Write-Log -Message "Recommended location: /pgsql/data/17/main" -Level "UI" -ForegroundColor White
        Write-Log -Message "Benefits: Better performance, I/O isolation, dedicated space" -Level "UI" -ForegroundColor White
    } else {
        Write-Log -Message "CURRENT CONFIGURATION: PostgreSQL using system volume" -Level "UI" -ForegroundColor White
        Write-Log -Message "Current location: $currentDataDir" -Level "UI" -ForegroundColor White
        Write-Log -Message "Recommendation: Consider setting up a dedicated volume" -Level "UI" -ForegroundColor Yellow
    }

    Write-Log -Message " " -Level "UI"
    Write-Log -Message "=== Schema File Deployment ===" -Level "UI" -ForegroundColor Cyan

    # Copy schema files from local deployment_files to server
    Write-Log -Message "Copying schema files to server..." -Level "UI" -ForegroundColor Yellow

    # Get installation directory
    $installDir = "/opt/database-service"
    if ($script:Config -and $script:Config.service -and $script:Config.service.install_dir) {
        $installDir = $script:Config.service.install_dir
    }

    # Go up two levels from Modules directory to get to the deployment root, then to deployment_files
    $deploymentRoot = Split-Path -Parent (Split-Path -Parent $PSScriptRoot)
    $localSchemaDir = Join-Path $deploymentRoot "deployment_files\schemas"
    Write-Log -Message "Looking for schema files in: $localSchemaDir" -Level "UI" -ForegroundColor White

    if (Test-Path $localSchemaDir) {
        $schemaFiles = Get-ChildItem -Path $localSchemaDir -Filter "*.sql"
        if ($schemaFiles.Count -gt 0) {
            Write-Log -Message "Found $($schemaFiles.Count) schema files to copy" -Level "UI" -ForegroundColor Green

            # Create directories on server if they don't exist
            $createDirsCmd = "sudo mkdir -p $installDir/sql $installDir/schemas"
            & ssh @testArgs $createDirsCmd 2>&1 | Out-Null

            foreach ($schemaFile in $schemaFiles) {
                Write-Log -Message "Copying schema file: $($schemaFile.Name)" -Level "UI" -ForegroundColor White
                try {
                    # Copy file to temp location on server first
                    $tempPath = "/tmp/$($schemaFile.Name)"
                    $scpCmd = "scp -i `"$keyPath`" -P $($script:Config.ssh.port) `"$($schemaFile.FullName)`" $($script:Config.ssh.username)@$($script:Config.ssh.host):$tempPath"
                    $scpResult = Invoke-Expression $scpCmd 2>&1

                    if ($LASTEXITCODE -eq 0) {
                        # Copy to both sql and schemas directories for compatibility
                        $copyCmd = "sudo cp $tempPath $installDir/sql/$($schemaFile.Name) && sudo cp $tempPath $installDir/schemas/$($schemaFile.Name) && sudo rm $tempPath"
                        $permCmd = "sudo chown database-service:database-service $installDir/sql/$($schemaFile.Name) $installDir/schemas/$($schemaFile.Name)"
                        & ssh @testArgs $copyCmd 2>&1 | Out-Null
                        & ssh @testArgs $permCmd 2>&1 | Out-Null
                        Write-Log -Message "✓ Successfully copied $($schemaFile.Name) to both sql/ and schemas/ directories" -Level "UI" -ForegroundColor Green
                    } else {
                        Write-Log -Message "✗ Failed to copy $($schemaFile.Name): $scpResult" -Level "UI" -ForegroundColor Red
                    }
                } catch {
                    Write-Log -Message "✗ Error copying $($schemaFile.Name): $($_.Exception.Message)" -Level "UI" -ForegroundColor Red
                }
            }
        } else {
            Write-Log -Message "No .sql files found in schema directory" -Level "UI" -ForegroundColor Yellow
        }
    } else {
        Write-Log -Message "Local schema directory not found: $localSchemaDir" -Level "UI" -ForegroundColor Red
    }

    Write-Log -Message " " -Level "UI"
    Write-Log -Message "=== Analysis Complete ===" -Level "UI" -ForegroundColor Green
    Get-UserInput -Prompt 'Press Enter to continue...' | Out-Null
}

Export-ModuleMember -Function Install-Database-Dependencies
