/**
 * @file transaction.cpp
 * @brief Implementation of the Transaction class
 * 
 * This file contains the implementation of the Transaction class which provides
 * ACID transaction support for database operations.
 */

#include "database-service/core/transaction.hpp"
#include "database-service/utils/logger.hpp"
#include "database-service/metrics/database_metrics.hpp"

// Standard Library Headers
#include <format>
#include <print>
#include <sstream>
#include <stdexcept>
#include <utility>

// PostgreSQL client library
#include <libpq-fe.h>

namespace dbservice::core {

// Note: Static assertion for atomic<State> removed due to private enum access

Transaction::Transaction(std::shared_ptr<Connection> connection)
    : connection_(std::move(connection)),
      state_(State::Active),
      startTime_(std::chrono::steady_clock::now()) {
    
    if (!connection_) {
        throw std::invalid_argument("Connection cannot be null");
    }
    
    if (!connection_->isOpen()) {
        throw std::runtime_error("Cannot start transaction: connection is not open");
    }
    
    utils::Logger::debug("Transaction started");
}

Transaction::Transaction(Transaction&& other) noexcept
    : connection_(std::move(other.connection_)),
      state_(other.state_.load(std::memory_order_acquire)),
      startTime_(other.startTime_) {
    // Invalidate the source object
    other.state_.store(State::RolledBack, std::memory_order_release);
}

Transaction& Transaction::operator=(Transaction&& other) noexcept {
    if (this != &other) {
        // Clean up current transaction if active
        if (isActive()) {
            try {
                rollback();
            } catch (...) {
                // Ignore exceptions during move assignment cleanup
            }
        }
        
        // Transfer ownership
        connection_ = std::move(other.connection_);
        state_.store(
            other.state_.load(std::memory_order_acquire),
            std::memory_order_release
        );
        const_cast<std::chrono::steady_clock::time_point&>(startTime_) = other.startTime_;
        
        // Invalidate the source object
        other.state_.store(State::RolledBack, std::memory_order_release);
    }
    return *this;
}

Transaction::~Transaction() noexcept {
    try {
        if (isActive()) {
            utils::Logger::debug("Auto-rolling back uncommitted transaction");
            if (auto result = rollback(); !result) {
                utils::Logger::error(std::format("Auto-rollback failed: {}", result.error()));
            }
        }
    } catch (const std::exception& e) {
        // Prevent exceptions from leaving destructor
        utils::Logger::error(std::format("Exception in ~Transaction(): {}", e.what()));
    } catch (...) {
        utils::Logger::error("Unknown exception in ~Transaction()");
    }
}

bool Transaction::transitionState(State expected, State desired) noexcept {
    State expected_state = expected;
    return state_.compare_exchange_strong(
        expected_state, desired,
        std::memory_order_acq_rel,
        std::memory_order_acquire
    );
}

std::expected<void, std::string> Transaction::commit(
    const std::source_location& loc
) {
    // Try to transition from Active to Committed
    if (!transitionState(State::Active, State::Committed)) {
        State current = state_.load(std::memory_order_acquire);
        if (current == State::Committed) {
            return std::unexpected("Transaction already committed");
        } else if (current == State::RolledBack) {
            return std::unexpected("Transaction already rolled back");
        } else {
            return std::unexpected("Transaction in an invalid state");
        }
    }

    try {
        // Execute COMMIT
        int result = connection_->executeNonQuery("COMMIT", {});
        if (result < 0) {
            // Transition back to Active if commit failed
            state_.store(State::Active, std::memory_order_release);
            return std::unexpected("Failed to commit transaction");
        }

        // Record metrics
        auto duration = getDuration();
        auto& dbMetrics = metrics::DatabaseMetrics::getInstance();
        dbMetrics.recordTransactionMetric(true, duration.count());

        utils::Logger::debug(std::format("Transaction committed successfully ({} ms)", duration.count()));
        return {}; // Success
        
    } catch (const std::exception& e) {
        // Transition to RolledBack on error
        state_.store(State::RolledBack, std::memory_order_release);
        
        std::string error = std::format(
            "Exception during transaction commit at {}:{}: {}",
            loc.file_name(), loc.line(), e.what()
        );
        utils::Logger::error(error);
        return std::unexpected(std::move(error));
    } catch (...) {
        state_.store(State::RolledBack, std::memory_order_release);
        utils::Logger::error("Unknown exception during transaction commit");
        return std::unexpected("Unknown error during commit");
    }
}

std::expected<void, std::string> Transaction::rollback(
    const std::source_location& loc
) {
    // Try to transition from Active to RolledBack
    if (!transitionState(State::Active, State::RolledBack)) {
        State current = state_.load(std::memory_order_acquire);
        if (current == State::Committed) {
            return std::unexpected("Cannot rollback: transaction already committed");
        } else if (current == State::RolledBack) {
            return std::unexpected("Transaction already rolled back");
        } else {
            return std::unexpected("Transaction in an invalid state");
        }
    }

    try {
        // Execute ROLLBACK
        int result = -1;
        try {
            result = connection_->executeNonQuery("ROLLBACK", {});
        } catch (const std::exception& e) {
            utils::Logger::warning(std::format("Error during ROLLBACK: {}", e.what()));
            // Continue even if ROLLBACK fails
        }

        // Record metrics
        auto duration = getDuration();
        auto& dbMetrics = metrics::DatabaseMetrics::getInstance();
        dbMetrics.recordTransactionMetric(false, duration.count());

        if (result < 0) {
            utils::Logger::warning("ROLLBACK command may have failed");
        }

        utils::Logger::debug(std::format("Transaction rolled back ({} ms)", duration.count()));
        return {}; // Success
        
    } catch (const std::exception& e) {
        std::string error = std::format(
            "Exception during transaction rollback at {}:{}: {}",
            loc.file_name(), loc.line(), e.what()
        );
        utils::Logger::error(error);
        return std::unexpected(std::move(error));
    } catch (...) {
        utils::Logger::error("Unknown exception during transaction rollback");
        return std::unexpected("Unknown error during rollback");
    }
}

std::shared_ptr<Connection> Transaction::getConnection() const noexcept {
    return connection_;
}

bool Transaction::isActive() const noexcept {
    return state_.load(std::memory_order_acquire) == State::Active;
}

std::chrono::steady_clock::time_point Transaction::getStartTime() const noexcept {
    return startTime_;
}

std::chrono::milliseconds Transaction::getDuration() const noexcept {
    auto now = std::chrono::steady_clock::now();
    return std::chrono::duration_cast<std::chrono::milliseconds>(
        now - startTime_
    );
}

} // namespace dbservice::core
