// C++ Standard Library
#include <algorithm>
#include <atomic>
#include <chrono>
#include <condition_variable>
#include <cstddef>
#include <cstdint>
#include <cstring>
#include <expected>
#include <filesystem>
#include <format>
#include <functional>
#include <iomanip>
#include <iostream>
#include <iterator>
#include <latch>
#include <memory>
#include <mutex>
#include <optional>
#include <ranges>
#include <source_location>
#include <span>
#include <sstream>
#include <stdexcept>
#include <stop_token>
#include <string>
#include <string_view>
#include <system_error>
#include <thread>
#include <type_traits>
#include <utility>
#include <vector>

// Third-party libraries
#include <nlohmann/json.hpp>

// Project headers
#include "database-service/core/connection_manager.hpp"
#include "database-service/core/connection.hpp"
#include "database-service/core/transaction.hpp"
#include "database-service/metrics/database_metrics.hpp"
#include "database-service/utils/logger.hpp"
#include "database-service/utils/cache.hpp"


// Project-specific headers
#include "database-service/core/connection_manager.hpp"
#include "database-service/core/connection.hpp"
#include "database-service/core/transaction.hpp"
#include "database-service/utils/logger.hpp"

// Logging macros for compatibility
#define LOG_INFO(msg, ...) utils::Logger::info(std::format(msg, ##__VA_ARGS__))
#define LOG_WARNING(msg, ...) utils::Logger::warning(std::format(msg, ##__VA_ARGS__))
#define LOG_ERROR(msg, ...) utils::Logger::error(std::format(msg, ##__VA_ARGS__))
#define LOG_DEBUG(msg, ...) utils::Logger::debug(std::format(msg, ##__VA_ARGS__))
#include "database-service/metrics/database_metrics.hpp"

namespace dbservice::core {

using namespace std::chrono_literals;
using namespace std::string_literals;

std::string isolationLevelToString(TransactionIsolationLevel level) {
    switch (level) {
        case TransactionIsolationLevel::ReadUncommitted:
            return "READ UNCOMMITTED";
        case TransactionIsolationLevel::ReadCommitted:
            return "READ COMMITTED";
        case TransactionIsolationLevel::RepeatableRead:
            return "REPEATABLE READ";
        case TransactionIsolationLevel::Serializable:
            return "SERIALIZABLE";
        default:
            throw std::invalid_argument("Invalid transaction isolation level");
    }
}

std::string sslModeToString(SSLMode mode) {
    switch (mode) {
        case SSLMode::Disable:
            return "disable";
        case SSLMode::Allow:
            return "allow";
        case SSLMode::Prefer:
            return "prefer";
        case SSLMode::Require:
            return "require";
        case SSLMode::VerifyCa:
            return "verify-ca";
        case SSLMode::VerifyFull:
            return "verify-full";
        default:
            throw std::invalid_argument("Invalid SSL mode");
    }
}

// Anonymous namespace for internal implementation details
namespace {

// Constants
constexpr auto DEFAULT_CONNECTION_TIMEOUT = 30s;
constexpr auto MAX_RETRY_ATTEMPTS = 3;
constexpr auto VALIDATION_QUERY = "SELECT 1";
constexpr auto POOL_CLEANUP_INTERVAL = 5min;
constexpr auto MAINTENANCE_INTERVAL = 5min;
constexpr auto VALIDATION_INTERVAL = 30s;
constexpr auto MAX_IDLE_TIME = 10min;

// Helper function to modify connection string with SSL settings
std::string addSSLToConnectionString(const std::string& baseConnectionString, const SSLConfig& sslConfig) {
    std::ostringstream ss;
    ss << baseConnectionString;

    if (sslConfig.enabled) {
        ss << " sslmode=" << sslModeToString(sslConfig.mode);
        if (!sslConfig.certPath.empty()) {
            ss << " sslcert=" << sslConfig.certPath;
        }
        if (!sslConfig.keyPath.empty()) {
            ss << " sslkey=" << sslConfig.keyPath;
        }
        if (!sslConfig.caPath.empty()) {
            ss << " sslrootcert=" << sslConfig.caPath;
        }
    } else {
        ss << " sslmode=disable";
    }

    return ss.str();
}

} // anonymous namespace

ConnectionManager::ConnectionManager(std::string connectionString, size_t maxConnections, const SSLConfig& sslConfig)
    : connectionString_(std::move(connectionString)),
      maxConnections_(maxConnections > 0 ? maxConnections : 10),
      sslConfig_(sslConfig),
      shutdown_(false),
      activeConnections_(0),
      waitingConnections_(0),
      lastMetricsUpdate_(std::chrono::steady_clock::now()),
      autoScalingEnabled_(false),
      minConnections_(2),
      autoScalingMaxConnections_(50),
      scaleUpThreshold_(0.8),
      scaleDownThreshold_(0.3),
      autoScalingCheckInterval_(std::chrono::seconds(30)),
      lastAutoScalingCheck_(std::chrono::steady_clock::now()),
      scaleUpEvents_(0),
      scaleDownEvents_(0),
      lastScaleEvent_(std::chrono::steady_clock::now()) {

    // Initialize query cache
    queryCache_ = std::make_unique<utils::QueryCache>(1000, std::chrono::minutes(5));

    // Validate configuration
    if (connectionString_.empty()) {
        throw std::invalid_argument("Connection string cannot be empty");
    }

    if (maxConnections == 0) {
        throw std::invalid_argument("Maximum connections must be greater than zero");
    }

    // Log SSL configuration if enabled
    if (sslConfig_.enabled) {
        LOG_INFO("SSL enabled with mode: {}", sslModeToString(sslConfig_.mode));
        if (!sslConfig_.caPath.empty()) {
            LOG_DEBUG("Using CA certificate: {}", sslConfig_.caPath);
        }
        if (!sslConfig_.certPath.empty()) {
            LOG_DEBUG("Using client certificate: {}", sslConfig_.certPath);
        }
    }

    LOG_INFO("ConnectionManager initialized with pool size: {}", maxConnections_);
}

ConnectionManager::~ConnectionManager() noexcept {
    try {
        // Close all connections
        std::lock_guard lock(mutex_);
        for (auto& conn : connections_) {
            if (conn) {
                try {
                    conn->close();
                } catch (const std::exception& e) {
                    LOG_ERROR("Error closing connection: {}", e.what());
                }
            }
        }
        connections_.clear();

        LOG_INFO("ConnectionManager destroyed");
    } catch (const std::exception& e) {
        LOG_ERROR("Error during ConnectionManager destruction: {}", e.what());
    } catch (...) {
        LOG_ERROR("Unknown error during ConnectionManager destruction");
    }
}

// Maintenance worker method removed - not declared in header

std::expected<std::shared_ptr<Connection>, std::string> ConnectionManager::getConnection(
    [[maybe_unused]] std::chrono::milliseconds timeout) {

    std::lock_guard lock(mutex_);

    // Perform auto-scaling check
    const_cast<ConnectionManager*>(this)->performAutoScaling();

    // Try to find an available connection
    for (auto it = connections_.begin(); it != connections_.end(); ++it) {
        if (*it && (*it)->isOpen()) {
            auto conn = *it;
            connections_.erase(it);
            activeConnections_++;
            return conn;
        }
    }

    // No available connection, create a new one if under limit
    size_t totalConnections = connections_.size() + activeConnections_;
    size_t effectiveMaxConnections = autoScalingEnabled_ ? autoScalingMaxConnections_ : maxConnections_;

    if (totalConnections < effectiveMaxConnections) {
        try {
            auto connResult = createConnection();
            if (connResult) {
                activeConnections_++;
                return connResult.value();
            } else {
                return std::unexpected(connResult.error());
            }
        } catch (const std::exception& e) {
            return std::unexpected(std::format("Failed to create new connection: {}", e.what()));
        }
    }

    return std::unexpected("No connections available and pool is at maximum capacity");
}

void ConnectionManager::returnConnection(std::shared_ptr<Connection> connection) {
    if (!connection) {
        LOG_WARNING("Attempted to return null connection");
        return;
    }

    try {
        std::lock_guard lock(mutex_);

        // Decrease active connection count
        if (activeConnections_ > 0) {
            activeConnections_--;
        }

        // Check if connection is still valid
        if (connection->isOpen()) {
            connections_.push_back(connection);
            LOG_DEBUG("Connection returned to pool");
        } else {
            LOG_WARNING("Returned connection is not open, discarding");
        }
    } catch (const std::exception& e) {
        LOG_ERROR("Error returning connection to pool: {}", e.what());
    }
}

std::expected<std::vector<std::vector<std::string>>, std::string>
ConnectionManager::executeQuery(const std::string& query, const std::vector<std::string>& params) {
    auto connResult = getConnection();
    if (!connResult) {
        return std::unexpected(connResult.error());
    }

    auto conn = connResult.value();
    try {
        auto result = conn->executeQuery(query, params);
        returnConnection(conn);
        return result;
    } catch (const std::exception& e) {
        returnConnection(conn);
        return std::unexpected(std::format("Query execution failed: {}", e.what()));
    }
}

std::expected<void, std::string> ConnectionManager::executeQueryWithCallback(
    const std::string& query,
    std::function<void(std::span<const std::string>)> callback,
    const std::vector<std::string>& params) {

    auto connResult = getConnection();
    if (!connResult) {
        return std::unexpected(connResult.error());
    }

    auto conn = connResult.value();
    try {
        auto result = conn->executeQuery(query, params);

        // Call callback for each row
        for (const auto& row : result) {
            std::span<const std::string> rowSpan(row.data(), row.size());
            callback(rowSpan);
        }

        returnConnection(conn);
        return {};
    } catch (const std::exception& e) {
        returnConnection(conn);
        return std::unexpected(std::format("Query execution with callback failed: {}", e.what()));
    }
}

std::expected<int, std::string> ConnectionManager::executeNonQuery(
    const std::string& statement,
    const std::vector<std::string>& params) {
    auto connResult = getConnection();
    if (!connResult) {
        return std::unexpected(connResult.error());
    }

    auto conn = connResult.value();
    try {
        auto result = conn->executeNonQuery(statement, params);
        returnConnection(conn);
        return result;
    } catch (const std::exception& e) {
        returnConnection(conn);
        return std::unexpected(std::format("Non-query execution failed: {}", e.what()));
    }
}

std::expected<std::vector<std::string>, std::string> ConnectionManager::listDatabases() {
    auto connResult = getConnection();
    if (!connResult) {
        return std::unexpected(connResult.error());
    }

    auto conn = connResult.value();
    try {
        auto result = conn->executeQuery("SELECT datname FROM pg_database WHERE datistemplate = false", {});

        std::vector<std::string> databases;
        for (const auto& row : result) {
            if (!row.empty()) {
                databases.push_back(row[0]);
            }
        }

        returnConnection(conn);
        return databases;
    } catch (const std::exception& e) {
        returnConnection(conn);
        return std::unexpected(std::format("Failed to list databases: {}", e.what()));
    }
}

std::expected<void, std::string> ConnectionManager::createDatabase(
    const std::string& dbName,
    const std::string& templateName,
    const std::optional<std::string>& owner,
    const std::optional<std::string>& encoding,
    const std::optional<std::string>& lcCollate,
    const std::optional<std::string>& lcCtype,
    const std::optional<std::string>& tablespace,
    int connectionLimit,
    bool isTemplate) {

    if (dbName.empty()) {
        return std::unexpected("Database name cannot be empty");
    }

    auto connResult = getConnection();
    if (!connResult) {
        return std::unexpected(connResult.error());
    }

    auto conn = connResult.value();
    try {
        std::ostringstream query;
        query << "CREATE DATABASE " << dbName;

        if (!templateName.empty() && templateName != "template1") {
            query << " TEMPLATE " << templateName;
        }

        if (owner) {
            query << " OWNER " << *owner;
        }

        if (encoding) {
            query << " ENCODING '" << *encoding << "'";
        }

        if (lcCollate) {
            query << " LC_COLLATE '" << *lcCollate << "'";
        }

        if (lcCtype) {
            query << " LC_CTYPE '" << *lcCtype << "'";
        }

        if (tablespace) {
            query << " TABLESPACE " << *tablespace;
        }

        if (connectionLimit >= 0) {
            query << " CONNECTION LIMIT " << connectionLimit;
        }

        if (isTemplate) {
            query << " IS_TEMPLATE true";
        }

        conn->executeNonQuery(query.str(), {});
        returnConnection(conn);
        return {};
    } catch (const std::exception& e) {
        returnConnection(conn);
        return std::unexpected(std::format("Failed to create database: {}", e.what()));
    }
}

std::expected<void, std::string> ConnectionManager::dropDatabase(
    const std::string& dbName,
    bool ifExists,
    bool force) {

    if (dbName.empty()) {
        return std::unexpected("Database name cannot be empty");
    }

    auto connResult = getConnection();
    if (!connResult) {
        return std::unexpected(connResult.error());
    }

    auto conn = connResult.value();
    try {
        std::ostringstream query;
        query << "DROP DATABASE ";

        if (ifExists) {
            query << "IF EXISTS ";
        }

        query << dbName;

        if (force) {
            query << " WITH (FORCE)";
        }

        conn->executeNonQuery(query.str(), {});
        returnConnection(conn);
        return {};
    } catch (const std::exception& e) {
        returnConnection(conn);
        return std::unexpected(std::format("Failed to drop database: {}", e.what()));
    }
}

std::string ConnectionManager::buildConnectionString() const {
    return addSSLToConnectionString(connectionString_, sslConfig_);
}

std::string ConnectionManager::getConnectionString() const {
    return buildConnectionString();
}

const SSLConfig& ConnectionManager::getSSLConfig() const {
    return sslConfig_;
}

size_t ConnectionManager::getActiveConnectionCount() const {
    std::lock_guard lock(mutex_);
    return connections_.size();
}

size_t ConnectionManager::getIdleConnectionCount() const {
    std::lock_guard lock(mutex_);
    return connections_.size(); // All connections in pool are idle
}

size_t ConnectionManager::getWaitingConnectionCount() const {
    return 0; // Simplified implementation
}

size_t ConnectionManager::getMaxConnections() const {
    return maxConnections_;
}

std::expected<nlohmann::json, std::string> ConnectionManager::getMetrics() const {
    try {
        std::lock_guard lock(mutex_);

        nlohmann::json metrics = {
            {"active_connections", 0}, // Simplified - would need tracking
            {"available_connections", connections_.size()},
            {"waiting_connections", 0},
            {"max_connections", maxConnections_},
            {"total_connections_created", 0}, // Simplified - would need tracking
            {"total_connections_closed", 0},
            {"total_queries_executed", 0},
            {"total_transactions", 0},
            {"committed_transactions", 0},
            {"rolled_back_transactions", 0}
        };

        return metrics;
    } catch (const std::exception& e) {
        return std::unexpected(std::format("Failed to get metrics: {}", e.what()));
    }
}

// Removed PoolStats and validateConnection methods - not in header

std::expected<nlohmann::json, std::string> ConnectionManager::executeCachedQuery(
    const std::string& query,
    const std::vector<std::string>& params,
    std::chrono::milliseconds ttl) {

    if (!queryCache_) {
        return std::unexpected("Query cache not initialized");
    }

    // Check cache first
    auto cachedResult = queryCache_->get(query, params);
    if (cachedResult) {
        LOG_DEBUG("Cache hit for query: {}", query.substr(0, 50));
        return *cachedResult;
    }

    // Cache miss - execute query
    LOG_DEBUG("Cache miss for query: {}", query.substr(0, 50));
    auto queryResult = executeQuery(query, params);
    if (!queryResult) {
        return std::unexpected(queryResult.error());
    }

    // Convert result to JSON
    try {
        nlohmann::json jsonResult = nlohmann::json::array();
        for (const auto& row : *queryResult) {
            nlohmann::json jsonRow = nlohmann::json::array();
            for (const auto& cell : row) {
                jsonRow.push_back(cell);
            }
            jsonResult.push_back(jsonRow);
        }

        // Cache the result
        queryCache_->put(query, params, jsonResult,
                        ttl.count() > 0 ? std::make_optional(ttl) : std::nullopt);

        return jsonResult;
    } catch (const std::exception& e) {
        return std::unexpected(std::format("Failed to convert query result to JSON: {}", e.what()));
    }
}

// Removed methods not in header

std::expected<std::shared_ptr<Connection>, std::string> ConnectionManager::createConnection() {
    try {
        auto finalConnectionString = addSSLToConnectionString(connectionString_, sslConfig_);
        auto conn = std::make_shared<Connection>(finalConnectionString, sslConfig_.enabled);
        if (!conn->open()) {
            return std::unexpected("Failed to open database connection");
        }

        return conn;
    } catch (const std::exception& e) {
        return std::unexpected(std::format("Failed to create connection: {}", e.what()));
    }
}

std::expected<std::shared_ptr<Transaction>, std::string> ConnectionManager::beginTransaction(
    [[maybe_unused]] TransactionIsolationLevel isolationLevel,
    [[maybe_unused]] bool readOnly,
    [[maybe_unused]] bool deferrable) {

    auto connResult = getConnection();
    if (!connResult) {
        return std::unexpected(std::format("Failed to get connection: {}", connResult.error()));
    }

    try {
        auto conn = connResult.value();
        auto txn = conn->beginTransaction();
        if (!txn) {
            returnConnection(conn);
            return std::unexpected("Failed to begin transaction");
        }

        // Set transaction properties
        // Note: This is a simplified implementation
        // In a real implementation, you'd set isolation level, read-only, etc.

        return txn;
    } catch (const std::exception& e) {
        returnConnection(connResult.value());
        return std::unexpected(std::format("Failed to begin transaction: {}", e.what()));
    }
}

void ConnectionManager::shutdown() {
    std::lock_guard<std::mutex> lock(mutex_);

    if (shutdown_) {
        return; // Already shut down
    }

    shutdown_ = true;

    // Close all connections in the pool
    while (!connections_.empty()) {
        auto connection = connections_.front();
        connections_.pop_front();
        // Connection will be automatically closed when it goes out of scope
    }

    // Notify all waiting threads
    cv_.notify_all();

    utils::Logger::info("ConnectionManager shutdown completed");
}

std::string ConnectionManager::sslModeToString(SSLMode mode) {
    switch (mode) {
        case SSLMode::Disable:
            return "disable";
        case SSLMode::Allow:
            return "allow";
        case SSLMode::Prefer:
            return "prefer";
        case SSLMode::Require:
            return "require";
        case SSLMode::VerifyCa:
            return "verify-ca";
        case SSLMode::VerifyFull:
            return "verify-full";
        default:
            throw std::invalid_argument("Invalid SSL mode");
    }
}

// ManagedConnection implementation
ManagedConnection::ManagedConnection(ConnectionManager& manager)
    : manager_(&manager), moved_from_(false) {
    connection_expected_ = manager_->getConnection();
}

ManagedConnection::~ManagedConnection() {
    if (!moved_from_ && connection_expected_.has_value()) {
        manager_->returnConnection(std::move(connection_expected_.value()));
    }
}

ManagedConnection::ManagedConnection(ManagedConnection&& other) noexcept
    : manager_(other.manager_),
      connection_expected_(std::move(other.connection_expected_)),
      moved_from_(false) {
    other.moved_from_ = true;
}

ManagedConnection& ManagedConnection::operator=(ManagedConnection&& other) noexcept {
    if (this != &other) {
        // Return current connection if we have one
        if (!moved_from_ && connection_expected_.has_value()) {
            manager_->returnConnection(std::move(connection_expected_.value()));
        }

        manager_ = other.manager_;
        connection_expected_ = std::move(other.connection_expected_);
        moved_from_ = false;
        other.moved_from_ = true;
    }
    return *this;
}

Connection* ManagedConnection::operator->() const {
    if (connection_expected_.has_value()) {
        return connection_expected_.value().get();
    }
    return nullptr;
}

std::shared_ptr<Connection> ManagedConnection::get() const {
    if (connection_expected_.has_value()) {
        return connection_expected_.value();
    }
    return nullptr;
}

ManagedConnection::operator bool() const {
    return connection_expected_.has_value();
}

const std::string* ManagedConnection::getError() const {
    if (!connection_expected_.has_value()) {
        return &connection_expected_.error();
    }
    return nullptr;
}

void ConnectionManager::configureAutoScaling(
    bool enabled,
    size_t minConnections,
    size_t maxConnections,
    double scaleUpThreshold,
    double scaleDownThreshold,
    std::chrono::milliseconds checkIntervalMs) {

    std::lock_guard<std::mutex> lock(mutex_);

    autoScalingEnabled_ = enabled;
    minConnections_ = minConnections;
    autoScalingMaxConnections_ = maxConnections;
    scaleUpThreshold_ = scaleUpThreshold;
    scaleDownThreshold_ = scaleDownThreshold;
    autoScalingCheckInterval_ = checkIntervalMs;

    LOG_INFO("Auto-scaling configured: enabled={}, min={}, max={}, up_threshold={:.2f}, down_threshold={:.2f}",
             enabled, minConnections, maxConnections, scaleUpThreshold, scaleDownThreshold);
}

nlohmann::json ConnectionManager::getAutoScalingStats() const {
    std::lock_guard<std::mutex> lock(mutex_);

    nlohmann::json stats;
    stats["enabled"] = autoScalingEnabled_;
    stats["min_connections"] = minConnections_;
    stats["max_connections"] = autoScalingMaxConnections_;
    stats["scale_up_threshold"] = scaleUpThreshold_;
    stats["scale_down_threshold"] = scaleDownThreshold_;
    stats["check_interval_ms"] = autoScalingCheckInterval_.count();
    stats["scale_up_events"] = scaleUpEvents_;
    stats["scale_down_events"] = scaleDownEvents_;
    stats["last_scale_event"] = std::chrono::duration_cast<std::chrono::seconds>(
        lastScaleEvent_.time_since_epoch()).count();

    return stats;
}

void ConnectionManager::performAutoScaling() {
    if (!autoScalingEnabled_) {
        return;
    }

    auto now = std::chrono::steady_clock::now();
    if (now - lastAutoScalingCheck_ < autoScalingCheckInterval_) {
        return;
    }

    lastAutoScalingCheck_ = now;

    std::lock_guard<std::mutex> lock(mutex_);

    size_t totalConnections = connections_.size() + activeConnections_;

    // Calculate utilization
    double utilization = totalConnections > 0 ?
        static_cast<double>(activeConnections_) / totalConnections : 0.0;

    // Scale up if utilization is high and we're below max
    if (utilization > scaleUpThreshold_ && totalConnections < autoScalingMaxConnections_) {
        size_t targetSize = std::min(totalConnections + 2, autoScalingMaxConnections_);
        scaleUp(targetSize);
    }
    // Scale down if utilization is low and we're above min
    else if (utilization < scaleDownThreshold_ && totalConnections > minConnections_) {
        size_t targetSize = std::max(totalConnections - 1, minConnections_);
        scaleDown(targetSize);
    }
}

void ConnectionManager::scaleUp(size_t targetSize) {
    size_t currentSize = connections_.size() + activeConnections_;
    size_t connectionsToAdd = targetSize - currentSize;

    for (size_t i = 0; i < connectionsToAdd; ++i) {
        auto connResult = createConnection();
        if (connResult) {
            connections_.push_back(connResult.value());
            LOG_DEBUG("Added connection to pool (auto-scaling)");
        } else {
            LOG_WARNING("Failed to create connection during scale-up: {}", connResult.error());
            break;
        }
    }

    scaleUpEvents_++;
    lastScaleEvent_ = std::chrono::steady_clock::now();
    LOG_INFO("Scaled up connection pool to {} connections", connections_.size() + activeConnections_);
}

void ConnectionManager::scaleDown(size_t targetSize) {
    size_t currentSize = connections_.size() + activeConnections_;
    size_t connectionsToRemove = currentSize - targetSize;

    // Only remove idle connections
    size_t removed = 0;
    while (removed < connectionsToRemove && !connections_.empty()) {
        connections_.pop_back();
        removed++;
    }

    if (removed > 0) {
        scaleDownEvents_++;
        lastScaleEvent_ = std::chrono::steady_clock::now();
        LOG_INFO("Scaled down connection pool by {} connections", removed);
    }
}

void ConnectionManager::clearCache() {
    if (queryCache_) {
        queryCache_->clear();
        LOG_INFO("Query cache cleared");
    }
}

nlohmann::json ConnectionManager::getCacheStats() const {
    if (queryCache_) {
        return queryCache_->getStats();
    }
    return nlohmann::json::object();
}

void ConnectionManager::updateMetrics() {
    std::lock_guard<std::mutex> lock(mutex_);
    lastMetricsUpdate_ = std::chrono::steady_clock::now();

    // Perform auto-scaling check
    const_cast<ConnectionManager*>(this)->performAutoScaling();
}

bool ConnectionManager::isShutdown() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return shutdown_;
}

} // namespace dbservice::core
