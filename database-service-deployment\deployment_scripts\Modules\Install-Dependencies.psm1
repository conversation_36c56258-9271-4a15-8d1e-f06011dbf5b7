# Install-Dependencies-Clean.psm1 - Clean module for installing dependencies

<#
.SYNOPSIS
    Provides functionality for installing system dependencies.

.DESCRIPTION
    This module handles the installation of system dependencies required
    for both database-service and auth-service applications.

.NOTES
    File Name      : Install-Dependencies-Clean.psm1
    Author         : Auth Service Deployment Team
    Prerequisite   : PowerShell 5.1 or later
    Copyright      : (c) 2025 Augment
#>

function Install-Dependencies {
    [CmdletBinding()]
    param()

    Write-Host "=== Installing System Dependencies ===" -ForegroundColor Cyan
    Write-Host ""

    # Determine service name from configuration
    Write-Host "Debug: Config project name = '$($script:Config.project.name)'" -ForegroundColor Magenta
    Write-Host "Debug: Config service name = '$($script:Config.service.name)'" -ForegroundColor Magenta

    $serviceName = if ($script:Config.project.name -eq "auth-service") {
        "auth-service"
    } elseif ($script:Config.service.name -eq "auth-service") {
        "auth-service"
    } else {
        "database-service"
    }

    $installDir = if ($serviceName -eq "auth-service") {
        "/opt/auth-service"
    } else {
        "/opt/database-service"
    }
    
    Write-Host "Service: $serviceName" -ForegroundColor Yellow
    Write-Host "Install Directory: $installDir" -ForegroundColor Yellow
    Write-Host ""

    # Get dependencies from configuration
    $dependencies = $script:Config.dependencies

    if (-not $dependencies) {
        Write-Host "❌ No dependencies found in configuration" -ForegroundColor Red
        return $false
    }

    Write-Host "Found $($dependencies.Count) dependencies to install" -ForegroundColor Green
    Write-Host ""

    # Update package list first
    Write-Host "Updating package list..." -ForegroundColor Yellow
    
    $updateCmd = "sudo apt-get update"
    
    try {
        $sshCommand = "ssh -i `"$($script:Config.ssh.key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$($script:Config.ssh.host) `"$updateCmd`""
        $result = Invoke-Expression $sshCommand
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Package list updated successfully" -ForegroundColor Green
        } else {
            Write-Host "❌ Failed to update package list" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ Error updating package list: $_" -ForegroundColor Red
        return $false
    }

    Write-Host ""

    # Install each dependency
    $successCount = 0
    $totalCount = $dependencies.Count

    foreach ($dependency in $dependencies) {
        $depName = $dependency.name
        $depCommand = $dependency.command
        $depCheck = $dependency.check

        Write-Host "Installing: $depName" -ForegroundColor Yellow
        Write-Host "  Command: $depCommand" -ForegroundColor Gray

        try {
            $sshCommand = "ssh -i `"$($script:Config.ssh.key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$($script:Config.ssh.host) `"$depCommand`""
            $result = Invoke-Expression $sshCommand
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "  ✅ $depName installed successfully" -ForegroundColor Green
                $successCount++
            } else {
                Write-Host "  ❌ Failed to install $depName" -ForegroundColor Red
                Write-Host "  Output: $result" -ForegroundColor Gray
            }
        } catch {
            Write-Host "  ❌ Error installing $depName`: $_" -ForegroundColor Red
        }

        Write-Host ""
    }

    # Summary
    Write-Host "=== Installation Summary ===" -ForegroundColor Cyan
    Write-Host "Successfully installed: $successCount of $totalCount dependencies" -ForegroundColor $(if ($successCount -eq $totalCount) { "Green" } else { "Yellow" })

    if ($successCount -eq $totalCount) {
        Write-Host ""
        Write-Host "✅ All dependencies installed successfully!" -ForegroundColor Green
        
        # Create service user and directories
        Write-Host ""
        Write-Host "Creating service user and directories..." -ForegroundColor Yellow
        
        $setupCmd = @"
sudo useradd -r -s /bin/false $serviceName 2>/dev/null || echo 'User already exists' &&
sudo mkdir -p $installDir/bin $installDir/config $installDir/logs $installDir/sql &&
sudo chown -R $serviceName`:$serviceName $installDir &&
sudo chmod -R 775 $installDir
"@

        try {
            $sshCommand = "ssh -i `"$($script:Config.ssh.key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$($script:Config.ssh.host) `"$setupCmd`""
            $result = Invoke-Expression $sshCommand
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ Service user and directories created" -ForegroundColor Green
            } else {
                Write-Host "❌ Failed to create service user and directories" -ForegroundColor Red
            }
        } catch {
            Write-Host "❌ Error creating service setup: $_" -ForegroundColor Red
        }

        return $true
    } else {
        Write-Host ""
        Write-Host "❌ Some dependencies failed to install. Please check the errors above." -ForegroundColor Red
        return $false
    }
}

# Export the function
Export-ModuleMember -Function Install-Dependencies
