#include "user_manager.hpp"
#include "database_manager.hpp"
#include "security_manager.hpp"
#include <iostream>

UserManager::UserManager(DatabaseManager* db_manager, SecurityManager* sec_manager)
    : db_manager_(db_manager), sec_manager_(sec_manager) {}

UserManager::~UserManager() = default;

bool UserManager::create_user(const std::string& username, const std::string& password) {
    std::cout << "Creating user: " << username << std::endl;
    
    // Hash password
    std::string hashed_password = sec_manager_->hash_password(password);
    
    // Store in database (will be implemented later)
    std::cout << "User created successfully: " << username << std::endl;
    return true;
}

bool UserManager::authenticate_user(const std::string& username, const std::string& password) {
    std::cout << "Authenticating user: " << username << std::endl;
    
    // Retrieve user from database (will be implemented later)
    // For now, simulate successful authentication
    std::string stored_hash = sec_manager_->hash_password(password);
    
    bool authenticated = sec_manager_->verify_password(password, stored_hash);
    
    if (authenticated) {
        std::cout << "User authenticated successfully: " << username << std::endl;
    } else {
        std::cout << "Authentication failed for user: " << username << std::endl;
    }
    
    return authenticated;
}

bool UserManager::delete_user(const std::string& username) {
    std::cout << "Deleting user: " << username << std::endl;
    
    // Delete from database (will be implemented later)
    std::cout << "User deleted successfully: " << username << std::endl;
    return true;
}
