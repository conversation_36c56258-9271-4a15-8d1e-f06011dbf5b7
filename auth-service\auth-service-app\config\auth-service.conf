{"database": {"host": "***********", "port": 5432, "name": "auth_service", "user": "auth_service", "password": "password2311"}, "server": {"port": 8082, "log_level": "info"}, "oauth2": {"jwt": {"secret": "production-jwt-secret-key-change-this-in-production", "access_token_expiry": 3600, "refresh_token_expiry": 604800, "algorithm": "HS256"}, "argon2": {"memory_cost": 65536, "time_cost": 3, "parallelism": 4, "salt_length": 32}, "session": {"timeout": 86400, "cleanup_interval": 3600, "max_sessions_per_user": 5}}}