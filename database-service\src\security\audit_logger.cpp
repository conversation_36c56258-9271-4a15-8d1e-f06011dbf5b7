#include "database-service/security/audit_logger.hpp"
#include "database-service/core/connection.hpp"
#include "database-service/core/connection_manager.hpp"
#include "database-service/utils/logger.hpp"
#include <format>
#include <sstream>

namespace dbservice::security {

AuditLogger::AuditLogger(std::shared_ptr<dbservice::core::ConnectionManager> connectionManager)
    : connectionManager_(connectionManager), initialized_(false) {
}

bool AuditLogger::initialize() {
    if (initialized_) {
        return true;
    }

    if (!connectionManager_) {
        utils::Logger::error("AuditLogger: Connection manager is null");
        return false;
    }

    try {
        auto connection_result = connectionManager_->getConnection();
        if (!connection_result) {
            utils::Logger::error("AuditLogger: Failed to get database connection");
            return false;
        }
        auto connection = *connection_result;

        // Check if audit_log table exists
        std::string checkTableQuery = R"(
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'audit_log'
            );
        )";

        auto result = connection->executeQuery(checkTableQuery);
        if (result.empty() || result[0].empty() || result[0][0] != "t") {
            utils::Logger::error("AuditLogger: Audit_log table does not exist");
            return false;
        }

        initialized_ = true;
        utils::Logger::info("AuditLogger initialized successfully");
        return true;

    } catch (const std::exception& e) {
        utils::Logger::error(std::format("AuditLogger initialization failed: {}", e.what()));
        return false;
    }
}

std::expected<void, AuditError> AuditLogger::logUserAction(
    int userId,
    const std::string& action,
    const std::string& resource,
    bool success,
    const nlohmann::json& details,
    const std::string& ipAddress,
    const std::string& userAgent) {

    AuditEntry entry;
    entry.timestamp = std::chrono::system_clock::now();
    entry.eventType = AuditEventType::USER_LOGIN; // Will be determined by action
    entry.userId = userId;
    entry.applicationId = 0; // Not applicable for user actions
    entry.action = action;
    entry.resource = resource;
    entry.ipAddress = ipAddress;
    entry.userAgent = userAgent;
    entry.success = success;
    entry.details = details;

    // Determine event type based on action
    if (action == "login") {
        entry.eventType = AuditEventType::USER_LOGIN;
    } else if (action == "logout") {
        entry.eventType = AuditEventType::USER_LOGOUT;
    } else if (action == "create_user") {
        entry.eventType = AuditEventType::USER_CREATED;
    } else if (action == "update_user") {
        entry.eventType = AuditEventType::USER_UPDATED;
    } else if (action == "delete_user") {
        entry.eventType = AuditEventType::USER_DELETED;
    }

    return logEntry(entry);
}

std::expected<void, AuditError> AuditLogger::logApplicationAction(
    int applicationId,
    const std::string& action,
    const std::string& resource,
    bool success,
    const nlohmann::json& details,
    const std::string& ipAddress) {

    AuditEntry entry;
    entry.timestamp = std::chrono::system_clock::now();
    entry.eventType = AuditEventType::APPLICATION_REGISTERED; // Will be determined by action
    entry.userId = 0; // Not applicable for application actions
    entry.applicationId = applicationId;
    entry.action = action;
    entry.resource = resource;
    entry.ipAddress = ipAddress;
    entry.success = success;
    entry.details = details;

    // Determine event type based on action
    if (action == "register_application") {
        entry.eventType = AuditEventType::APPLICATION_REGISTERED;
    } else if (action == "update_application") {
        entry.eventType = AuditEventType::APPLICATION_UPDATED;
    } else if (action == "deactivate_application") {
        entry.eventType = AuditEventType::APPLICATION_DEACTIVATED;
    } else if (action == "generate_api_key") {
        entry.eventType = AuditEventType::API_KEY_GENERATED;
    } else if (action == "validate_api_key") {
        entry.eventType = AuditEventType::API_KEY_VALIDATED;
    }

    return logEntry(entry);
}

std::expected<void, AuditError> AuditLogger::logSecurityEvent(
    AuditEventType eventType,
    const std::string& action,
    const std::string& resource,
    bool success,
    const nlohmann::json& details,
    int userId,
    int applicationId,
    const std::string& ipAddress,
    const std::string& userAgent) {

    AuditEntry entry;
    entry.timestamp = std::chrono::system_clock::now();
    entry.eventType = eventType;
    entry.userId = userId;
    entry.applicationId = applicationId;
    entry.action = action;
    entry.resource = resource;
    entry.ipAddress = ipAddress;
    entry.userAgent = userAgent;
    entry.success = success;
    entry.details = details;

    return logEntry(entry);
}

std::expected<void, AuditError> AuditLogger::logDatabaseOperation(
    int applicationId,
    const std::string& operation,
    const std::string& query,
    bool success,
    const nlohmann::json& details,
    const std::string& ipAddress) {

    AuditEntry entry;
    entry.timestamp = std::chrono::system_clock::now();
    entry.userId = 0; // Database operations are application-based
    entry.applicationId = applicationId;
    entry.action = operation;
    entry.resource = "database";
    entry.ipAddress = ipAddress;
    entry.success = success;
    entry.details = details;

    // Add query to details
    nlohmann::json extendedDetails = details;
    extendedDetails["query"] = query;
    entry.details = extendedDetails;

    // Determine event type based on operation
    if (operation == "SELECT" || operation == "query") {
        entry.eventType = AuditEventType::DATABASE_QUERY;
    } else if (operation == "INSERT" || operation == "UPDATE" || operation == "DELETE" || operation == "execute") {
        entry.eventType = AuditEventType::DATABASE_EXECUTE;
    } else if (operation == "transaction") {
        entry.eventType = AuditEventType::DATABASE_TRANSACTION;
    } else {
        entry.eventType = AuditEventType::DATABASE_EXECUTE;
    }

    return logEntry(entry);
}

std::expected<void, AuditError> AuditLogger::logEntry(const AuditEntry& entry) {
    if (!initialized_) {
        return std::unexpected(AuditError::INITIALIZATION_FAILED);
    }

    try {
        auto connection_result = connectionManager_->getConnection();
        if (!connection_result) {
            return std::unexpected(AuditError::DATABASE_ERROR);
        }
        auto connection = *connection_result;

        std::string insertQuery = R"(
            INSERT INTO audit_log (timestamp, event_type, user_id, application_id, action, resource, ip_address, user_agent, success, error_message, details, created_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, NOW())
        )";

        // Convert timestamp to string (simplified)
        auto timeT = std::chrono::system_clock::to_time_t(entry.timestamp);
        std::string timestampStr = std::to_string(timeT);

        std::vector<std::string> params = {
            timestampStr,
            eventTypeToString(entry.eventType),
            std::to_string(entry.userId),
            std::to_string(entry.applicationId),
            entry.action,
            entry.resource,
            entry.ipAddress,
            entry.userAgent,
            entry.success ? "true" : "false",
            entry.errorMessage,
            entry.details.dump()
        };

        std::vector<std::string> param_vector(params.begin(), params.end());
        connection->executeNonQuery(insertQuery, param_vector);
        return {};

    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Failed to log audit entry: {}", e.what()));
        return std::unexpected(AuditError::DATABASE_ERROR);
    }
}

std::string AuditLogger::eventTypeToString(AuditEventType eventType) {
    switch (eventType) {
        case AuditEventType::USER_LOGIN: return "USER_LOGIN";
        case AuditEventType::USER_LOGOUT: return "USER_LOGOUT";
        case AuditEventType::USER_CREATED: return "USER_CREATED";
        case AuditEventType::USER_UPDATED: return "USER_UPDATED";
        case AuditEventType::USER_DELETED: return "USER_DELETED";
        case AuditEventType::APPLICATION_REGISTERED: return "APPLICATION_REGISTERED";
        case AuditEventType::APPLICATION_UPDATED: return "APPLICATION_UPDATED";
        case AuditEventType::APPLICATION_DEACTIVATED: return "APPLICATION_DEACTIVATED";
        case AuditEventType::DATABASE_QUERY: return "DATABASE_QUERY";
        case AuditEventType::DATABASE_EXECUTE: return "DATABASE_EXECUTE";
        case AuditEventType::DATABASE_TRANSACTION: return "DATABASE_TRANSACTION";
        case AuditEventType::API_KEY_GENERATED: return "API_KEY_GENERATED";
        case AuditEventType::API_KEY_VALIDATED: return "API_KEY_VALIDATED";
        case AuditEventType::PERMISSION_GRANTED: return "PERMISSION_GRANTED";
        case AuditEventType::PERMISSION_REVOKED: return "PERMISSION_REVOKED";
        case AuditEventType::SECURITY_VIOLATION: return "SECURITY_VIOLATION";
        case AuditEventType::CONFIGURATION_CHANGED: return "CONFIGURATION_CHANGED";
        case AuditEventType::SYSTEM_ERROR: return "SYSTEM_ERROR";
        default: return "UNKNOWN";
    }
}

AuditEventType AuditLogger::stringToEventType(const std::string& eventTypeStr) {
    if (eventTypeStr == "USER_LOGIN") return AuditEventType::USER_LOGIN;
    if (eventTypeStr == "USER_LOGOUT") return AuditEventType::USER_LOGOUT;
    if (eventTypeStr == "USER_CREATED") return AuditEventType::USER_CREATED;
    if (eventTypeStr == "USER_UPDATED") return AuditEventType::USER_UPDATED;
    if (eventTypeStr == "USER_DELETED") return AuditEventType::USER_DELETED;
    if (eventTypeStr == "APPLICATION_REGISTERED") return AuditEventType::APPLICATION_REGISTERED;
    if (eventTypeStr == "APPLICATION_UPDATED") return AuditEventType::APPLICATION_UPDATED;
    if (eventTypeStr == "APPLICATION_DEACTIVATED") return AuditEventType::APPLICATION_DEACTIVATED;
    if (eventTypeStr == "DATABASE_QUERY") return AuditEventType::DATABASE_QUERY;
    if (eventTypeStr == "DATABASE_EXECUTE") return AuditEventType::DATABASE_EXECUTE;
    if (eventTypeStr == "DATABASE_TRANSACTION") return AuditEventType::DATABASE_TRANSACTION;
    if (eventTypeStr == "API_KEY_GENERATED") return AuditEventType::API_KEY_GENERATED;
    if (eventTypeStr == "API_KEY_VALIDATED") return AuditEventType::API_KEY_VALIDATED;
    if (eventTypeStr == "PERMISSION_GRANTED") return AuditEventType::PERMISSION_GRANTED;
    if (eventTypeStr == "PERMISSION_REVOKED") return AuditEventType::PERMISSION_REVOKED;
    if (eventTypeStr == "SECURITY_VIOLATION") return AuditEventType::SECURITY_VIOLATION;
    if (eventTypeStr == "CONFIGURATION_CHANGED") return AuditEventType::CONFIGURATION_CHANGED;
    if (eventTypeStr == "SYSTEM_ERROR") return AuditEventType::SYSTEM_ERROR;
    return AuditEventType::SYSTEM_ERROR; // Default fallback
}

} // namespace dbservice::security
