# Authentication Service Requirements

*Requirements Documentation*  
*Created: December 24, 2024*  
*Target Platform: Ubuntu 24.04 LTS*  
*Based on: Current git.chcit.org server analysis*

## Table of Contents
1. [System Requirements](#system-requirements)
2. [Software Dependencies](#software-dependencies)
3. [Library Requirements](#library-requirements)
4. [Development Tools](#development-tools)
5. [Runtime Dependencies](#runtime-dependencies)
6. [Security Requirements](#security-requirements)
7. [Performance Requirements](#performance-requirements)
8. [Installation Commands](#installation-commands)

## System Requirements

### Current Server Analysis (git.chcit.org)
```bash
# Verified current system specifications
OS: Ubuntu 24.04.2 LTS (noble)
PostgreSQL: 17.5 (Ubuntu 17.5-1.pgdg24.04+1) ✅
GCC: 14.2.0 (Ubuntu 14.2.0-4ubuntu2~24.04) ✅
CMake: 4.0.1 ✅
Boost: 1.83.0.1ubuntu2 ✅
OpenSSL: 3.0.13 ✅
nlohmann-json: 3.11.3-1 ✅
libpqxx: 7.8.1-2.1build1 ✅
libcurl: 8.5.0-2ubuntu10.6 ✅
```

### Hardware Requirements
```json
{
  "minimum": {
    "cpu_cores": 2,
    "memory": "4GB",
    "storage": "20GB SSD",
    "network": "100Mbps"
  },
  "recommended": {
    "cpu_cores": 4,
    "memory": "8GB", 
    "storage": "50GB SSD",
    "network": "1Gbps"
  },
  "production": {
    "cpu_cores": 8,
    "memory": "16GB",
    "storage": "100GB SSD",
    "network": "1Gbps"
  }
}
```

## Software Dependencies

### Core System Requirements
```json
{
  "operating_system": {
    "name": "Ubuntu",
    "version": "24.04.2 LTS",
    "codename": "noble",
    "architecture": "amd64",
    "kernel": "6.8+",
    "required": true
  },
  "compiler": {
    "name": "GCC",
    "minimum_version": "14.2.0",
    "current_version": "14.2.0",
    "features_required": ["C++23", "std::expected", "std::format"],
    "package": "gcc-14 g++-14",
    "required": true
  },
  "build_system": {
    "name": "CMake",
    "minimum_version": "3.20",
    "current_version": "4.0.1",
    "package": "cmake",
    "required": true
  }
}
```

### Database Requirements
```json
{
  "postgresql": {
    "server_version": "17.5",
    "client_version": "17.5",
    "extensions_required": ["pgcrypto", "uuid-ossp"],
    "packages": [
      "postgresql-17",
      "postgresql-client-17", 
      "postgresql-contrib-17",
      "libpq-dev"
    ],
    "configuration": {
      "max_connections": 200,
      "shared_buffers": "256MB",
      "ssl": "on"
    },
    "required": true
  }
}
```

## Library Requirements

### Core C++ Libraries
```json
{
  "boost": {
    "minimum_version": "1.83.0",
    "current_version": "1.83.0.1ubuntu2",
    "components_required": [
      "system",
      "program_options", 
      "thread",
      "filesystem",
      "asio"
    ],
    "package": "libboost-all-dev",
    "required": true
  },
  "openssl": {
    "minimum_version": "3.0.13",
    "current_version": "3.0.13",
    "features_required": ["TLS 1.3", "ECDSA", "RSA"],
    "package": "libssl-dev",
    "required": true
  },
  "nlohmann_json": {
    "minimum_version": "3.11.3",
    "current_version": "3.11.3-1",
    "features_required": ["JSON parsing", "serialization"],
    "package": "nlohmann-json3-dev",
    "required": true
  },
  "libpqxx": {
    "minimum_version": "7.8.1",
    "current_version": "7.8.1-2.1build1",
    "features_required": ["PostgreSQL C++ API", "prepared statements"],
    "package": "libpqxx-dev",
    "required": true
  }
}
```

### Authentication-Specific Libraries
```json
{
  "argon2": {
    "minimum_version": "20190702",
    "package": "libargon2-dev",
    "features_required": ["Argon2id", "memory-hard hashing"],
    "status": "NEEDS_INSTALLATION",
    "required": true,
    "priority": "HIGH"
  },
  "jwt_cpp": {
    "minimum_version": "0.7.0",
    "repository": "https://github.com/Thalhammer/jwt-cpp",
    "features_required": ["RS256", "HS256", "token validation"],
    "status": "NEEDS_INSTALLATION", 
    "required": true,
    "priority": "HIGH"
  },
  "libcurl": {
    "minimum_version": "8.5.0",
    "current_version": "8.5.0-2ubuntu10.6",
    "features_required": ["HTTP client", "SSL support"],
    "package": "libcurl4-openssl-dev",
    "required": true
  }
}
```

### HTTP Server Libraries
```json
{
  "crow": {
    "minimum_version": "1.2.0",
    "repository": "https://github.com/CrowCpp/Crow",
    "features_required": ["HTTP server", "middleware", "JSON support"],
    "status": "NEEDS_INSTALLATION",
    "build_from_source": true,
    "required": true,
    "priority": "HIGH"
  },
  "alternative_httplib": {
    "minimum_version": "0.14.0",
    "repository": "https://github.com/yhirose/cpp-httplib",
    "features_required": ["HTTP server", "SSL support"],
    "status": "FALLBACK_OPTION",
    "header_only": true,
    "required": false
  }
}
```

### Utility Libraries
```json
{
  "spdlog": {
    "minimum_version": "1.12.0",
    "package": "libspdlog-dev",
    "features_required": ["structured logging", "async logging"],
    "status": "RECOMMENDED",
    "required": false
  },
  "fmt": {
    "minimum_version": "10.0.0",
    "package": "libfmt-dev", 
    "features_required": ["string formatting", "C++23 std::format fallback"],
    "status": "RECOMMENDED",
    "required": false
  },
  "prometheus_cpp": {
    "minimum_version": "1.2.0",
    "repository": "https://github.com/jupp0r/prometheus-cpp",
    "features_required": ["metrics collection", "HTTP exposition"],
    "status": "OPTIONAL",
    "required": false
  }
}
```

## Development Tools

### Required Development Tools
```json
{
  "pkg_config": {
    "minimum_version": "0.29.1",
    "package": "pkg-config",
    "required": true
  },
  "git": {
    "minimum_version": "2.34.1",
    "package": "git",
    "required": true
  },
  "make": {
    "minimum_version": "4.3",
    "package": "build-essential",
    "required": true
  },
  "ninja": {
    "minimum_version": "1.11.1",
    "package": "ninja-build",
    "required": false,
    "recommended": true
  }
}
```

### Optional Development Tools
```json
{
  "clang_format": {
    "version": "18.0.0",
    "package": "clang-format-18",
    "purpose": "code formatting",
    "required": false
  },
  "clang_tidy": {
    "version": "18.0.0", 
    "package": "clang-tidy-18",
    "purpose": "static analysis",
    "required": false
  },
  "valgrind": {
    "version": "3.21.0",
    "package": "valgrind",
    "purpose": "memory debugging",
    "required": false
  },
  "gdb": {
    "version": "15.0.50",
    "package": "gdb",
    "purpose": "debugging",
    "required": false
  }
}
```

## Runtime Dependencies

### System Services
```json
{
  "systemd": {
    "minimum_version": "255.4",
    "features_required": ["service management", "socket activation"],
    "required": true
  },
  "nginx": {
    "minimum_version": "1.24.0",
    "package": "nginx",
    "purpose": "reverse proxy and SSL termination",
    "required": true
  },
  "ufw": {
    "minimum_version": "0.36.2",
    "package": "ufw",
    "purpose": "firewall management",
    "required": true
  }
}
```

### Security Tools
```json
{
  "fail2ban": {
    "minimum_version": "1.0.2",
    "package": "fail2ban",
    "purpose": "intrusion prevention",
    "required": true
  },
  "certbot": {
    "minimum_version": "2.9.0",
    "package": "certbot python3-certbot-nginx",
    "purpose": "SSL certificate management",
    "required": true
  },
  "unattended_upgrades": {
    "package": "unattended-upgrades",
    "purpose": "automatic security updates",
    "required": true
  }
}
```

## Security Requirements

### Cryptographic Requirements
```json
{
  "password_hashing": {
    "algorithm": "Argon2id",
    "parameters": {
      "time_cost": 3,
      "memory_cost": "65536KB",
      "parallelism": 4,
      "salt_length": 16,
      "hash_length": 32
    }
  },
  "jwt_signing": {
    "algorithms": ["RS256", "HS256"],
    "key_size": 2048,
    "token_lifetime": "30 minutes",
    "refresh_lifetime": "7 days"
  },
  "tls": {
    "minimum_version": "1.2",
    "preferred_version": "1.3",
    "cipher_suites": ["ECDHE-RSA-AES256-GCM-SHA384", "ECDHE-RSA-CHACHA20-POLY1305"]
  }
}
```

### Compliance Requirements
```json
{
  "standards": {
    "owasp": "Top 10 2021 compliance",
    "gdpr": "data protection compliance",
    "iso27001": "information security management"
  },
  "audit_logging": {
    "retention": "90 days minimum",
    "format": "structured JSON",
    "fields": ["timestamp", "user_id", "action", "ip_address", "result"]
  }
}
```

## Performance Requirements

### Response Time Requirements
```json
{
  "authentication": {
    "login": "< 500ms (95th percentile)",
    "token_validation": "< 50ms (95th percentile)",
    "password_hashing": "< 1000ms (95th percentile)"
  },
  "throughput": {
    "concurrent_users": 1000,
    "requests_per_second": 500,
    "database_connections": 100
  }
}
```

### Resource Limits
```json
{
  "memory": {
    "maximum": "512MB per process",
    "typical": "256MB per process"
  },
  "cpu": {
    "maximum": "80% of available cores",
    "typical": "20% of available cores"
  },
  "network": {
    "bandwidth": "100Mbps sustained",
    "connections": "10000 concurrent"
  }
}
```

## Installation Commands

### System Preparation (Ubuntu 24.04)
```bash
#!/bin/bash
# install-auth-service-dependencies.sh - Ubuntu 24.04 dependency installation

set -e

echo "Installing Authentication Service dependencies on Ubuntu 24.04..."

# Update system
sudo apt update && sudo apt upgrade -y

# Install core development tools
sudo apt install -y build-essential cmake ninja-build pkg-config git curl wget

# Install GCC 14.2 (already available in Ubuntu 24.04)
sudo apt install -y gcc-14 g++-14
sudo update-alternatives --install /usr/bin/gcc gcc /usr/bin/gcc-14 100 \
    --slave /usr/bin/g++ g++ /usr/bin/g++-14

# Install PostgreSQL 17 (already installed on git.chcit.org)
sudo apt install -y postgresql-17 postgresql-client-17 postgresql-contrib-17 libpq-dev

# Install core C++ libraries (already available)
sudo apt install -y libboost-all-dev libssl-dev nlohmann-json3-dev libpqxx-dev
sudo apt install -y libcurl4-openssl-dev

# Install Argon2 library (REQUIRED - not currently installed)
sudo apt install -y libargon2-dev

# Install optional but recommended libraries
sudo apt install -y libspdlog-dev libfmt-dev

# Install system services
sudo apt install -y nginx ufw fail2ban unattended-upgrades

# Install development tools
sudo apt install -y clang-format-18 clang-tidy-18 valgrind gdb

echo "Core dependencies installed successfully!"
```

### Build-from-Source Libraries
```bash
#!/bin/bash
# install-source-libraries.sh - Install libraries that need to be built from source

set -e

INSTALL_PREFIX="/usr/local"
BUILD_DIR="/tmp/auth-service-build"

mkdir -p "$BUILD_DIR"
cd "$BUILD_DIR"

echo "Installing JWT-CPP library..."
# JWT-CPP (header-only library)
git clone --depth 1 --branch v0.7.0 https://github.com/Thalhammer/jwt-cpp.git
cd jwt-cpp
mkdir build && cd build
cmake .. -DCMAKE_INSTALL_PREFIX="$INSTALL_PREFIX" -DJWT_BUILD_EXAMPLES=OFF
sudo make install
cd "$BUILD_DIR"

echo "Installing Crow HTTP framework..."
# Crow HTTP framework
git clone --depth 1 --branch v1.2.0 https://github.com/CrowCpp/Crow.git
cd Crow
mkdir build && cd build
cmake .. -DCMAKE_INSTALL_PREFIX="$INSTALL_PREFIX" \
         -DCROW_BUILD_EXAMPLES=OFF \
         -DCROW_BUILD_TESTS=OFF \
         -DCMAKE_BUILD_TYPE=Release
make -j$(nproc)
sudo make install
cd "$BUILD_DIR"

echo "Installing Prometheus C++ client (optional)..."
# Prometheus C++ client (optional)
git clone --depth 1 --branch v1.2.4 https://github.com/jupp0r/prometheus-cpp.git
cd prometheus-cpp
git submodule update --init --recursive
mkdir build && cd build
cmake .. -DCMAKE_INSTALL_PREFIX="$INSTALL_PREFIX" \
         -DENABLE_TESTING=OFF \
         -DCMAKE_BUILD_TYPE=Release
make -j$(nproc)
sudo make install

# Update library cache
sudo ldconfig

# Clean up
cd /
rm -rf "$BUILD_DIR"

echo "Source libraries installed successfully!"
```

### Verification Script
```bash
#!/bin/bash
# verify-auth-service-requirements.sh - Verify all dependencies are installed

set -e

echo "Verifying Authentication Service requirements..."

# Function to check version
check_version() {
    local name="$1"
    local command="$2"
    local min_version="$3"

    echo -n "Checking $name: "
    if version=$($command 2>/dev/null); then
        echo "✅ $version"
    else
        echo "❌ Not found or error"
        return 1
    fi
}

# Function to check package
check_package() {
    local name="$1"
    local package="$2"

    echo -n "Checking $name: "
    if dpkg -s "$package" >/dev/null 2>&1; then
        version=$(dpkg -s "$package" | grep Version | awk '{print $2}')
        echo "✅ $version"
    else
        echo "❌ Not installed"
        return 1
    fi
}

# Function to check header
check_header() {
    local name="$1"
    local header="$2"

    echo -n "Checking $name header: "
    if echo "#include <$header>" | gcc -x c++ -E - >/dev/null 2>&1; then
        echo "✅ Found"
    else
        echo "❌ Not found"
        return 1
    fi
}

echo "=== System Information ==="
check_version "Ubuntu" "lsb_release -r | awk '{print \$2}'" "24.04"
check_version "GCC" "gcc --version | head -1 | awk '{print \$4}'" "14.2.0"
check_version "CMake" "cmake --version | head -1 | awk '{print \$3}'" "3.20"

echo -e "\n=== Database ==="
check_version "PostgreSQL" "psql --version | awk '{print \$3}'" "17.5"
check_package "libpq-dev" "libpq-dev"

echo -e "\n=== Core Libraries ==="
check_package "Boost" "libboost-dev"
check_package "OpenSSL" "libssl-dev"
check_package "nlohmann-json" "nlohmann-json3-dev"
check_package "libpqxx" "libpqxx-dev"
check_package "libcurl" "libcurl4-openssl-dev"

echo -e "\n=== Authentication Libraries ==="
check_package "Argon2" "libargon2-dev"
check_header "JWT-CPP" "jwt-cpp/jwt.h"
check_header "Crow" "crow.h"

echo -e "\n=== Optional Libraries ==="
check_package "spdlog" "libspdlog-dev" || echo "  (Optional - for enhanced logging)"
check_package "fmt" "libfmt-dev" || echo "  (Optional - for string formatting)"

echo -e "\n=== System Services ==="
check_version "systemd" "systemctl --version | head -1 | awk '{print \$2}'" "255"
check_package "nginx" "nginx"
check_package "ufw" "ufw"
check_package "fail2ban" "fail2ban"

echo -e "\n=== Development Tools ==="
check_package "pkg-config" "pkg-config"
check_package "git" "git"
check_package "ninja" "ninja-build" || echo "  (Optional - faster builds)"

echo -e "\n=== Security Verification ==="
echo -n "Checking Argon2 functionality: "
if echo "test" | argon2 salt -t 1 -m 12 -p 1 >/dev/null 2>&1; then
    echo "✅ Working"
else
    echo "❌ Not working"
fi

echo -n "Checking OpenSSL functionality: "
if openssl rand -hex 16 >/dev/null 2>&1; then
    echo "✅ Working"
else
    echo "❌ Not working"
fi

echo -n "Checking PostgreSQL connection: "
if sudo -u postgres psql -c "SELECT version();" >/dev/null 2>&1; then
    echo "✅ Working"
else
    echo "❌ Not working"
fi

echo -e "\n=== Summary ==="
echo "Requirements verification completed."
echo "If any items show ❌, install them using the provided installation scripts."
```

### Quick Installation (All-in-One)
```bash
#!/bin/bash
# quick-install-auth-service-deps.sh - One-command installation

set -e

echo "Quick installation of Authentication Service dependencies..."

# Download and run system preparation
curl -fsSL https://raw.githubusercontent.com/your-repo/auth-service/main/scripts/install-auth-service-dependencies.sh | bash

# Download and run source library installation
curl -fsSL https://raw.githubusercontent.com/your-repo/auth-service/main/scripts/install-source-libraries.sh | bash

# Download and run verification
curl -fsSL https://raw.githubusercontent.com/your-repo/auth-service/main/scripts/verify-auth-service-requirements.sh | bash

echo "Authentication Service dependencies installation completed!"
echo "You can now proceed with building the auth-service application."
```

## Compatibility Matrix

### Tested Configurations
| Component | Version | Status | Notes |
|-----------|---------|--------|-------|
| Ubuntu | 24.04.2 LTS | ✅ Verified | Current production environment |
| GCC | 14.2.0 | ✅ Verified | Full C++23 support |
| PostgreSQL | 17.5 | ✅ Verified | Latest stable version |
| Boost | 1.83.0 | ✅ Verified | Ubuntu 24.04 default |
| OpenSSL | 3.0.13 | ✅ Verified | Ubuntu 24.04 default |
| CMake | 4.0.1 | ✅ Verified | Latest version |
| nlohmann-json | 3.11.3 | ✅ Verified | Ubuntu 24.04 package |
| libpqxx | 7.8.1 | ✅ Verified | Ubuntu 24.04 package |
| libcurl | 8.5.0 | ✅ Verified | Ubuntu 24.04 package |
| Argon2 | 20190702 | ⚠️ Needs Install | Available in Ubuntu repos |
| JWT-CPP | 0.7.0 | ⚠️ Build Required | Header-only library |
| Crow | 1.2.0 | ⚠️ Build Required | HTTP framework |

### Version Comparison with Database Service
| Library | Database Service | Auth Service | Compatibility |
|---------|------------------|--------------|---------------|
| PostgreSQL | 17.5 | 17.5 | ✅ Same |
| GCC | 14.2.0 | 14.2.0 | ✅ Same |
| Boost | 1.83.0 | 1.83.0 | ✅ Same |
| OpenSSL | 3.0.13 | 3.0.13 | ✅ Same |
| nlohmann-json | 3.11.3 | 3.11.3 | ✅ Same |
| libpqxx | 7.8.1 | 7.8.1 | ✅ Same |
| Argon2 | ❌ Not used | ✅ Required | ➕ New addition |
| JWT-CPP | ❌ Not used | ✅ Required | ➕ New addition |
| Crow | ❌ Not used | ✅ Required | ➕ New addition |

### Migration Notes from Database Service
The auth-service shares many dependencies with the existing database-service:

**✅ Already Available (No Installation Required):**
- PostgreSQL 17.5: Already installed and configured
- GCC 14.2: Already available with C++23 support
- Core libraries: Boost, OpenSSL, nlohmann-json, libpqxx already present
- System services: nginx, systemd already configured
- Development tools: cmake, git, build-essential already installed

**⚠️ New Requirements (Installation Required):**
- **Argon2**: `sudo apt install libargon2-dev`
- **JWT-CPP**: Build from source (header-only)
- **Crow HTTP**: Build from source
- **Optional**: spdlog, fmt for enhanced logging

**🔧 Configuration Updates Needed:**
- PostgreSQL: Create auth_service database and user
- Nginx: Add auth-service reverse proxy configuration
- Systemd: Create auth-service.service file
- Firewall: Open port 8082 for auth service

This ensures minimal additional setup required on the existing git.chcit.org server.

## CMake Configuration Template

### CMakeLists.txt for Auth Service
```cmake
cmake_minimum_required(VERSION 3.20)
project(auth-service VERSION 1.0.0 LANGUAGES CXX)

# Set CMake policies
if(POLICY CMP0167)
    cmake_policy(SET CMP0167 NEW)
endif()

# Require C++23
set(CMAKE_CXX_STANDARD 23)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Compiler-specific options
if(CMAKE_CXX_COMPILER_ID STREQUAL "GNU")
    if(CMAKE_CXX_COMPILER_VERSION VERSION_LESS "14.2")
        message(FATAL_ERROR "GCC 14.2 or later required for C++23 support")
    endif()
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -Wpedantic")
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O3 -march=native")
endif()

# Find required packages
find_package(Boost REQUIRED COMPONENTS system program_options thread)
find_package(PostgreSQL REQUIRED)
find_package(OpenSSL REQUIRED)
find_package(Threads REQUIRED)
find_package(nlohmann_json REQUIRED)

# Find pqxx
find_package(pqxx REQUIRED)

# Find Argon2
find_package(PkgConfig REQUIRED)
pkg_check_modules(ARGON2 REQUIRED libargon2)

# Find Crow (assuming it's installed)
find_package(Crow REQUIRED)

# Find JWT-CPP (header-only)
find_path(JWT_CPP_INCLUDE_DIR jwt-cpp/jwt.h)
if(NOT JWT_CPP_INCLUDE_DIR)
    message(FATAL_ERROR "JWT-CPP headers not found. Please install jwt-cpp.")
endif()

# Source files
file(GLOB_RECURSE SOURCES "src/*.cpp")

# Create executable
add_executable(auth-service ${SOURCES})

# Include directories
target_include_directories(auth-service PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${JWT_CPP_INCLUDE_DIR}
    ${ARGON2_INCLUDE_DIRS}
)

# Link libraries
target_link_libraries(auth-service PRIVATE
    Boost::system
    Boost::program_options
    Boost::thread
    PostgreSQL::PostgreSQL
    pqxx
    OpenSSL::SSL
    OpenSSL::Crypto
    nlohmann_json::nlohmann_json
    Crow::Crow
    ${ARGON2_LIBRARIES}
    Threads::Threads
)

# Compiler definitions
target_compile_definitions(auth-service PRIVATE
    CROW_ENABLE_SSL
    BOOST_ASIO_HAS_STD_INVOKE_RESULT
)

# Install targets
install(TARGETS auth-service
    RUNTIME DESTINATION bin
)

install(FILES config/production.json
    DESTINATION etc/auth-service
    RENAME config.json
)
```

This requirements document provides a complete foundation for implementing the auth-service with the latest software versions while leveraging the existing infrastructure on git.chcit.org.
