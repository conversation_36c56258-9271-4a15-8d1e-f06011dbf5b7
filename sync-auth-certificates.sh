#!/bin/bash

# SSL Certificate Sync Script for Auth-Service
# Distributes *.chcit.org certificates to auth-service development and production servers
# Based on the proven sync-certificates.sh system
#
# COPY TO: D:\Coding_Projects\auth-service\cert_sync_helper_app\
# DEPLOY TO: /opt/auth-service/scripts/sync-auth-certificates.sh

# Configuration
MAIN_SERVER="project-tracker.chcit.org"
MAIN_SERVER_USER="btaylor-admin"
CERT_DIR="/etc/letsencrypt"
DOMAIN="chcit.org"
LOG_DIR="/opt/auth-service/logs"
SYNC_LOG="$LOG_DIR/auth-cert-sync.log"
SSH_KEY="/home/<USER>/.ssh/id_ed25519"

# Target servers for auth-service
declare -A AUTH_SERVERS=(
    ["development"]="dev.chcit.org"
    ["production"]="git.chcit.org"
)

# Auth-service specific paths
AUTH_CERT_DIR="/etc/ssl/certs/auth-service"
AUTH_SERVICE_NAME="auth-service"
AUTH_USER="auth-service"
AUTH_GROUP="auth-service"

# Function to log messages
log() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] $1"
    echo "[$timestamp] $1" >> "$SYNC_LOG" 2>/dev/null || true
}

# Function to check if we're running on the certificate source server
check_source_server() {
    local hostname=$(hostname -f)
    if [[ "$hostname" != "$MAIN_SERVER" ]]; then
        log "ERROR: This script must be run from the certificate source server ($MAIN_SERVER)"
        log "Current server: $hostname"
        return 1
    fi
    return 0
}

# Function to verify certificate files exist
verify_source_certificates() {
    log "Verifying source certificates for domain: $DOMAIN"
    
    local cert_path="$CERT_DIR/live/$DOMAIN"
    local required_files=("cert.pem" "privkey.pem" "chain.pem" "fullchain.pem")
    
    for file in "${required_files[@]}"; do
        if [[ ! -f "$cert_path/$file" ]]; then
            log "ERROR: Required certificate file not found: $cert_path/$file"
            return 1
        fi
    done
    
    # Check certificate expiry
    local expiry_date=$(openssl x509 -in "$cert_path/cert.pem" -noout -enddate | cut -d= -f2)
    local expiry_epoch=$(date -d "$expiry_date" +%s)
    local current_epoch=$(date +%s)
    local days_until_expiry=$(( (expiry_epoch - current_epoch) / 86400 ))
    
    log "Certificate expires in $days_until_expiry days ($expiry_date)"
    
    if [[ $days_until_expiry -lt 30 ]]; then
        log "WARNING: Certificate expires in less than 30 days!"
    fi
    
    return 0
}

# Function to sync certificates to a specific auth-service server
sync_to_auth_server() {
    local env=$1
    local server=${AUTH_SERVERS[$env]}
    
    if [[ -z "$server" ]]; then
        log "ERROR: Unknown environment: $env"
        return 1
    fi
    
    log "Syncing certificates to auth-service $env environment ($server)..."
    
    # Test SSH connectivity
    if ! ssh -i "$SSH_KEY" -o ConnectTimeout=10 -o BatchMode=yes "$MAIN_SERVER_USER@$server" "echo 'SSH connection successful'" >/dev/null 2>&1; then
        log "ERROR: SSH connection failed to $server"
        return 1
    fi
    
    # Create remote certificate directory structure
    log "Creating certificate directory structure on $server..."
    ssh -i "$SSH_KEY" "$MAIN_SERVER_USER@$server" "
        sudo mkdir -p $AUTH_CERT_DIR &&
        sudo chown $AUTH_USER:$AUTH_GROUP $AUTH_CERT_DIR &&
        sudo chmod 750 $AUTH_CERT_DIR
    "
    
    if [[ $? -ne 0 ]]; then
        log "ERROR: Failed to create certificate directory on $server"
        return 1
    fi
    
    # Create temporary staging directory
    local staging_dir="/tmp/auth-certs-$$"
    mkdir -p "$staging_dir"
    
    # Copy certificates to staging directory
    cp "$CERT_DIR/live/$DOMAIN"/*.pem "$staging_dir/"
    
    # Transfer certificates to remote server
    log "Transferring certificates to $server..."
    rsync -avz --delete \
        -e "ssh -i $SSH_KEY" \
        "$staging_dir/" \
        "$MAIN_SERVER_USER@$server:/tmp/auth-certs-staging/"
    
    if [[ $? -ne 0 ]]; then
        log "ERROR: Failed to transfer certificates to $server"
        rm -rf "$staging_dir"
        return 1
    fi
    
    # Move certificates to final location and set permissions
    log "Installing certificates on $server..."
    ssh -i "$SSH_KEY" "$MAIN_SERVER_USER@$server" "
        sudo cp /tmp/auth-certs-staging/*.pem $AUTH_CERT_DIR/ &&
        sudo chown $AUTH_USER:$AUTH_GROUP $AUTH_CERT_DIR/*.pem &&
        sudo chmod 600 $AUTH_CERT_DIR/privkey.pem &&
        sudo chmod 644 $AUTH_CERT_DIR/cert.pem &&
        sudo chmod 644 $AUTH_CERT_DIR/chain.pem &&
        sudo chmod 644 $AUTH_CERT_DIR/fullchain.pem &&
        sudo rm -rf /tmp/auth-certs-staging/
    "
    
    if [[ $? -ne 0 ]]; then
        log "ERROR: Failed to install certificates on $server"
        rm -rf "$staging_dir"
        return 1
    fi
    
    # Verify certificate installation
    log "Verifying certificate installation on $server..."
    local remote_cert_check=$(ssh -i "$SSH_KEY" "$MAIN_SERVER_USER@$server" "
        sudo test -f $AUTH_CERT_DIR/cert.pem &&
        sudo test -f $AUTH_CERT_DIR/privkey.pem &&
        sudo test -f $AUTH_CERT_DIR/chain.pem &&
        echo 'CERT_OK' || echo 'CERT_FAILED'
    ")
    
    if [[ "$remote_cert_check" != "CERT_OK" ]]; then
        log "ERROR: Certificate verification failed on $server"
        rm -rf "$staging_dir"
        return 1
    fi
    
    # Reload auth-service if it's running
    log "Reloading auth-service on $server..."
    ssh -i "$SSH_KEY" "$MAIN_SERVER_USER@$server" "
        if sudo systemctl is-active --quiet $AUTH_SERVICE_NAME; then
            sudo systemctl reload $AUTH_SERVICE_NAME
            log 'Auth-service reloaded successfully'
        else
            log 'Auth-service is not running, skipping reload'
        fi
    "
    
    # Test HTTPS endpoint
    local subdomain="auth"
    if [[ "$env" == "development" ]]; then
        subdomain="auth-dev"
    fi
    
    local test_url="https://$subdomain.chcit.org:8082"
    log "Testing HTTPS endpoint: $test_url"
    
    # Give the service a moment to reload
    sleep 2
    
    if curl -s -o /dev/null -w "%{http_code}" --connect-timeout 10 "$test_url" >/dev/null 2>&1; then
        log "HTTPS endpoint test successful for $test_url"
    else
        log "WARNING: HTTPS endpoint test failed for $test_url (service may not be running)"
    fi
    
    # Clean up staging directory
    rm -rf "$staging_dir"
    
    log "Certificate sync to $env environment completed successfully"
    return 0
}

# Function to sync certificates to all auth-service environments
sync_all_environments() {
    log "Starting auth-service certificate synchronization to all environments"
    
    local overall_success=true
    
    for env in "${!AUTH_SERVERS[@]}"; do
        if ! sync_to_auth_server "$env"; then
            overall_success=false
            log "ERROR: Failed to sync certificates to $env environment"
        fi
    done
    
    if $overall_success; then
        log "All auth-service certificate synchronizations completed successfully"
        return 0
    else
        log "Some auth-service certificate synchronizations failed"
        return 1
    fi
}

# Function to display usage information
usage() {
    echo "Usage: $0 [environment]"
    echo ""
    echo "Environments:"
    echo "  development  - Sync to development server (dev.chcit.org)"
    echo "  production   - Sync to production server (git.chcit.org)"
    echo "  all          - Sync to all environments (default)"
    echo ""
    echo "Examples:"
    echo "  $0                    # Sync to all environments"
    echo "  $0 all                # Sync to all environments"
    echo "  $0 development        # Sync to development only"
    echo "  $0 production         # Sync to production only"
}

# Main execution
main() {
    # Create log directory if it doesn't exist
    mkdir -p "$LOG_DIR" 2>/dev/null || true
    
    log "Auth-service certificate sync script started"
    
    # Check if we're on the correct source server
    if ! check_source_server; then
        exit 1
    fi
    
    # Verify source certificates
    if ! verify_source_certificates; then
        log "ERROR: Source certificate verification failed"
        exit 1
    fi
    
    # Parse command line arguments
    local target_env="${1:-all}"
    
    case "$target_env" in
        "development"|"production")
            if ! sync_to_auth_server "$target_env"; then
                log "ERROR: Certificate sync failed for $target_env environment"
                exit 1
            fi
            ;;
        "all"|"")
            if ! sync_all_environments; then
                log "ERROR: Certificate sync failed for one or more environments"
                exit 1
            fi
            ;;
        "help"|"-h"|"--help")
            usage
            exit 0
            ;;
        *)
            echo "ERROR: Unknown environment: $target_env"
            usage
            exit 1
            ;;
    esac
    
    log "Auth-service certificate sync script completed successfully"
}

# Run main function with all arguments
main "$@"
