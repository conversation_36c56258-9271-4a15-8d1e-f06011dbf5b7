#include "database-service/service/database_instance_manager.hpp"
#include "database-service/core/connection_manager.hpp"
#include "database-service/core/connection.hpp"
#include "database-service/security/credential_store.hpp"
#include "database-service/utils/logger.hpp"
#include <regex>
#include <format>
#include <sstream>
#include <span>

namespace dbservice::service {

DatabaseInstanceManager::DatabaseInstanceManager(
    std::shared_ptr<dbservice::core::ConnectionManager> connectionManager,
    std::shared_ptr<dbservice::security::CredentialStore> credentialStore)
    : connectionManager_(connectionManager), 
      credentialStore_(credentialStore), 
      initialized_(false) {
}

bool DatabaseInstanceManager::initialize() {
    if (initialized_) {
        return true;
    }

    if (!connectionManager_) {
        utils::Logger::error("DatabaseInstanceManager: Connection manager is null");
        return false;
    }

    if (!credentialStore_) {
        utils::Logger::error("DatabaseInstanceManager: Credential store is null");
        return false;
    }

    try {
        auto connectionResult = connectionManager_->getConnection();
        if (!connectionResult.has_value()) {
            utils::Logger::error("DatabaseInstanceManager: Failed to get database connection");
            return false;
        }
        auto connection = connectionResult.value();

        // Check if database_instances table exists
        std::string checkTableQuery = R"(
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'database_instances'
            );
        )";

        auto result = connection->executeQuery(checkTableQuery);
        if (result.empty() || result[0].empty() || result[0][0] != "t") {
            utils::Logger::error("DatabaseInstanceManager: Database_instances table does not exist");
            return false;
        }

        initialized_ = true;
        utils::Logger::info("DatabaseInstanceManager initialized successfully");
        return true;

    } catch (const std::exception& e) {
        utils::Logger::error(std::format("DatabaseInstanceManager initialization failed: {}", e.what()));
        return false;
    }
}

std::expected<int, DatabaseError> DatabaseInstanceManager::registerInstance(const DatabaseConfig& config) {
    if (!initialized_) {
        utils::Logger::error("DatabaseInstanceManager: Not initialized");
        return std::unexpected(DatabaseError::DATABASE_ERROR);
    }

    // Validate configuration
    if (!isValidDatabaseConfig(config)) {
        utils::Logger::warning("Invalid database configuration");
        return std::unexpected(DatabaseError::INVALID_PARAMETERS);
    }

    // Check if instance name already exists
    if (instanceNameExists(config.name)) {
        utils::Logger::warning(std::format("Database instance name already exists: {}", config.name));
        return std::unexpected(DatabaseError::INSTANCE_ALREADY_EXISTS);
    }

    try {
        auto connectionResult = connectionManager_->getConnection();
        if (!connectionResult.has_value()) {
            utils::Logger::error("Failed to get database connection");
            return std::unexpected(DatabaseError::DATABASE_ERROR);
        }
        auto connection = connectionResult.value();

        // Insert database instance
        std::string insertQuery = R"(
            INSERT INTO database_instances (name, host, port, database_name, username, use_ssl, max_connections, created_at, updated_at, active, metadata)
            VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW(), true, $8)
            RETURNING id;
        )";

        std::vector<std::string> params = {
            config.name,
            config.host,
            std::to_string(config.port),
            config.databaseName,
            config.username,
            config.useSSL ? "true" : "false",
            std::to_string(config.maxConnections),
            config.metadata.dump()
        };

        auto result = connection->executeQuery(insertQuery, std::span<const std::string>(params));
        if (result.empty() || result[0].empty()) {
            utils::Logger::error("Failed to insert database instance");
            return std::unexpected(DatabaseError::DATABASE_ERROR);
        }

        int instanceId = std::stoi(result[0][0]);

        // Store credentials securely
        if (!storeCredentials(instanceId, config.username, config.password)) {
            // Rollback the instance creation
            std::vector<std::string> deleteParams = {std::to_string(instanceId)};
            connection->executeNonQuery("DELETE FROM database_instances WHERE id = $1", std::span<const std::string>(deleteParams));
            utils::Logger::error("Failed to store credentials securely");
            return std::unexpected(DatabaseError::CREDENTIAL_STORAGE_FAILED);
        }

        utils::Logger::info(std::format("Database instance registered successfully: {} (ID: {})", config.name, instanceId));
        return instanceId;

    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Failed to register database instance: {}", e.what()));
        return std::unexpected(DatabaseError::DATABASE_ERROR);
    }
}

std::expected<DatabaseInstance, DatabaseError> DatabaseInstanceManager::getInstance(int instanceId) {
    if (!initialized_) {
        return std::unexpected(DatabaseError::DATABASE_ERROR);
    }

    try {
        auto connectionResult = connectionManager_->getConnection();
        if (!connectionResult.has_value()) {
            return std::unexpected(DatabaseError::DATABASE_ERROR);
        }
        auto connection = connectionResult.value();

        std::string query = R"(
            SELECT id, name, host, port, database_name, username, use_ssl, max_connections, created_at, updated_at, active, metadata
            FROM database_instances 
            WHERE id = $1
        )";

        std::vector<std::string> queryParams = {std::to_string(instanceId)};
        auto result = connection->executeQuery(query, std::span<const std::string>(queryParams));
        if (result.empty()) {
            return std::unexpected(DatabaseError::INSTANCE_NOT_FOUND);
        }

        return rowToDatabaseInstance(result[0]);

    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Failed to get database instance: {}", e.what()));
        return std::unexpected(DatabaseError::DATABASE_ERROR);
    }
}

std::expected<DatabaseInstance, DatabaseError> DatabaseInstanceManager::getInstanceByName(const std::string& name) {
    if (!initialized_) {
        return std::unexpected(DatabaseError::DATABASE_ERROR);
    }

    try {
        auto connectionResult = connectionManager_->getConnection();
        if (!connectionResult.has_value()) {
            return std::unexpected(DatabaseError::DATABASE_ERROR);
        }
        auto connection = connectionResult.value();

        std::string query = R"(
            SELECT id, name, host, port, database_name, username, use_ssl, max_connections, created_at, updated_at, active, metadata
            FROM database_instances 
            WHERE name = $1
        )";

        std::vector<std::string> queryParams = {name};
        auto result = connection->executeQuery(query, std::span<const std::string>(queryParams));
        if (result.empty()) {
            return std::unexpected(DatabaseError::INSTANCE_NOT_FOUND);
        }

        return rowToDatabaseInstance(result[0]);

    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Failed to get database instance by name: {}", e.what()));
        return std::unexpected(DatabaseError::DATABASE_ERROR);
    }
}

std::expected<std::vector<DatabaseInstance>, DatabaseError> DatabaseInstanceManager::listInstances() {
    if (!initialized_) {
        return std::unexpected(DatabaseError::DATABASE_ERROR);
    }

    try {
        auto connectionResult = connectionManager_->getConnection();
        if (!connectionResult.has_value()) {
            return std::unexpected(DatabaseError::DATABASE_ERROR);
        }
        auto connection = connectionResult.value();

        std::string query = R"(
            SELECT id, name, host, port, database_name, username, use_ssl, max_connections, created_at, updated_at, active, metadata
            FROM database_instances 
            ORDER BY created_at DESC
        )";

        auto result = connection->executeQuery(query);
        
        std::vector<DatabaseInstance> instances;
        instances.reserve(result.size());
        
        for (const auto& row : result) {
            instances.push_back(rowToDatabaseInstance(row));
        }

        return instances;

    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Failed to list database instances: {}", e.what()));
        return std::unexpected(DatabaseError::DATABASE_ERROR);
    }
}

bool DatabaseInstanceManager::isValidDatabaseConfig(const DatabaseConfig& config) {
    // Validate name (3-50 characters, alphanumeric with hyphens and underscores)
    if (config.name.length() < 3 || config.name.length() > 50) {
        return false;
    }
    
    std::regex namePattern("^[a-zA-Z0-9][a-zA-Z0-9_-]*[a-zA-Z0-9]$");
    if (!std::regex_match(config.name, namePattern)) {
        return false;
    }

    // Validate host
    if (config.host.empty() || config.host.length() > 255) {
        return false;
    }

    // Validate port
    if (config.port < 1 || config.port > 65535) {
        return false;
    }

    // Validate database name
    if (config.databaseName.empty() || config.databaseName.length() > 63) {
        return false;
    }

    // Validate username
    if (config.username.empty() || config.username.length() > 63) {
        return false;
    }

    // Validate password
    if (config.password.empty() || config.password.length() > 255) {
        return false;
    }

    // Validate max connections
    if (config.maxConnections < 1 || config.maxConnections > 1000) {
        return false;
    }

    return true;
}

bool DatabaseInstanceManager::instanceNameExists(const std::string& name) {
    try {
        auto connectionResult = connectionManager_->getConnection();
        if (!connectionResult.has_value()) {
            return false;
        }
        auto connection = connectionResult.value();

        std::string query = "SELECT COUNT(*) as count FROM database_instances WHERE name = $1";
        std::vector<std::string> queryParams = {name};
        auto result = connection->executeQuery(query, std::span<const std::string>(queryParams));

        return !result.empty() && !result[0].empty() && std::stoi(result[0][0]) > 0;

    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Failed to check database instance name existence: {}", e.what()));
        return false;
    }
}

bool DatabaseInstanceManager::storeCredentials(int instanceId, const std::string& username, const std::string& password) {
    std::string usernameKey = std::format("db_instance_{}_username", instanceId);
    std::string passwordKey = std::format("db_instance_{}_password", instanceId);

    return credentialStore_->storeCredential(usernameKey, username) &&
           credentialStore_->storeCredential(passwordKey, password);
}

std::pair<std::string, std::string> DatabaseInstanceManager::getCredentials(int instanceId) {
    std::string usernameKey = std::format("db_instance_{}_username", instanceId);
    std::string passwordKey = std::format("db_instance_{}_password", instanceId);

    std::string username = credentialStore_->getCredential(usernameKey);
    std::string password = credentialStore_->getCredential(passwordKey);

    return {username, password};
}

DatabaseInstance DatabaseInstanceManager::rowToDatabaseInstance(const std::vector<std::string>& row) {
    DatabaseInstance instance;

    // Expecting columns: id, name, host, port, database_name, username, use_ssl, max_connections, created_at, updated_at, active, metadata
    if (row.size() >= 12) {
        instance.id = std::stoi(row[0]);
        instance.name = row[1];
        instance.host = row[2];
        instance.port = std::stoi(row[3]);
        instance.databaseName = row[4];
        instance.username = row[5];
        instance.useSSL = (row[6] == "t" || row[6] == "true");
        instance.maxConnections = std::stoi(row[7]);
        // Skip created_at (row[8]) and updated_at (row[9]) for now
        instance.active = (row[10] == "t" || row[10] == "true");

        // Parse metadata JSON
        try {
            instance.metadata = nlohmann::json::parse(row[11]);
        } catch (const std::exception&) {
            instance.metadata = nlohmann::json::object();
        }
    }

    // Parse timestamps (simplified - in real implementation you'd parse ISO strings)
    instance.createdAt = std::chrono::system_clock::now();
    instance.updatedAt = std::chrono::system_clock::now();

    return instance;
}

std::expected<std::shared_ptr<dbservice::core::Connection>, DatabaseError> DatabaseInstanceManager::getConnection(int applicationId, int instanceId) {
    if (!initialized_) {
        return std::unexpected(DatabaseError::DATABASE_ERROR);
    }

    try {
        // Get database instance
        auto instanceResult = getInstance(instanceId);
        if (!instanceResult) {
            return std::unexpected(instanceResult.error());
        }

        auto instance = instanceResult.value();
        if (!instance.active) {
            utils::Logger::warning(std::format("Database instance {} is not active", instanceId));
            return std::unexpected(DatabaseError::INSTANCE_NOT_FOUND);
        }

        // Get credentials
        auto [username, password] = getCredentials(instanceId);
        if (username.empty() || password.empty()) {
            utils::Logger::error(std::format("Failed to retrieve credentials for database instance {}", instanceId));
            return std::unexpected(DatabaseError::INVALID_CREDENTIALS);
        }

        // Create connection string
        std::string connectionString = createConnectionString(instance, username, password);

        // Create new connection (simplified - in real implementation you'd use a connection pool per instance)
        auto connection = std::make_shared<dbservice::core::Connection>(connectionString);
        if (!connection->open()) {
            utils::Logger::error(std::format("Failed to connect to database instance {}", instanceId));
            return std::unexpected(DatabaseError::CONNECTION_FAILED);
        }

        utils::Logger::info(std::format("Connection established for application {} to database instance {}", applicationId, instanceId));
        return connection;

    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Failed to get connection: {}", e.what()));
        return std::unexpected(DatabaseError::DATABASE_ERROR);
    }
}

std::expected<void, DatabaseError> DatabaseInstanceManager::updateCredentials(int instanceId, const std::string& username, const std::string& password) {
    if (!initialized_) {
        return std::unexpected(DatabaseError::DATABASE_ERROR);
    }

    if (username.empty() || password.empty()) {
        return std::unexpected(DatabaseError::INVALID_CREDENTIALS);
    }

    try {
        auto connectionResult = connectionManager_->getConnection();
        if (!connectionResult.has_value()) {
            return std::unexpected(DatabaseError::DATABASE_ERROR);
        }
        auto connection = connectionResult.value();

        // Check if instance exists
        std::string checkQuery = "SELECT id FROM database_instances WHERE id = $1";
        std::vector<std::string> checkParams = {std::to_string(instanceId)};
        auto result = connection->executeQuery(checkQuery, std::span<const std::string>(checkParams));

        if (result.empty()) {
            return std::unexpected(DatabaseError::INSTANCE_NOT_FOUND);
        }

        // Update username in database
        std::string updateQuery = R"(
            UPDATE database_instances
            SET username = $1, updated_at = NOW()
            WHERE id = $2
        )";

        std::vector<std::string> updateParams = {username, std::to_string(instanceId)};
        connection->executeNonQuery(updateQuery, std::span<const std::string>(updateParams));

        // Store new credentials securely
        if (!storeCredentials(instanceId, username, password)) {
            utils::Logger::error("Failed to store updated credentials");
            return std::unexpected(DatabaseError::CREDENTIAL_STORAGE_FAILED);
        }

        utils::Logger::info(std::format("Credentials updated for database instance: {}", instanceId));
        return {};

    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Failed to update credentials: {}", e.what()));
        return std::unexpected(DatabaseError::DATABASE_ERROR);
    }
}

std::expected<bool, DatabaseError> DatabaseInstanceManager::testConnection(int instanceId) {
    if (!initialized_) {
        return std::unexpected(DatabaseError::DATABASE_ERROR);
    }

    try {
        // Get database instance
        auto instanceResult = getInstance(instanceId);
        if (!instanceResult) {
            return std::unexpected(instanceResult.error());
        }

        auto instance = instanceResult.value();

        // Get credentials
        auto [username, password] = getCredentials(instanceId);
        if (username.empty() || password.empty()) {
            return std::unexpected(DatabaseError::INVALID_CREDENTIALS);
        }

        // Create connection string
        std::string connectionString = createConnectionString(instance, username, password);

        // Test connection
        auto testConnection = std::make_shared<dbservice::core::Connection>(connectionString);
        bool connected = testConnection->open();

        if (connected) {
            // Test with a simple query
            try {
                auto result = testConnection->executeQuery("SELECT 1 as test");
                testConnection->close();
                return !result.empty();
            } catch (const std::exception&) {
                testConnection->close();
                return false;
            }
        }

        return false;

    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Failed to test connection: {}", e.what()));
        return std::unexpected(DatabaseError::DATABASE_ERROR);
    }
}

std::string DatabaseInstanceManager::createConnectionString(const DatabaseInstance& instance, const std::string& username, const std::string& password) {
    std::ostringstream oss;
    oss << "postgresql://" << username << ":" << password << "@" << instance.host << ":" << instance.port << "/" << instance.databaseName;

    if (instance.useSSL) {
        oss << "?sslmode=require&connect_timeout=10";
    } else {
        oss << "?sslmode=disable&connect_timeout=10";
    }

    return oss.str();
}

} // namespace dbservice::service
