# Build-AuthService.psm1 - Module for building the auth service project

<#
.SYNOPSIS
    Provides functionality for building the auth service project.

.DESCRIPTION
    This module handles the build process for the auth service project,
    including source code synchronization, remote build, and deployment.
    Based on the original batch script functionality.

.NOTES
    File Name      : Build-AuthService.psm1
    Author         : Auth Service Deployment Team
    Prerequisite   : PowerShell 5.1 or later
    Copyright      : (c) 2025 Augment
#>

# Import required modules
try {
    # Import common module
    if (-not (Get-Module -Name "Common")) {
        Import-Module -Name "$PSScriptRoot\..\Common.psm1" -Force -ErrorAction Stop
    }

    # Import Logger module
    if (-not (Get-Module -Name "Logger")) {
        Import-Module -Name "$PSScriptRoot\Logger\Logger.psm1" -Force -ErrorAction Stop
    }

    # Import SSHManager module
    if (-not (Get-Module -Name "SSHManager")) {
        Write-Host "SSHManager module not loaded. Attempting to load..." -ForegroundColor Yellow
        Import-Module -Name "$PSScriptRoot\SSHManager.psm1" -Force -ErrorAction Stop
        Write-Host "SSHManager module loaded successfully." -ForegroundColor Green
    }
} catch {
    Write-Log -Message "Error loading required modules: $_" -Level "Error" -Component "Build-AuthService"
}

# Helper function to invoke remote commands
function Invoke-RemoteCommand {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$Command,

        [Parameter(Mandatory = $false)]
        [switch]$Silent,

        [Parameter(Mandatory = $false)]
        [string]$HostName,

        [Parameter(Mandatory = $false)]
        [string]$User,

        [Parameter(Mandatory = $false)]
        [string]$KeyPath,

        [Parameter(Mandatory = $false)]
        [int]$Port = 22
    )

    # Use configuration values if not provided
    $sshHost = if ($HostName) { $HostName } else { $script:Config.ssh.host }
    $sshUser = if ($User) { $User } else { $script:Config.ssh.username }
    $sshKeyPath = if ($KeyPath) { $KeyPath } else { $script:Config.ssh.key_path }
    $sshPort = if ($Port -ne 22) { $Port } else { $script:Config.ssh.port }

    Write-Host "[Invoke-RemoteCommand] SSH Host: $sshHost" -ForegroundColor Gray
    Write-Host "[Invoke-RemoteCommand] SSH User: $sshUser" -ForegroundColor Gray
    Write-Host "[Invoke-RemoteCommand] SSH Port: $sshPort" -ForegroundColor Gray
    Write-Host "[Invoke-RemoteCommand] SSH Key Path: $sshKeyPath" -ForegroundColor Gray

    try {
        $sshCommand = "ssh -i `"$sshKeyPath`" -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -o LogLevel=ERROR -o BatchMode=yes $sshUser@$sshHost $Command"
        Write-Log -Message "Executing SSH command: $sshCommand" -Level "Info" -Component "SSH"
        
        $result = Invoke-Expression $sshCommand
        
        if ($LASTEXITCODE -eq 0) {
            if (-not $Silent) {
                Write-Host $result -ForegroundColor Green
            }
            return $result
        } else {
            Write-Host "SSH command failed with exit code: $LASTEXITCODE" -ForegroundColor Red
            Write-Host "Command: $sshCommand" -ForegroundColor Red
            if ($result) {
                Write-Host "Output: $result" -ForegroundColor Red
            }
            return $null
        }
    } catch {
        Write-Host "Error executing SSH command: $_" -ForegroundColor Red
        Write-Log -Message "Error executing SSH command: $_" -Level "Error" -Component "SSH"
        return $null
    }
}

function Build-AuthService {
    [CmdletBinding()]
    param()

    Write-Log -Message "Build-Project started." -Level "Info" -Component "Build-Project"

    # Display current configuration
    Write-Host "Current Configuration:" -ForegroundColor Cyan
    Write-Host "Project: $($script:Config.project.name)" -ForegroundColor White
    Write-Host "Environment: " -ForegroundColor White
    Write-Host "Server: $($script:Config.ssh.host)" -ForegroundColor White
    Write-Host "Source Directory: $($script:Config.project.local_source_dir)" -ForegroundColor White
    Write-Host "SSH Host: $($script:Config.ssh.host)" -ForegroundColor White
    Write-Host "SSH User: $($script:Config.ssh.username)" -ForegroundColor White
    Write-Host "SSH Port: $($script:Config.ssh.port)" -ForegroundColor White
    Write-Host "SSH Key Path: $($script:Config.ssh.key_path)" -ForegroundColor White

    # Validate source directory
    Write-Host "Starting build process..." -ForegroundColor Yellow
    $sourceDir = $script:Config.project.local_source_dir
    
    if (-not (Test-Path $sourceDir)) {
        Write-Host "Error: Source directory not found: $sourceDir" -ForegroundColor Red
        Write-Log -Message "Source directory not found: $sourceDir" -Level "Error" -Component "Build-Project"
        return $false
    }
    
    Write-Host "Source directory verified: $sourceDir" -ForegroundColor Green

    # Test SSH connection
    Write-Host "Testing SSH connection to $($script:Config.ssh.host)..." -ForegroundColor Yellow
    $testResult = Invoke-RemoteCommand -Command "echo 'SSH connection successful'" -Silent
    
    if (-not $testResult) {
        Write-Host "Error: SSH connection failed." -ForegroundColor Red
        return $false
    }
    
    Write-Host "SSH connection successful." -ForegroundColor Green

    # Create remote build directory
    $remoteBuildDir = $script:Config.project.remote_build_dir
    Write-Host "Creating remote build directory: $remoteBuildDir" -ForegroundColor Yellow
    $createDirResult = Invoke-RemoteCommand -Command "mkdir -p $remoteBuildDir" -Silent
    
    if ($createDirResult -eq $null) {
        Write-Host "Warning: Could not verify remote directory creation, but continuing..." -ForegroundColor Yellow
    } else {
        Write-Host "Remote build directory created/verified." -ForegroundColor Green
    }

    # Synchronize source code to remote server
    Write-Host "Synchronizing source code to remote server..." -ForegroundColor Yellow
    
    try {
        Write-Host "Using SCP for direct file transfer on Windows..." -ForegroundColor Cyan
        
        # Use SCP to transfer files
        $scpCommand = "scp -r -i `"$($script:Config.ssh.key_path)`" -o StrictHostKeyChecking=no `"$sourceDir\*`" $($script:Config.ssh.username)@$($script:Config.ssh.host):$remoteBuildDir/source/"
        
        Write-Host "Executing: $scpCommand" -ForegroundColor Gray
        $scpResult = Invoke-Expression $scpCommand
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "Source code synchronized successfully." -ForegroundColor Green
        } else {
            Write-Host "Error: SCP file transfer failed. Cannot proceed with build." -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "Error during file transfer: $_" -ForegroundColor Red
        Write-Log -Message "Error during file transfer: $_" -Level "Error" -Component "Build-Project"
        return $false
    }

    # Build the project on remote server
    Write-Host "Building project on remote server..." -ForegroundColor Yellow
    
    $buildCommands = @(
        "cd $remoteBuildDir",
        "mkdir -p build",
        "cd build", 
        "cmake ../source",
        "make -j$(nproc)"
    )
    
    $buildCommand = $buildCommands -join " && "
    Write-Host "Executing build command: $buildCommand" -ForegroundColor Gray
    
    $buildResult = Invoke-RemoteCommand -Command $buildCommand
    
    if ($buildResult -eq $null) {
        Write-Host "Error: Build failed on remote server." -ForegroundColor Red
        return $false
    }
    
    Write-Host "Build completed successfully!" -ForegroundColor Green
    Write-Log -Message "Build completed successfully" -Level "Info" -Component "Build-Project"
    
    return $true
}

# Export the function
Export-ModuleMember -Function Build-AuthService
