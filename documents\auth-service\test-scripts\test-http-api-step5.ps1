# OAuth 2.0 HTTP API Endpoints Testing Script - Step 5
# Tests complete OAuth 2.0 REST API implementation with JWT token management

param(
    [string]$Environment = "development",
    [string]$Server = "dev.chcit.org",
    [int]$Port = 8082,
    [switch]$Verbose
)

Write-Host "=== OAuth 2.0 HTTP API Testing (Step 5) ===" -ForegroundColor Cyan
Write-Host "Environment: $Environment" -ForegroundColor Yellow
Write-Host "Server: $Server" -ForegroundColor Yellow
Write-Host "Port: $Port" -ForegroundColor Yellow
Write-Host ""

# Test configuration
$baseUrl = "http://${Server}:${Port}"
$testUser = @{
    username = "test_api_user"
    email = "<EMAIL>"
    password = "TestAPI123!"
}

# Test results tracking
$testResults = @{
    total = 0
    passed = 0
    failed = 0
    errors = @()
}

function Write-TestResult {
    param(
        [string]$TestName,
        [bool]$Success,
        [string]$Message = ""
    )
    
    $testResults.total++
    
    if ($Success) {
        $testResults.passed++
        Write-Host "✅ $TestName" -ForegroundColor Green
        if ($Message -and $Verbose) {
            Write-Host "   $Message" -ForegroundColor Gray
        }
    } else {
        $testResults.failed++
        $testResults.errors += "$TestName - $Message"
        Write-Host "❌ $TestName" -ForegroundColor Red
        if ($Message) {
            Write-Host "   Error: $Message" -ForegroundColor Red
        }
    }
}

function Test-HttpEndpoint {
    param(
        [string]$Url,
        [string]$Method = "GET",
        [hashtable]$Headers = @{},
        [string]$Body = $null
    )
    
    try {
        $params = @{
            Uri = $Url
            Method = $Method
            Headers = $Headers
            UseBasicParsing = $true
            TimeoutSec = 30
        }
        
        if ($Body) {
            $params.Body = $Body
            $params.ContentType = "application/json"
        }
        
        $response = Invoke-WebRequest @params
        return @{
            Success = $true
            StatusCode = $response.StatusCode
            Content = $response.Content
            Headers = $response.Headers
        }
    } catch {
        return @{
            Success = $false
            Error = $_.Exception.Message
            StatusCode = if ($_.Exception.Response) { $_.Exception.Response.StatusCode } else { 0 }
        }
    }
}

# Test 1: Health Check Endpoint
Write-Host "1. Testing health check endpoint..." -ForegroundColor Yellow
$healthTest = Test-HttpEndpoint -Url "$baseUrl/health" -Method "GET"
Write-TestResult -TestName "Health Check Endpoint" -Success $healthTest.Success -Message $healthTest.Error

if ($healthTest.Success) {
    try {
        $healthResponse = $healthTest.Content | ConvertFrom-Json
        Write-TestResult -TestName "Health Response Format" -Success ($healthResponse.status -eq "healthy") -Message "Status: $($healthResponse.status)"
        Write-TestResult -TestName "Service Identification" -Success ($healthResponse.service -eq "auth-service") -Message "Service: $($healthResponse.service)"
        Write-TestResult -TestName "OAuth2 Endpoints Listed" -Success ($healthResponse.oauth2_endpoints.Count -eq 4) -Message "Endpoints: $($healthResponse.oauth2_endpoints.Count)"
        
        if ($Verbose) {
            Write-Host "   Available endpoints:" -ForegroundColor Gray
            foreach ($endpoint in $healthResponse.oauth2_endpoints) {
                Write-Host "     - $endpoint" -ForegroundColor Gray
            }
        }
    } catch {
        Write-TestResult -TestName "Health Response Parsing" -Success $false -Message $_.Exception.Message
    }
}

# Test 2: OAuth Token Generation Endpoint
Write-Host "`n2. Testing OAuth token generation endpoint..." -ForegroundColor Yellow

$tokenRequestBody = @{
    username = $testUser.username
    password = $testUser.password
    grant_type = "password"
} | ConvertTo-Json

$tokenTest = Test-HttpEndpoint -Url "$baseUrl/oauth/token" -Method "POST" -Body $tokenRequestBody
Write-TestResult -TestName "Token Generation Request" -Success $tokenTest.Success -Message $tokenTest.Error

$accessToken = $null
$refreshToken = $null

if ($tokenTest.Success) {
    try {
        $tokenResponse = $tokenTest.Content | ConvertFrom-Json
        $accessToken = $tokenResponse.access_token
        $refreshToken = $tokenResponse.refresh_token
        
        Write-TestResult -TestName "Access Token Received" -Success ($null -ne $accessToken) -Message "Token length: $($accessToken.Length)"
        Write-TestResult -TestName "Refresh Token Received" -Success ($null -ne $refreshToken) -Message "Token length: $($refreshToken.Length)"
        Write-TestResult -TestName "Token Type" -Success ($tokenResponse.token_type -eq "Bearer") -Message "Type: $($tokenResponse.token_type)"
        Write-TestResult -TestName "Expires In" -Success ($tokenResponse.expires_in -gt 0) -Message "Expires: $($tokenResponse.expires_in) seconds"
        Write-TestResult -TestName "Scope" -Success ($tokenResponse.scope -eq "read write") -Message "Scope: $($tokenResponse.scope)"
        
        if ($Verbose) {
            Write-Host "   Access Token: $($accessToken.Substring(0, [Math]::Min(50, $accessToken.Length)))..." -ForegroundColor Gray
            Write-Host "   Refresh Token: $($refreshToken.Substring(0, [Math]::Min(50, $refreshToken.Length)))..." -ForegroundColor Gray
        }
    } catch {
        Write-TestResult -TestName "Token Response Parsing" -Success $false -Message $_.Exception.Message
    }
}

# Test 3: OAuth Token Validation Endpoint
Write-Host "`n3. Testing OAuth token validation endpoint..." -ForegroundColor Yellow

if ($accessToken) {
    $headers = @{
        "Authorization" = "Bearer $accessToken"
    }
    
    $validateTest = Test-HttpEndpoint -Url "$baseUrl/oauth/validate" -Method "POST" -Headers $headers
    Write-TestResult -TestName "Token Validation Request" -Success $validateTest.Success -Message $validateTest.Error
    
    if ($validateTest.Success) {
        try {
            $validationResponse = $validateTest.Content | ConvertFrom-Json
            Write-TestResult -TestName "Token Valid" -Success ($validationResponse.valid -eq $true) -Message "Valid: $($validationResponse.valid)"
            Write-TestResult -TestName "User ID Returned" -Success ($validationResponse.user_id -gt 0) -Message "User ID: $($validationResponse.user_id)"
            Write-TestResult -TestName "Scopes Returned" -Success ($validationResponse.scopes.Count -gt 0) -Message "Scopes: $($validationResponse.scopes -join ', ')"
        } catch {
            Write-TestResult -TestName "Validation Response Parsing" -Success $false -Message $_.Exception.Message
        }
    }
} else {
    Write-TestResult -TestName "Token Validation Request" -Success $false -Message "No access token available"
}

# Test 4: OAuth Token Refresh Endpoint
Write-Host "`n4. Testing OAuth token refresh endpoint..." -ForegroundColor Yellow

if ($refreshToken) {
    $refreshRequestBody = @{
        refresh_token = $refreshToken
        grant_type = "refresh_token"
    } | ConvertTo-Json
    
    $refreshTest = Test-HttpEndpoint -Url "$baseUrl/oauth/refresh" -Method "POST" -Body $refreshRequestBody
    Write-TestResult -TestName "Token Refresh Request" -Success $refreshTest.Success -Message $refreshTest.Error
    
    if ($refreshTest.Success) {
        try {
            $refreshResponse = $refreshTest.Content | ConvertFrom-Json
            $newAccessToken = $refreshResponse.access_token
            $newRefreshToken = $refreshResponse.refresh_token
            
            Write-TestResult -TestName "New Access Token" -Success ($null -ne $newAccessToken) -Message "Token length: $($newAccessToken.Length)"
            Write-TestResult -TestName "New Refresh Token" -Success ($null -ne $newRefreshToken) -Message "Token length: $($newRefreshToken.Length)"
            Write-TestResult -TestName "Token Type" -Success ($refreshResponse.token_type -eq "Bearer") -Message "Type: $($refreshResponse.token_type)"
            
            # Test new access token validation
            $newHeaders = @{
                "Authorization" = "Bearer $newAccessToken"
            }
            
            $newValidateTest = Test-HttpEndpoint -Url "$baseUrl/oauth/validate" -Method "POST" -Headers $newHeaders
            Write-TestResult -TestName "New Token Validation" -Success $newValidateTest.Success -Message $newValidateTest.Error
            
        } catch {
            Write-TestResult -TestName "Refresh Response Parsing" -Success $false -Message $_.Exception.Message
        }
    }
} else {
    Write-TestResult -TestName "Token Refresh Request" -Success $false -Message "No refresh token available"
}

# Test 5: OAuth Token Revocation Endpoint
Write-Host "`n5. Testing OAuth token revocation endpoint..." -ForegroundColor Yellow

if ($accessToken) {
    $revokeRequestBody = @{
        token = $accessToken
    } | ConvertTo-Json
    
    $revokeTest = Test-HttpEndpoint -Url "$baseUrl/oauth/revoke" -Method "POST" -Body $revokeRequestBody
    Write-TestResult -TestName "Token Revocation Request" -Success $revokeTest.Success -Message $revokeTest.Error
    
    if ($revokeTest.Success) {
        Write-TestResult -TestName "Revocation Response" -Success ($revokeTest.StatusCode -eq 200) -Message "Status: $($revokeTest.StatusCode)"
        
        # Test that revoked token is no longer valid
        Start-Sleep -Seconds 1
        
        $headers = @{
            "Authorization" = "Bearer $accessToken"
        }
        
        $revokedValidateTest = Test-HttpEndpoint -Url "$baseUrl/oauth/validate" -Method "POST" -Headers $headers
        # Should fail with revoked token
        Write-TestResult -TestName "Revoked Token Rejection" -Success (-not $revokedValidateTest.Success) -Message "Expected failure"
    }
} else {
    Write-TestResult -TestName "Token Revocation Request" -Success $false -Message "No access token available"
}

# Test 6: Error Handling
Write-Host "`n6. Testing error handling..." -ForegroundColor Yellow

# Test invalid endpoint
$invalidEndpointTest = Test-HttpEndpoint -Url "$baseUrl/oauth/invalid" -Method "POST"
Write-TestResult -TestName "Invalid Endpoint (404)" -Success (-not $invalidEndpointTest.Success) -Message "Expected 404"

# Test invalid token
$invalidHeaders = @{
    "Authorization" = "Bearer invalid.jwt.token"
}
$invalidTokenTest = Test-HttpEndpoint -Url "$baseUrl/oauth/validate" -Method "POST" -Headers $invalidHeaders
Write-TestResult -TestName "Invalid Token Rejection" -Success (-not $invalidTokenTest.Success) -Message "Expected failure"

# Test missing parameters
$emptyBodyTest = Test-HttpEndpoint -Url "$baseUrl/oauth/token" -Method "POST" -Body "{}"
Write-TestResult -TestName "Missing Parameters (400)" -Success (-not $emptyBodyTest.Success) -Message "Expected 400"

# Test Summary
Write-Host "`n=== OAuth 2.0 HTTP API Testing Summary ===" -ForegroundColor Cyan
Write-Host "Total Tests: $($testResults.total)" -ForegroundColor White
Write-Host "Passed: $($testResults.passed)" -ForegroundColor Green
Write-Host "Failed: $($testResults.failed)" -ForegroundColor Red

if ($testResults.failed -gt 0) {
    Write-Host "`nFailed Tests:" -ForegroundColor Red
    foreach ($testError in $testResults.errors) {
        Write-Host "  - $testError" -ForegroundColor Red
    }
}

$successRate = if ($testResults.total -gt 0) { 
    [math]::Round(($testResults.passed / $testResults.total) * 100, 2) 
} else { 0 }

Write-Host "`nSuccess Rate: $successRate%" -ForegroundColor $(if ($successRate -ge 80) { "Green" } elseif ($successRate -ge 60) { "Yellow" } else { "Red" })

if ($successRate -ge 80) {
    Write-Host "`n🎉 OAuth 2.0 HTTP API implementation is working excellently!" -ForegroundColor Green
} elseif ($successRate -ge 60) {
    Write-Host "`n⚠️  OAuth 2.0 HTTP API has some issues that need attention." -ForegroundColor Yellow
} else {
    Write-Host "`n❌ OAuth 2.0 HTTP API needs significant fixes." -ForegroundColor Red
}

Write-Host "`nStep 5: OAuth 2.0 HTTP API Endpoints testing completed." -ForegroundColor Cyan
