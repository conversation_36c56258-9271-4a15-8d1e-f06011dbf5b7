#include "database-service/security/security_manager.hpp"
#include "database-service/security/jwt.hpp"
#include "database-service/core/connection_manager.hpp"
#include "database-service/core/connection.hpp"
#include "database-service/core/transaction.hpp"
#include "database-service/metrics/database_metrics.hpp"
#include "database-service/utils/logger.hpp"
#include "database-service/utils/config_manager.hpp"
#include <format> // C++20 feature
#include <random>
#include <chrono>
#include <algorithm>
#include <openssl/sha.h>
#include <openssl/hmac.h>
#include <openssl/evp.h>
#include <openssl/bio.h>
#include <openssl/buffer.h>
#include <sstream>
#include <iomanip>
#include <pqxx/pqxx>

namespace dbservice::security {

using dbservice::core::ManagedConnection; // For RAII-based connection handling

// Helper function to compute SHA256 hash
std::string sha256(const std::string& input) {
    unsigned char hash[SHA256_DIGEST_LENGTH];
    SHA256_CTX sha256;
    SHA256_Init(&sha256);
    SHA256_Update(&sha256, input.c_str(), input.size());
    SHA256_Final(hash, &sha256);

    std::stringstream ss;
    for (int i = 0; i < SHA256_DIGEST_LENGTH; i++) {
        ss << std::hex << std::setw(2) << std::setfill('0') << (int)hash[i];
    }
    return ss.str();
}

SecurityManager::SecurityManager(std::shared_ptr<core::ConnectionManager> connectionManager)
    : connectionManager_(connectionManager),
      initialized_(false),
      jwtSecret_("change-this-to-a-secure-secret-in-production"),
      accessTokenExpirationSeconds_(3600),
      refreshTokenExpirationSeconds_(86400) {

    // Load configuration
    auto& configManager = utils::ConfigManager::getInstance();
    jwtSecret_ = configManager.getString("security.jwt_secret", jwtSecret_);
    accessTokenExpirationSeconds_ = configManager.getInt("security.token_expiration_seconds", accessTokenExpirationSeconds_);
    refreshTokenExpirationSeconds_ = configManager.getInt("security.refresh_token_expiration_seconds", refreshTokenExpirationSeconds_);
}

SecurityManager::~SecurityManager() {
}

std::expected<void, SecurityError> SecurityManager::initialize() {
    std::lock_guard<std::mutex> lock(mutex_);
    if (initialized_) {
        utils::Logger::info("Security Manager already initialized.");
        return {};
    }

    utils::Logger::info("Initializing security manager...");

    auto usersTableResult = createUsersTable();
    if (!usersTableResult) {
        return std::unexpected(SecurityError{
            SecurityErrorType::InitializationFailed,
            "Failed to create users table: " + usersTableResult.error().message,
            usersTableResult.error().code
        });
    }

    auto permissionsTableResult = createPermissionsTable();
    if (!permissionsTableResult) {
        return std::unexpected(SecurityError{
            SecurityErrorType::InitializationFailed,
            "Failed to create permissions table: " + permissionsTableResult.error().message,
            permissionsTableResult.error().code
        });
    }

    auto refreshTokensTableResult = createRefreshTokensTable();
    if (!refreshTokensTableResult) {
        return std::unexpected(SecurityError{
            SecurityErrorType::InitializationFailed,
            "Failed to create refresh tokens table: " + refreshTokensTableResult.error().message,
            refreshTokensTableResult.error().code
        });
    }

    // cleanupExpiredRefreshTokens logs its own errors and doesn't return std::expected
    // If it needs to be critical for initialization, it should also return std::expected
    cleanupExpiredRefreshTokens();

    initialized_ = true;
    utils::Logger::info("Security manager initialized successfully");
    return {};
}

std::expected<TokenPair, SecurityError> SecurityManager::authenticate(const std::string& username, const std::string& password) {
    if (!initialized_) {
        auto initResult = initialize(); // initialize() now returns std::expected<void, SecurityError>
        if (!initResult) {
            std::string errMsg = "Authentication failed: Security Manager not initialized: " + initResult.error().message;
            utils::Logger::error(errMsg);
            return std::unexpected(SecurityError{
                SecurityErrorType::InvalidCredentials,
                errMsg,
                initResult.error().code
            });
        }
    }

    ManagedConnection conn(*connectionManager_);
    if (!conn) {
        std::string errMsg = std::format("Authentication failed for user {}: Failed to acquire database connection", username);
        utils::Logger::error(errMsg);
        // No specific metric for DB connection failure here, but auth fails.
        return std::unexpected(SecurityError{SecurityErrorType::DatabaseError, errMsg});
    }

    try {
        // Get user's password hash
        std::string queryUser = "SELECT password_hash FROM users WHERE username = $1 AND active = true";
        std::vector<std::string> params = {username};
        auto dbResult = conn->executeQuery(queryUser, params);

        if (dbResult.empty() || dbResult[0].empty()) {
            std::string errMsg = std::format("Authentication failed: User {} not found or inactive", username);
            utils::Logger::warning(errMsg);
            metrics::DatabaseMetrics::getInstance().recordAuthenticationMetric(false);
            return std::unexpected(SecurityError{SecurityErrorType::InvalidCredentials, errMsg});
        }
        std::string storedHash = dbResult[0][0];

        // Verify password (assuming verifyPassword is refactored to return std::expected<void, SecurityError>)
        auto verifyResult = verifyPassword(password, storedHash);
        if (!verifyResult) {
            std::string errMsg = std::format("Authentication failed: Invalid password for user {}. Reason: {}", username, verifyResult.error().message);
            utils::Logger::warning(errMsg);
            metrics::DatabaseMetrics::getInstance().recordAuthenticationMetric(false);
            return std::unexpected(SecurityError{
                SecurityErrorType::InvalidCredentials,
                errMsg,
                verifyResult.error().code
            });
        }

        // Update last login time (best effort, non-critical for auth success)
        try {
            std::string updateQuery = "UPDATE users SET last_login = NOW() WHERE username = $1";
            std::vector<std::string> updateParams = {username};
            int updateOpResult = conn->executeNonQuery(updateQuery, updateParams);
            if (updateOpResult < 0) {
                utils::Logger::warning(std::format("Failed to update last login time for user {}. Proceeding with authentication.", username));
            }
        } catch (const pqxx::sql_error& e_update) {
            utils::Logger::warning(std::format("SQL error updating last login for user {}: {}. Proceeding.", username, e_update.what()));
        } catch (const std::exception& e_update) {
            utils::Logger::warning(std::format("Exception updating last login for user {}: {}. Proceeding.", username, e_update.what()));
        }

        // Generate token pair (assuming generateTokenPair is refactored to return std::expected<TokenPair, SecurityError>)
        auto tokenPairResult = generateTokenPair(username);
        if (!tokenPairResult) {
            std::string errMsg = std::format("Authentication succeeded for user {} but failed to generate token pair. Reason: {}", username, tokenPairResult.error().message);
            utils::Logger::error(errMsg);
            metrics::DatabaseMetrics::getInstance().recordAuthenticationMetric(false); // Overall operation failed
            return std::unexpected(SecurityError{
                SecurityErrorType::TokenGenerationFailed,
                errMsg,
                tokenPairResult.error().code
            });
        }

        metrics::DatabaseMetrics::getInstance().recordAuthenticationMetric(true);
        utils::Logger::info(std::format("User {} authenticated successfully", username));
        return tokenPairResult.value();

    } catch (const pqxx::sql_error& e) {
        std::string errMsg = std::format("SQL error during authentication for user {}: {}", username, e.what());
        utils::Logger::error(errMsg);
        metrics::DatabaseMetrics::getInstance().recordAuthenticationMetric(false);
        return std::unexpected(SecurityError{SecurityErrorType::DatabaseError, errMsg});
    } catch (const std::exception& e) {
        std::string errMsg = std::format("Generic exception during authentication for user {}: {}", username, e.what());
        utils::Logger::error(errMsg);
        metrics::DatabaseMetrics::getInstance().recordAuthenticationMetric(false);
        return std::unexpected(SecurityError{SecurityErrorType::InvalidCredentials, errMsg});
    }
}

std::expected<std::string, SecurityError> SecurityManager::validateToken(const std::string& token) {
    if (!initialized_) {
        auto initResult = initialize();
        if (!initResult) {
            std::string errMsg = "Failed to validate token: Security Manager not initialized and initialization failed.";
            utils::Logger::error(errMsg + " Error: " + initResult.error().message);
            return std::unexpected(SecurityError{SecurityErrorType::InitializationFailed, errMsg, initResult.error().code});
        }
    }

    if (jwtSecret_ == "change-this-to-a-secure-secret-in-production" || jwtSecret_.empty()) {
        std::string errMsg = "JWT secret is not configured securely. Cannot validate tokens.";
        utils::Logger::critical(errMsg);
        return std::unexpected(SecurityError{SecurityErrorType::ConfigurationError, errMsg});
    }

    std::unordered_map<std::string, std::string> payload;
    try {
        if (!JWT::verify(token, jwtSecret_, payload)) {
            std::string errMsg = "Invalid or expired JWT token (JWT::verify returned false).";
            utils::Logger::warning(errMsg);
            return std::unexpected(SecurityError{SecurityErrorType::TokenValidationFailed, errMsg});
        }
    } catch (const std::exception& e_jwt) {
        // Catch exceptions specifically from JWT::verify (e.g., malformed token)
        std::string errMsg = std::format("Exception during JWT token verification: {}", e_jwt.what());
        utils::Logger::warning(errMsg);
        return std::unexpected(SecurityError{SecurityErrorType::TokenValidationFailed, errMsg});
    }

    auto tokenTypeIt = payload.find("type");
    if (tokenTypeIt == payload.end() || tokenTypeIt->second != "access") {
        std::string errMsg = "Token validation failed: Not an access token.";
        utils::Logger::warning(errMsg);
        return std::unexpected(SecurityError{SecurityErrorType::TokenValidationFailed, errMsg});
    }

    auto usernameIt = payload.find("sub");
    if (usernameIt == payload.end() || usernameIt->second.empty()) {
        std::string errMsg = "Token validation failed: Token does not contain a subject (username) or username is empty.";
        utils::Logger::warning(errMsg);
        return std::unexpected(SecurityError{SecurityErrorType::TokenValidationFailed, errMsg});
    }
    std::string username = usernameIt->second;

    ManagedConnection conn(*connectionManager_);
    if (!conn) {
        std::string errMsg = std::format("Failed to validate token for user {}: Could not acquire database connection", username);
        utils::Logger::error(errMsg);
        return std::unexpected(SecurityError{SecurityErrorType::DatabaseError, errMsg});
    }

    try {
        std::string query = "SELECT 1 FROM users WHERE username = $1 AND active = true";
        std::vector<std::string> queryParams = {username};
        auto dbResult = conn->executeQuery(query, queryParams);

        if (dbResult.empty()) {
            std::string errMsg = std::format("Token validation failed: User '{}' not found, inactive, or database error.", username);
            utils::Logger::warning(errMsg);
            return std::unexpected(SecurityError{SecurityErrorType::TokenValidationFailed, errMsg});
        }

        utils::Logger::debug(std::format("Token validated successfully for user {}", username));
        return username; // Success, return username
    } catch (const pqxx::sql_error& e) {
        std::string errMsg = std::format("SQL error during token validation for user {}: {}", username, e.what());
        utils::Logger::error(errMsg);
        return std::unexpected(SecurityError{SecurityErrorType::DatabaseError, errMsg});
    } catch (const std::exception& e) {
        std::string errMsg = std::format("Generic exception during token validation for user {}: {}", username, e.what());
        utils::Logger::error(errMsg);
        return std::unexpected(SecurityError{SecurityErrorType::Unknown, errMsg});
    }
}

bool SecurityManager::validateJwtToken(const std::string& token, std::unordered_map<std::string, std::string>& payload) {
    if (!initialized_) {
        auto initResult = initialize();
        if (!initResult) {
            utils::Logger::error("Failed to validate JWT token: Security Manager not initialized");
            return false;
        }
    }

    if (jwtSecret_ == "change-this-to-a-secure-secret-in-production" || jwtSecret_.empty()) {
        utils::Logger::critical("JWT secret is not configured securely. Cannot validate tokens.");
        return false;
    }

    try {
        return JWT::verify(token, jwtSecret_, payload);
    } catch (const std::exception& e) {
        utils::Logger::warning(std::format("Exception during JWT token validation: {}", e.what()));
        return false;
    }
}

std::expected<std::unordered_map<std::string, std::string>, SecurityError> SecurityManager::getUserInfo(const std::string& token) {
    std::unordered_map<std::string, std::string> userInfo;

    if (!initialized_) {
        auto initResult = initialize();
        if (!initResult) {
            std::string errMsg = "Failed to get user info: Security Manager not initialized and initialization failed.";
            utils::Logger::error(errMsg + " Error: " + initResult.error().message);
            return std::unexpected(SecurityError{SecurityErrorType::InitializationFailed, errMsg, initResult.error().code});
        }
    }

    try {
        // Verify JWT token
        std::unordered_map<std::string, std::string> payload;
        if (!JWT::verify(token, jwtSecret_, payload)) {
            std::string errMsg = "Invalid or expired JWT token";
            utils::Logger::warning(errMsg);
            return std::unexpected(SecurityError{SecurityErrorType::TokenValidationFailed, errMsg});
        }

        // Get username from token
        auto usernameIt = payload.find("sub");
        if (usernameIt == payload.end()) {
            std::string errMsg = "Token does not contain a subject (username)";
            utils::Logger::warning(errMsg);
            return std::unexpected(SecurityError{SecurityErrorType::TokenValidationFailed, errMsg});
        }

        std::string username = usernameIt->second;

        // Get user info
        std::string query = "SELECT username, email, is_admin, created_at FROM users WHERE username = $1";
        std::vector<std::string> userParams = {username};
        auto result = connectionManager_->executeQuery(query, userParams);

        if (!result) {
            std::string errMsg = std::format("Database error getting user info for {}: {}", username, result.error());
            utils::Logger::error(errMsg);
            return std::unexpected(SecurityError{SecurityErrorType::DatabaseError, errMsg});
        }

        if (result->empty() || (*result)[0].size() < 4) {
            std::string errMsg = std::format("User info not found for {}", username);
            utils::Logger::warning(errMsg);
            return std::unexpected(SecurityError{SecurityErrorType::UserNotFound, errMsg});
        }

        userInfo["username"] = (*result)[0][0];
        userInfo["email"] = (*result)[0][1];
        userInfo["is_admin"] = (*result)[0][2];
        userInfo["created_at"] = (*result)[0][3];

        return userInfo;
    } catch (const std::exception& e) {
        std::string errMsg = std::format("Exception during user info retrieval: {}", e.what());
        utils::Logger::error(errMsg);
        return std::unexpected(SecurityError{SecurityErrorType::Unknown, errMsg});
    }
}

std::expected<bool, SecurityError> SecurityManager::hasPermission(const std::string& username, const std::string& permission) {
    if (!initialized_) {
        auto initResult = initialize();
        if (!initResult) {
            std::string errMsg = "Failed to check permission: Security Manager not initialized and initialization failed.";
            utils::Logger::error(errMsg + " Error: " + initResult.error().message);
            return std::unexpected(SecurityError{SecurityErrorType::InitializationFailed, errMsg, initResult.error().code});
        }
    }

    try {
        // Check if user is admin
        std::string adminQuery = "SELECT is_admin FROM users WHERE username = $1";
        std::vector<std::string> adminParams = {username};
        auto adminResult = connectionManager_->executeQuery(adminQuery, adminParams);

        if (!adminResult) {
            std::string errMsg = std::format("Database error checking admin status for {}: {}", username, adminResult.error());
            utils::Logger::error(errMsg);
            return std::unexpected(SecurityError{SecurityErrorType::DatabaseError, errMsg});
        }

        if (!adminResult->empty() && !(*adminResult)[0].empty() && (*adminResult)[0][0] == "t") {
            return true; // Admins have all permissions
        }

        // Check specific permission
        std::string query = "SELECT 1 FROM permissions WHERE username = $1 AND permission = $2";
        std::vector<std::string> permParams = {username, permission};
        auto result = connectionManager_->executeQuery(query, permParams);

        if (!result) {
            std::string errMsg = std::format("Database error checking permission for {}: {}", username, result.error());
            utils::Logger::error(errMsg);
            return std::unexpected(SecurityError{SecurityErrorType::DatabaseError, errMsg});
        }

        return !result->empty();
    } catch (const std::exception& e) {
        std::string errMsg = std::format("Exception during permission check: {}", e.what());
        utils::Logger::error(errMsg);
        return std::unexpected(SecurityError{SecurityErrorType::DatabaseError, errMsg});
    }
}

std::expected<void, SecurityError> SecurityManager::grantPermission(const std::string& username, const std::string& permission) {
    if (!initialized_) {
        auto initResult = initialize();
        if (!initResult) {
            std::string errMsg = "Failed to grant permission: Security Manager not initialized and initialization failed.";
            utils::Logger::error(errMsg + " Error: " + initResult.error().message);
            return std::unexpected(SecurityError{SecurityErrorType::InitializationFailed, errMsg, initResult.error().code});
        }
    }

    try {
        // Check if user exists
        std::string userQuery = "SELECT 1 FROM users WHERE username = $1";
        std::vector<std::string> userCheckParams = {username};
        auto userResult = connectionManager_->executeQuery(userQuery, userCheckParams);

        if (!userResult) {
            std::string errMsg = std::format("Database error checking if user {} exists: {}", username, userResult.error());
            utils::Logger::error(errMsg);
            return std::unexpected(SecurityError{SecurityErrorType::DatabaseError, errMsg});
        }

        if (userResult->empty()) {
            std::string errMsg = std::format("Cannot grant permission: User {} not found", username);
            utils::Logger::warning(errMsg);
            return std::unexpected(SecurityError{SecurityErrorType::UserNotFound, errMsg});
        }

        // Check if permission already granted
        auto hasPermResult = hasPermission(username, permission);
        if (!hasPermResult) {
            return std::unexpected(hasPermResult.error());
        }
        if (hasPermResult.value()) {
            utils::Logger::info(std::format("Permission {} already granted to {}", permission, username));
            return {};
        }

        // Grant permission
        std::string query = "INSERT INTO permissions (username, permission, granted_at) VALUES ($1, $2, NOW())";
        std::vector<std::string> grantParams = {username, permission};
        auto result = connectionManager_->executeNonQuery(query, grantParams);

        if (!result) {
            std::string errMsg = std::format("Database error granting permission {} to {}: {}", permission, username, result.error());
            utils::Logger::error(errMsg);
            return std::unexpected(SecurityError{SecurityErrorType::DatabaseError, errMsg});
        }

        if (*result < 0) {
            std::string errMsg = std::format("Failed to grant permission {} to {}", permission, username);
            utils::Logger::error(errMsg);
            return std::unexpected(SecurityError{SecurityErrorType::OperationFailed, errMsg});
        }

        utils::Logger::info(std::format("Permission {} granted to {} successfully", permission, username));
        return {};
    } catch (const std::exception& e) {
        std::string errMsg = std::format("Exception during permission granting: {}", e.what());
        utils::Logger::error(errMsg);
        return std::unexpected(SecurityError{SecurityErrorType::DatabaseError, errMsg});
    }
}

std::expected<void, SecurityError> SecurityManager::revokePermission(const std::string& username, const std::string& permission) {
    if (!initialized_) {
        auto initResult = initialize();
        if (!initResult) {
            std::string errMsg = "Failed to revoke permission: Security Manager not initialized and initialization failed.";
            utils::Logger::error(errMsg + " Error: " + initResult.error().message);
            return std::unexpected(SecurityError{SecurityErrorType::InitializationFailed, errMsg, initResult.error().code});
        }
    }

    try {
        // Check if permission is granted
        auto hasPermResult = hasPermission(username, permission);
        if (!hasPermResult) {
            return std::unexpected(hasPermResult.error());
        }
        if (!hasPermResult.value()) {
            utils::Logger::info(std::format("Permission {} not granted to {}", permission, username));
            return {};
        }

        // Revoke permission
        std::string query = "DELETE FROM permissions WHERE username = $1 AND permission = $2";
        std::vector<std::string> revokeParams = {username, permission};
        auto result = connectionManager_->executeNonQuery(query, revokeParams);

        if (!result) {
            std::string errMsg = std::format("Database error revoking permission {} from {}: {}", permission, username, result.error());
            utils::Logger::error(errMsg);
            return std::unexpected(SecurityError{SecurityErrorType::DatabaseError, errMsg});
        }

        if (*result < 0) {
            std::string errMsg = std::format("Failed to revoke permission {} from {}", permission, username);
            utils::Logger::error(errMsg);
            return std::unexpected(SecurityError{SecurityErrorType::OperationFailed, errMsg});
        }

        utils::Logger::info(std::format("Permission {} revoked from {} successfully", permission, username));
        return {};
    } catch (const std::exception& e) {
        std::string errMsg = std::format("Exception during permission revocation: {}", e.what());
        utils::Logger::error(errMsg);
        return std::unexpected(SecurityError{SecurityErrorType::DatabaseError, errMsg});
    }
}

std::expected<void, SecurityError> SecurityManager::createUser(const std::string& username, const std::string& password, bool isAdmin) {
    if (!initialized_) {
        auto initResult = initialize();
        if (!initResult) {
            std::string errMsg = "Failed to create user: Security Manager not initialized and initialization failed.";
            utils::Logger::error(errMsg + " Error: " + initResult.error().message);
            return std::unexpected(SecurityError{SecurityErrorType::InitializationFailed, errMsg, initResult.error().code});
        }
    }

    try {
        // Check if user already exists
        std::string checkQuery = "SELECT 1 FROM users WHERE username = $1";
        std::vector<std::string> checkParams = {username};
        auto checkResult = connectionManager_->executeQuery(checkQuery, checkParams);

        if (!checkResult) {
            std::string errMsg = std::format("Database error checking if user {} exists: {}", username, checkResult.error());
            utils::Logger::error(errMsg);
            return std::unexpected(SecurityError{SecurityErrorType::DatabaseError, errMsg});
        }

        if (!checkResult->empty()) {
            std::string errMsg = std::format("User {} already exists", username);
            utils::Logger::warning(errMsg);
            return std::unexpected(SecurityError{SecurityErrorType::UserCreationFailure, errMsg});
        }

        // Hash password
        auto hashResult = hashPassword(password);
        if (!hashResult) {
            return std::unexpected(hashResult.error());
        }
        std::string passwordHash = hashResult.value();

        // Create user
        std::string query = "INSERT INTO users (username, password_hash, is_admin, active, created_at) VALUES ($1, $2, $3, true, NOW())";
        std::vector<std::string> createParams = {username, passwordHash, isAdmin ? "true" : "false"};
        auto result = connectionManager_->executeNonQuery(query, createParams);

        if (!result) {
            std::string errMsg = std::format("Database error creating user {}: {}", username, result.error());
            utils::Logger::error(errMsg);
            return std::unexpected(SecurityError{SecurityErrorType::DatabaseError, errMsg});
        }

        if (*result < 0) {
            std::string errMsg = std::format("Failed to create user {}", username);
            utils::Logger::error(errMsg);
            return std::unexpected(SecurityError{SecurityErrorType::UserCreationFailure, errMsg});
        }

        utils::Logger::info(std::format("User {} created successfully", username));
        return {};
    } catch (const std::exception& e) {
        std::string errMsg = std::format("Exception during user creation: {}", e.what());
        utils::Logger::error(errMsg);
        return std::unexpected(SecurityError{SecurityErrorType::DatabaseError, errMsg});
    }
}

std::expected<void, SecurityError> SecurityManager::deleteUser(const std::string& username) {
    if (!initialized_) {
        auto initResult = initialize();
        if (!initResult) {
            std::string errMsg = "Failed to delete user: Security Manager not initialized and initialization failed.";
            utils::Logger::error(errMsg + " Error: " + initResult.error().message);
            return std::unexpected(SecurityError{SecurityErrorType::InitializationFailed, errMsg, initResult.error().code});
        }
    }

    std::shared_ptr<core::Transaction> transaction;
    try {
        auto transactionResult = connectionManager_->beginTransaction();
        if (!transactionResult) {
            std::string errMsg = "Failed to begin database transaction for user deletion: " + transactionResult.error();
            utils::Logger::error(errMsg);
            return std::unexpected(SecurityError{SecurityErrorType::DatabaseError, errMsg});
        }
        transaction = *transactionResult;

        try {
            // Delete permissions
            std::string permissionsQuery = "DELETE FROM permissions WHERE username = $1";
            std::vector<std::string> permissionsParams = {username};
            int permissionsResult = transaction->getConnection()->executeNonQuery(permissionsQuery, permissionsParams);

            if (permissionsResult < 0) {
                std::string errMsg = std::format("Failed to delete permissions for user {}", username);
                utils::Logger::error(errMsg);
                auto rollbackResult = transaction->rollback();
                if (!rollbackResult) {
                    utils::Logger::error(std::format("Failed to rollback transaction: {}", rollbackResult.error()));
                }
                return std::unexpected(SecurityError{SecurityErrorType::UserDeletionFailure, errMsg});
            }

            // Delete refresh tokens
            std::string tokensQuery = "DELETE FROM refresh_tokens WHERE username = $1";
            std::vector<std::string> tokensParams = {username};
            int tokensResult = transaction->getConnection()->executeNonQuery(tokensQuery, tokensParams);

            if (tokensResult < 0) {
                std::string errMsg = std::format("Failed to delete refresh tokens for user {}", username);
                utils::Logger::error(errMsg);
                auto rollbackResult = transaction->rollback();
                if (!rollbackResult) {
                    utils::Logger::error(std::format("Failed to rollback transaction: {}", rollbackResult.error()));
                }
                return std::unexpected(SecurityError{SecurityErrorType::UserDeletionFailure, errMsg});
            }

            // Delete user
            std::string userQuery = "DELETE FROM users WHERE username = $1";
            std::vector<std::string> userParams = {username};
            int userResult = transaction->getConnection()->executeNonQuery(userQuery, userParams);

            if (userResult < 0) {
                std::string errMsg = std::format("Failed to delete user {}", username);
                utils::Logger::error(errMsg);
                auto rollbackResult = transaction->rollback();
                if (!rollbackResult) {
                    utils::Logger::error(std::format("Failed to rollback transaction: {}", rollbackResult.error()));
                }
                return std::unexpected(SecurityError{SecurityErrorType::UserDeletionFailure, errMsg});
            }

            // Commit transaction
            auto commitResult = transaction->commit();
            if (!commitResult) {
                std::string errMsg = std::format("Failed to commit user deletion for {}: {}", username, commitResult.error());
                utils::Logger::error(errMsg);
                return std::unexpected(SecurityError{SecurityErrorType::UserDeletionFailure, errMsg});
            }

            utils::Logger::info(std::format("User {} deleted successfully", username));
            return {};
        } catch (const std::exception& e) {
            std::string errMsg = std::format("Exception during user deletion transaction: {}", e.what());
            utils::Logger::error(errMsg);
            auto rollbackResult = transaction->rollback();
            if (!rollbackResult) {
                utils::Logger::error(std::format("Failed to rollback transaction: {}", rollbackResult.error()));
            }
            return std::unexpected(SecurityError{SecurityErrorType::DatabaseError, errMsg});
        }
    } catch (const std::exception& e) {
        std::string errMsg = std::format("Exception during user deletion: {}", e.what());
        utils::Logger::error(errMsg);
        return std::unexpected(SecurityError{SecurityErrorType::DatabaseError, errMsg});
    }
}

std::expected<void, SecurityError> SecurityManager::createUsersTable() {
    ManagedConnection conn(*connectionManager_);
    if (!conn) {
        std::string errMsg = "Failed to acquire database connection for createUsersTable";
        utils::Logger::error(errMsg);
        return std::unexpected(SecurityError{SecurityErrorType::DatabaseError, errMsg});
    }

    try {
        std::string query = R"(
            CREATE TABLE IF NOT EXISTS users (
                id SERIAL PRIMARY KEY,
                username VARCHAR(255) NOT NULL UNIQUE,
                password_hash VARCHAR(255) NOT NULL,
                email VARCHAR(255),
                is_admin BOOLEAN NOT NULL DEFAULT false,
                active BOOLEAN NOT NULL DEFAULT true,
                created_at TIMESTAMP NOT NULL,
                last_login TIMESTAMP
            )
        )";

        int result = conn->executeNonQuery(query, {});
        if (result < 0) {
            std::string errMsg = "Failed to create users table (executeNonQuery failed)";
            utils::Logger::error(errMsg);
            return std::unexpected(SecurityError{SecurityErrorType::DatabaseError, errMsg});
        }
        utils::Logger::info("Users table created successfully or already exists.");
        return {};
    } catch (const pqxx::sql_error& e) {
        std::string errMsg = std::format("SQL error during users table creation: {}", e.what());
        utils::Logger::error(errMsg);
        return std::unexpected(SecurityError{SecurityErrorType::DatabaseError, errMsg});
    } catch (const std::exception& e) {
        std::string errMsg = std::format("Generic exception during users table creation: {}", e.what());
        utils::Logger::error(errMsg);
        return std::unexpected(SecurityError{SecurityErrorType::Unknown, errMsg});
    }
}

std::expected<void, SecurityError> SecurityManager::createPermissionsTable() {
    ManagedConnection conn(*connectionManager_);
    if (!conn) {
        std::string errMsg = "Failed to acquire database connection for createPermissionsTable";
        utils::Logger::error(errMsg);
        return std::unexpected(SecurityError{SecurityErrorType::DatabaseError, errMsg});
    }

    try {
        std::string query = R"(
            CREATE TABLE IF NOT EXISTS permissions (
                id SERIAL PRIMARY KEY,
                username VARCHAR(255) NOT NULL,
                permission VARCHAR(255) NOT NULL,
                granted_at TIMESTAMP NOT NULL,
                UNIQUE (username, permission),
                FOREIGN KEY (username) REFERENCES users(username) ON DELETE CASCADE
            )
        )";

        int result = conn->executeNonQuery(query, {});
        if (result < 0) {
            std::string errMsg = "Failed to create permissions table (executeNonQuery failed)";
            utils::Logger::error(errMsg);
            return std::unexpected(SecurityError{SecurityErrorType::DatabaseError, errMsg});
        }
        utils::Logger::info("Permissions table created successfully or already exists.");
        return {};
    } catch (const pqxx::sql_error& e) {
        std::string errMsg = std::format("SQL error during permissions table creation: {}", e.what());
        utils::Logger::error(errMsg);
        return std::unexpected(SecurityError{SecurityErrorType::DatabaseError, errMsg});
    } catch (const std::exception& e) {
        std::string errMsg = std::format("Generic exception during permissions table creation: {}", e.what());
        utils::Logger::error(errMsg);
        return std::unexpected(SecurityError{SecurityErrorType::Unknown, errMsg});
    }
}

std::expected<void, SecurityError> SecurityManager::createRefreshTokensTable() {
    ManagedConnection conn(*connectionManager_);
    if (!conn) {
        std::string errMsg = "Failed to acquire database connection for createRefreshTokensTable";
        utils::Logger::error(errMsg);
        return std::unexpected(SecurityError{SecurityErrorType::DatabaseError, errMsg});
    }

    try {
        std::string query = R"(
            CREATE TABLE IF NOT EXISTS refresh_tokens (
                id SERIAL PRIMARY KEY,
                token VARCHAR(255) NOT NULL UNIQUE,
                username VARCHAR(255) NOT NULL,
                created_at TIMESTAMP NOT NULL,
                expires_at TIMESTAMP NOT NULL,
                is_revoked BOOLEAN NOT NULL DEFAULT false,
                FOREIGN KEY (username) REFERENCES users(username) ON DELETE CASCADE
            )
        )";

        int result = conn->executeNonQuery(query, {});
        if (result < 0) {
            std::string errMsg = "Failed to create refresh tokens table (executeNonQuery failed)";
            utils::Logger::error(errMsg);
            return std::unexpected(SecurityError{SecurityErrorType::DatabaseError, errMsg});
        }
        utils::Logger::info("Refresh tokens table created successfully or already exists.");
        return {};
    } catch (const pqxx::sql_error& e) {
        std::string errMsg = std::format("SQL error during refresh tokens table creation: {}", e.what());
        utils::Logger::error(errMsg);
        return std::unexpected(SecurityError{SecurityErrorType::DatabaseError, errMsg});
    } catch (const std::exception& e) {
        std::string errMsg = std::format("Generic exception during refresh tokens table creation: {}", e.what());
        utils::Logger::error(errMsg);
        return std::unexpected(SecurityError{SecurityErrorType::Unknown, errMsg});
    }
}

std::expected<std::string, SecurityError> SecurityManager::hashPassword(const std::string& password) {
    if (password.empty()) {
        std::string errMsg = "Password cannot be empty.";
        utils::Logger::error("hashPassword failed: " + errMsg);
        return std::unexpected(SecurityError{SecurityErrorType::HashingError, errMsg});
    }
    // It's generally good practice to also check for overly long passwords, but that's omitted for brevity here.

    try {
        // Generate salt
        std::string salt = generateSalt(16);

        // Hash password with salt
        std::string saltedPassword = password + salt;
        std::string hash = sha256(saltedPassword); // Assuming sha256 is robust or throws on OpenSSL errors

        // Format: algorithm$salt$hash
        return "sha256$" + salt + "$" + hash;
    } catch (const std::exception& e) {
        // This catch block is a safeguard. Ideally, generateSalt/sha256 would also return std::expected
        std::string errMsg = std::format("Exception during password hashing: {}", e.what());
        utils::Logger::error(errMsg);
        return std::unexpected(SecurityError{SecurityErrorType::HashingError, errMsg});
    }
}

std::expected<void, SecurityError> SecurityManager::verifyPassword(const std::string& password, const std::string& storedPasswordHash) {
    if (password.empty()) {
        std::string errMsg = "Input password cannot be empty for verification.";
        utils::Logger::warning("verifyPassword failed: " + errMsg);
        return std::unexpected(SecurityError{SecurityErrorType::InvalidCredentials, errMsg});
    }
    if (storedPasswordHash.empty()) {
        std::string errMsg = "Stored password hash cannot be empty for verification.";
        utils::Logger::warning("verifyPassword failed: " + errMsg);
        return std::unexpected(SecurityError{SecurityErrorType::InvalidCredentials, errMsg});
    }

    // Parse hash
    std::vector<std::string> parts;
    std::string delimiter = "$";
    size_t pos = 0;
    std::string hashCopy = storedPasswordHash;

    while ((pos = hashCopy.find(delimiter)) != std::string::npos) {
        parts.push_back(hashCopy.substr(0, pos));
        hashCopy.erase(0, pos + delimiter.length());
    }
    parts.push_back(hashCopy); // Add the last part

    if (parts.size() != 3) {
        std::string errMsg = std::format("Invalid stored hash format. Expected 'algorithm$salt$hash', got: '{}'", storedPasswordHash);
        utils::Logger::error("verifyPassword failed: " + errMsg);
        return std::unexpected(SecurityError{SecurityErrorType::InvalidCredentials, errMsg});
    }

    const std::string& algorithm = parts[0];
    const std::string& salt = parts[1];
    const std::string& actualStoredHash = parts[2]; // Renamed to avoid confusion with 'storedPasswordHash' parameter

    if (algorithm != "sha256") {
        std::string errMsg = std::format("Unsupported hash algorithm: '{}'. Expected 'sha256'.", algorithm);
        utils::Logger::error("verifyPassword failed: " + errMsg);
        return std::unexpected(SecurityError{SecurityErrorType::InvalidCredentials, errMsg});
    }

    try {
        // Hash password with salt
        std::string saltedPassword = password + salt;
        std::string computedHash = sha256(saltedPassword); // Assuming sha256 is robust or throws

        if (computedHash == actualStoredHash) {
            return {}; // Success
        } else {
            std::string errMsg = "Password mismatch.";
            utils::Logger::warning("verifyPassword failed: " + errMsg); // Log actual failure, but not too much detail
            return std::unexpected(SecurityError{SecurityErrorType::InvalidCredentials, errMsg});
        }
    } catch (const std::exception& e) {
        // This catch block is a safeguard. Ideally, sha256 would also return std::expected
        std::string errMsg = std::format("Exception during password verification (hashing step): {}", e.what());
        utils::Logger::error(errMsg);
        return std::unexpected(SecurityError{SecurityErrorType::InvalidCredentials, errMsg});
    }
}

std::expected<TokenPair, SecurityError> SecurityManager::generateTokenPair(const std::string& username) {
    if (jwtSecret_ == "change-this-to-a-secure-secret-in-production" || jwtSecret_.empty()) {
        std::string errMsg = "JWT secret is not configured securely. Cannot generate tokens.";
        utils::Logger::critical(errMsg); // Critical security misconfiguration
        return std::unexpected(SecurityError{SecurityErrorType::ConfigurationError, errMsg});
    }

    TokenPair tokens;
    bool isAdmin = false;

    // Get user info for token payload (is_admin status)
    {
        ManagedConnection conn(*connectionManager_);
        if (!conn) {
            std::string errMsg = std::format("Failed to generate token pair for user {}: Could not acquire database connection to get user info", username);
            utils::Logger::error(errMsg);
            return std::unexpected(SecurityError{SecurityErrorType::DatabaseError, errMsg});
        }
        try {
            std::string query = "SELECT is_admin FROM users WHERE username = $1";
            std::vector<std::string> tokenParams = {username};
            auto dbResult = conn->executeQuery(query, tokenParams);
            if (!dbResult.empty() && !dbResult[0].empty()) {
                std::string adminStr = dbResult[0][0];
                isAdmin = (adminStr == "t" || adminStr == "true" || adminStr == "1");
            } else {
                // This case should ideally not be hit if generateTokenPair is called after successful authentication
                // However, as a safeguard:
                std::string errMsg = std::format("Failed to generate token pair: User {} not found or no admin status retrieved.", username);
                utils::Logger::warning(errMsg);
                // Depending on policy, this could be a TokenGenerationFailed or a more specific error.
                // For now, let's assume isAdmin defaults to false and proceed, but log it.
            }
        } catch (const pqxx::sql_error& e) {
            std::string errMsg = std::format("SQL error getting user info for token generation for user {}: {}", username, e.what());
            utils::Logger::error(errMsg);
            return std::unexpected(SecurityError{SecurityErrorType::DatabaseError, errMsg});
        } catch (const std::exception& e) {
            std::string errMsg = std::format("Generic exception getting user info for token generation for user {}: {}", username, e.what());
            utils::Logger::error(errMsg);
            return std::unexpected(SecurityError{SecurityErrorType::Unknown, errMsg});
        }
    }

    try {
        // Create access token payload
        std::unordered_map<std::string, std::string> accessPayload;
        accessPayload["sub"] = username;
        accessPayload["type"] = "access";
        accessPayload["admin"] = isAdmin ? "true" : "false";

        // Create refresh token payload
        std::unordered_map<std::string, std::string> refreshPayload;
        refreshPayload["sub"] = username;
        refreshPayload["type"] = "refresh";
        refreshPayload["jti"] = generateSalt(32); // Unique token ID, increased length for better uniqueness

        // Generate tokens
        try {
            tokens.accessToken = JWT::create(accessPayload, jwtSecret_, accessTokenExpirationSeconds_);
        } catch (const std::exception& e_jwt_access) {
            std::string errMsg = std::format("Failed to create access token for user {}: {}", username, e_jwt_access.what());
            utils::Logger::error(errMsg);
            return std::unexpected(SecurityError{SecurityErrorType::TokenGenerationFailed, errMsg});
        }

        try {
            tokens.refreshToken = JWT::create(refreshPayload, jwtSecret_, refreshTokenExpirationSeconds_);
        } catch (const std::exception& e_jwt_refresh) {
            std::string errMsg = std::format("Failed to create refresh token for user {}: {}", username, e_jwt_refresh.what());
            utils::Logger::error(errMsg);
            return std::unexpected(SecurityError{SecurityErrorType::TokenGenerationFailed, errMsg});
        }

        // Store refresh token in database
        auto expiresAt = std::chrono::system_clock::now() + std::chrono::seconds(refreshTokenExpirationSeconds_);
        auto storeTokenResult = storeRefreshToken(username, tokens.refreshToken, expiresAt);
        if (!storeTokenResult) {
            std::string errMsg = std::format("Token pair generated for user {} but failed to store refresh token. Error: {}", username, storeTokenResult.error().message);
            utils::Logger::error(errMsg);
            // Propagate the error from storeRefreshToken, possibly wrapping it
            return std::unexpected(SecurityError{
                SecurityErrorType::TokenStorageFailed, // More specific than just TokenGenerationFailed
                errMsg,
                storeTokenResult.error().code
            });
        }

        utils::Logger::debug(std::format("Token pair generated successfully for user {}", username));
        return tokens;

    } catch (const std::exception& e) {
        // Catch-all for other unexpected issues during token payload setup or salt generation (if generateSalt throws)
        std::string errMsg = std::format("Generic exception during token pair generation for user {}: {}", username, e.what());
        utils::Logger::error(errMsg);
        return std::unexpected(SecurityError{SecurityErrorType::TokenGenerationFailed, errMsg});
    }
}

std::expected<TokenPair, SecurityError> SecurityManager::refreshAccessToken(const std::string& refreshToken) {
    if (!initialized_) {
        auto initResult = initialize();
        if (!initResult) {
            std::string errMsg = "Failed to refresh access token: Security Manager not initialized and initialization failed.";
            utils::Logger::error(errMsg + " Error: " + initResult.error().message);
            return std::unexpected(SecurityError{SecurityErrorType::InitializationFailed, errMsg, initResult.error().code});
        }
    }

    // Verify refresh token and get username
    auto validateResult = validateRefreshToken(refreshToken);
    if (!validateResult) {
        std::string errMsg = std::format("Failed to refresh access token: Invalid refresh token. Reason: {}", validateResult.error().message);
        utils::Logger::warning(errMsg);
        // Propagate the specific error from validateRefreshToken
        return std::unexpected(SecurityError{
            validateResult.error().type, // Preserve original error type
            errMsg,
            validateResult.error().code
        });
    }
    const std::string& username = validateResult.value();

    // Generate new token pair
    auto generateResult = generateTokenPair(username);
    if (!generateResult) {
        std::string errMsg = std::format("Failed to refresh access token for user {}: Could not generate new token pair. Reason: {}", username, generateResult.error().message);
        utils::Logger::error(errMsg);
        // Propagate the specific error from generateTokenPair
        return std::unexpected(SecurityError{
            generateResult.error().type, // Preserve original error type
            errMsg,
            generateResult.error().code
        });
    }
    TokenPair newTokens = generateResult.value();

    // Invalidate old refresh token (best effort)
    ManagedConnection conn(*connectionManager_);
    if (!conn) {
        utils::Logger::error(std::format("Failed to invalidate old refresh token for user {}: Could not acquire database connection. New tokens are still valid.", username));
    } else {
        try {
            std::string query = "UPDATE refresh_tokens SET is_revoked = true WHERE token = $1";
            std::vector<std::string> refreshParams = {refreshToken};
            int dbResult = conn->executeNonQuery(query, refreshParams);
            if (dbResult < 0) {
                 utils::Logger::warning(std::format("Failed to invalidate old refresh token for user {} (DB update failed). New tokens are still valid.", username));
            }
        } catch (const pqxx::sql_error& e) {
            utils::Logger::error(std::format("SQL error invalidating old refresh token for user {}: {}. New tokens are still valid.", username, e.what()));
        } catch (const std::exception& e) {
            utils::Logger::error(std::format("Generic exception invalidating old refresh token for user {}: {}. New tokens are still valid.", username, e.what()));
        }
    }

    utils::Logger::info(std::format("Access token refreshed successfully for user {}", username));
    return newTokens;
}

std::expected<void, SecurityError> SecurityManager::invalidateTokens(const std::string& username) {
    if (!initialized_) {
        auto initResult = initialize();
        if (!initResult) {
            std::string errMsg = std::format("Failed to invalidate tokens for user {}: Security Manager not initialized and initialization failed.", username);
            utils::Logger::error(errMsg + " Error: " + initResult.error().message);
            return std::unexpected(SecurityError{SecurityErrorType::InitializationFailed, errMsg, initResult.error().code});
        }
    }

    ManagedConnection conn(*connectionManager_);
    if (!conn) {
        std::string errMsg = std::format("Failed to invalidate tokens for user {}: Could not acquire database connection", username);
        utils::Logger::error(errMsg);
        return std::unexpected(SecurityError{SecurityErrorType::DatabaseError, errMsg});
    }

    try {
        std::string query = "UPDATE refresh_tokens SET is_revoked = true WHERE username = $1";
        std::vector<std::string> invalidateParams = {username};
        int dbResult = conn->executeNonQuery(query, invalidateParams);

        // executeNonQuery usually returns number of affected rows or 0 if none, -1 on some errors not throwing exceptions.
        // We consider >= 0 as success, as 0 rows affected is not an error if user had no tokens.
        if (dbResult < 0) {
            std::string errMsg = std::format("Failed to invalidate tokens for user {}: Database update failed.", username);
            utils::Logger::error(errMsg);
            return std::unexpected(SecurityError{SecurityErrorType::DatabaseError, errMsg});
        }

        utils::Logger::info(std::format("Tokens invalidated successfully for user {}. {} tokens affected.", username, dbResult));
        return {}; // Success
    } catch (const pqxx::sql_error& e) {
        std::string errMsg = std::format("SQL error invalidating tokens for user {}: {}", username, e.what());
        utils::Logger::error(errMsg);
        return std::unexpected(SecurityError{SecurityErrorType::DatabaseError, errMsg});
    } catch (const std::exception& e) {
        std::string errMsg = std::format("Generic exception invalidating tokens for user {}: {}", username, e.what());
        utils::Logger::error(errMsg);
        return std::unexpected(SecurityError{SecurityErrorType::Unknown, errMsg});
    }
}

std::expected<void, SecurityError> SecurityManager::storeRefreshToken(const std::string& username, const std::string& refreshToken,
                                                              const std::chrono::system_clock::time_point& expiresAt) {
    ManagedConnection conn(*connectionManager_);
    if (!conn) {
        std::string errMsg = std::format("Failed to store refresh token for user {}: Could not acquire database connection", username);
        utils::Logger::error(errMsg);
        return std::unexpected(SecurityError{SecurityErrorType::DatabaseError, errMsg});
    }

    try {
        // Convert expiration time to string for SQL
        auto expiresTime_t = std::chrono::system_clock::to_time_t(expiresAt);
        std::tm expiresTm = *std::localtime(&expiresTime_t); // Note: localtime is not thread-safe on all platforms
        char expiresBuffer[80];
        if (std::strftime(expiresBuffer, sizeof(expiresBuffer), "%Y-%m-%d %H:%M:%S", &expiresTm) == 0) {
            std::string errMsg = std::format("Failed to format expiration time for refresh token for user {}.", username);
            utils::Logger::error(errMsg);
            return std::unexpected(SecurityError{SecurityErrorType::TokenStorageFailed, errMsg});
        }

        std::string query = R"(
            INSERT INTO refresh_tokens (token, username, created_at, expires_at, is_revoked)
            VALUES ($1, $2, NOW(), $3, false)
        )";

        std::vector<std::string> storeParams = {refreshToken, username, std::string(expiresBuffer)};
        int dbResult = conn->executeNonQuery(query, storeParams);

        if (dbResult < 0) {
            std::string errMsg = std::format("Failed to store refresh token for user {}: Database insert failed.", username);
            utils::Logger::error(errMsg);
            return std::unexpected(SecurityError{SecurityErrorType::TokenStorageFailed, errMsg});
        }

        utils::Logger::debug(std::format("Refresh token stored successfully for user {}", username));
        return {}; // Success
    } catch (const pqxx::sql_error& e) {
        std::string errMsg = std::format("SQL error storing refresh token for user {}: {}", username, e.what());
        utils::Logger::error(errMsg);
        return std::unexpected(SecurityError{SecurityErrorType::DatabaseError, errMsg});
    } catch (const std::exception& e) {
        std::string errMsg = std::format("Generic exception storing refresh token for user {}: {}", username, e.what());
        utils::Logger::error(errMsg);
        return std::unexpected(SecurityError{SecurityErrorType::TokenStorageFailed, errMsg});
    }
}

std::expected<std::string, SecurityError> SecurityManager::validateRefreshToken(const std::string& refreshToken) {
    if (!initialized_) {
        auto initResult = initialize();
        if (!initResult) {
            std::string errMsg = "Failed to validate refresh token: Security Manager not initialized and initialization failed.";
            utils::Logger::error(errMsg + " Error: " + initResult.error().message);
            return std::unexpected(SecurityError{SecurityErrorType::InitializationFailed, errMsg, initResult.error().code});
        }
    }

    if (jwtSecret_ == "change-this-to-a-secure-secret-in-production" || jwtSecret_.empty()) {
        std::string errMsg = "JWT secret is not configured securely. Cannot validate refresh tokens.";
        utils::Logger::critical(errMsg);
        return std::unexpected(SecurityError{SecurityErrorType::ConfigurationError, errMsg});
    }

    std::unordered_map<std::string, std::string> payload;
    try {
        if (!JWT::verify(refreshToken, jwtSecret_, payload)) {
            std::string errMsg = "Invalid or expired JWT refresh token (JWT::verify returned false).";
            utils::Logger::warning(errMsg);
            return std::unexpected(SecurityError{SecurityErrorType::TokenValidationFailed, errMsg});
        }
    } catch (const std::exception& e_jwt) {
        std::string errMsg = std::format("Exception during JWT refresh token verification: {}", e_jwt.what());
        utils::Logger::warning(errMsg);
        return std::unexpected(SecurityError{SecurityErrorType::TokenValidationFailed, errMsg});
    }

    auto tokenTypeIt = payload.find("type");
    if (tokenTypeIt == payload.end() || tokenTypeIt->second != "refresh") {
        std::string errMsg = "Refresh token validation failed: Not a refresh token.";
        utils::Logger::warning(errMsg);
        return std::unexpected(SecurityError{SecurityErrorType::TokenValidationFailed, errMsg});
    }

    auto usernameIt = payload.find("sub");
    if (usernameIt == payload.end() || usernameIt->second.empty()) {
        std::string errMsg = "Refresh token validation failed: Token does not contain a subject (username) or username is empty.";
        utils::Logger::warning(errMsg);
        return std::unexpected(SecurityError{SecurityErrorType::TokenValidationFailed, errMsg});
    }
    std::string usernameFromToken = usernameIt->second;

    ManagedConnection conn(*connectionManager_);
    if (!conn) {
        std::string errMsg = std::format("Failed to validate refresh token for user {}: Could not acquire database connection", usernameFromToken);
        utils::Logger::error(errMsg);
        return std::unexpected(SecurityError{SecurityErrorType::DatabaseError, errMsg});
    }

    try {
        std::string query = R"(
            SELECT 1 FROM refresh_tokens
            WHERE token = $1 AND username = $2 AND is_revoked = false AND expires_at > NOW()
        )";

        std::vector<std::string> validateParams = {refreshToken, usernameFromToken};
        auto dbResult = conn->executeQuery(query, validateParams);

        if (dbResult.empty()) {
            std::string errMsg = std::format("Refresh token validation failed: Token for user '{}' not found in database, revoked, expired, or DB error.", usernameFromToken);
            utils::Logger::warning(errMsg);
            return std::unexpected(SecurityError{SecurityErrorType::TokenValidationFailed, errMsg});
        }

        utils::Logger::debug(std::format("Refresh token validated successfully for user {}", usernameFromToken));
        return usernameFromToken; // Success, return username
    } catch (const pqxx::sql_error& e) {
        std::string errMsg = std::format("SQL error during refresh token validation for user {}: {}", usernameFromToken, e.what());
        utils::Logger::error(errMsg);
        return std::unexpected(SecurityError{SecurityErrorType::DatabaseError, errMsg});
    } catch (const std::exception& e) {
        std::string errMsg = std::format("Generic exception during refresh token validation for user {}: {}", usernameFromToken, e.what());
        utils::Logger::error(errMsg);
        return std::unexpected(SecurityError{SecurityErrorType::Unknown, errMsg});
    }
}

void SecurityManager::cleanupExpiredRefreshTokens() {
    ManagedConnection conn(*connectionManager_);
    if (!conn) {
        utils::Logger::error("Failed to cleanup expired refresh tokens: Could not acquire database connection.");
        return;
    }

    try {
        std::string query = "DELETE FROM refresh_tokens WHERE expires_at < NOW()";
        int dbResult = conn->executeNonQuery(query, {});

        if (dbResult > 0) {
            utils::Logger::info(std::format("Cleaned up {} expired refresh tokens.", dbResult));
        } else if (dbResult == 0) {
            utils::Logger::debug("No expired refresh tokens to cleanup.");
        } else {
            // This case (negative result without exception) might indicate an issue with executeNonQuery's error reporting
            utils::Logger::warning("Refresh token cleanup query executed but reported an unexpected result ( < 0).");
        }
    } catch (const pqxx::sql_error& e) {
        utils::Logger::error(std::format("SQL error during expired refresh token cleanup: {}. SQLSTATE: {}", e.what(), e.sqlstate()));
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Generic exception during expired refresh token cleanup: {}", e.what()));
    }
}

void SecurityManager::setJwtSecret(const std::string& secret) {
    jwtSecret_ = secret;
}

void SecurityManager::setTokenExpirationTimes(int accessTokenExpirationSeconds, int refreshTokenExpirationSeconds) {
    accessTokenExpirationSeconds_ = accessTokenExpirationSeconds;
    refreshTokenExpirationSeconds_ = refreshTokenExpirationSeconds;
}

std::string SecurityManager::generateSalt(size_t length) {
    static const std::string chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";

    std::random_device rd;
    std::mt19937 generator(rd());
    std::uniform_int_distribution<> distribution(0, chars.size() - 1);

    std::string salt;
    salt.reserve(length);

    for (size_t i = 0; i < length; ++i) {
        salt += chars[distribution(generator)];
    }

    return salt;
}



} // namespace dbservice::security
