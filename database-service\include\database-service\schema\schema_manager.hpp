#pragma once
#include <string>
#include <memory>
#include <vector>
#include <unordered_map>

namespace dbservice {

namespace core {
    class ConnectionManager;
}

namespace schema {

enum class SchemaErrorType {
    Unknown,
    InitializationFailed,
    ConnectionFailed,
    QueryFailed,
    SchemaCreationFailure,
    SchemaDropFailure,
    SchemaNotFound,
    MigrationTableCreationFailure,
    MigrationFileLoadFailure,
    MigrationApplyFailure,
    FilesystemError
};

struct SchemaError {
    SchemaErrorType type = SchemaErrorType::Unknown;
    std::string message;
    int code = 0; // Optional: could be DB error code or internal code

    std::string toString() const {
        // A simple way to convert enum to string might be needed if not C++23 std::format support for enums directly
        // For now, casting to int or implementing a helper.
        std::string type_str;
        switch (type) {
            case SchemaErrorType::Unknown: type_str = "Unknown"; break;
            case SchemaErrorType::InitializationFailed: type_str = "InitializationFailed"; break;
            case SchemaErrorType::ConnectionFailed: type_str = "ConnectionFailed"; break;
            case SchemaErrorType::QueryFailed: type_str = "QueryFailed"; break;
            case SchemaErrorType::SchemaCreationFailure: type_str = "SchemaCreationFailure"; break;
            case SchemaErrorType::SchemaDropFailure: type_str = "SchemaDropFailure"; break;
            case SchemaErrorType::SchemaNotFound: type_str = "SchemaNotFound"; break;
            case SchemaErrorType::MigrationTableCreationFailure: type_str = "MigrationTableCreationFailure"; break;
            case SchemaErrorType::MigrationFileLoadFailure: type_str = "MigrationFileLoadFailure"; break;
            case SchemaErrorType::MigrationApplyFailure: type_str = "MigrationApplyFailure"; break;
            case SchemaErrorType::FilesystemError: type_str = "FilesystemError"; break;
            default: type_str = "InvalidErrorType"; break;
        }
        return std::format("SchemaError (Type: {}, Code: {}): {}", type_str, code, message);
    }
};

/**
 * @class SchemaManager
 * @brief Manages database schemas
 */
class SchemaManager {
public:
    /**
     * @brief Constructor
     * @param connectionManager Connection manager
     * @param schemaDirectory Directory containing schema files
     */
    SchemaManager(std::shared_ptr<core::ConnectionManager> connectionManager, 
                 const std::string& schemaDirectory);

    /**
     * @brief Destructor
     */
    ~SchemaManager();

    /**
     * @brief Initialize the schema manager
     * @return True if initialization was successful
     */
    std::expected<void, SchemaError> initialize();

    /**
     * @brief Create a schema
     * @param schemaName Schema name
     * @return True if schema was created successfully
     */
    std::expected<void, SchemaError> createSchema(const std::string& schemaName);

    /**
     * @brief Drop a schema
     * @param schemaName Schema name
     * @return True if schema was dropped successfully
     */
    std::expected<void, SchemaError> dropSchema(const std::string& schemaName);

    /**
     * @brief Check if a schema exists
     * @param schemaName Schema name
     * @return True if schema exists
     */
    std::expected<bool, SchemaError> schemaExists(const std::string& schemaName);

    /**
     * @brief Get all schemas
     * @return List of schema names
     */
    std::expected<std::vector<std::string>, SchemaError> getSchemas();

    /**
     * @brief Apply a migration
     * @param schemaName Schema name
     * @param migrationName Migration name
     * @return True if migration was applied successfully
     */
    std::expected<void, SchemaError> applyMigration(const std::string& schemaName, const std::string& migrationName);

    /**
     * @brief Get applied migrations
     * @param schemaName Schema name
     * @return List of applied migration names
     */
    std::expected<std::vector<std::string>, SchemaError> getAppliedMigrations(const std::string& schemaName);

private:
    /**
     * @brief Create the migrations table
     * @return True if table was created successfully
     */
    std::expected<void, SchemaError> createMigrationsTable();

    /**
     * @brief Load migration files
     * @return True if migration files were loaded successfully
     */
    std::expected<void, SchemaError> loadMigrationFiles();

    std::shared_ptr<core::ConnectionManager> connectionManager_;
    std::string schemaDirectory_;
    std::unordered_map<std::string, std::string> migrationFiles_;
    bool initialized_;
};

} // namespace schema
} // namespace dbservice
