#pragma once
#include <unordered_map>
#include <list>
#include <mutex>
#include <chrono>
#include <optional>
#include <functional>
#include <nlohmann/json.hpp>

namespace dbservice::utils {

/**
 * @struct CacheEntry
 * @brief Represents a cached entry with expiration time
 */
template<typename T>
struct CacheEntry {
    T value;
    std::chrono::steady_clock::time_point expiry;
    std::chrono::steady_clock::time_point lastAccessed;
    size_t accessCount = 0;
    
    bool isExpired() const {
        return std::chrono::steady_clock::now() > expiry;
    }
    
    void updateAccess() {
        lastAccessed = std::chrono::steady_clock::now();
        accessCount++;
    }
};

/**
 * @class LRUCache
 * @brief Thread-safe LRU cache with TTL support
 */
template<typename Key, typename Value>
class LRUCache {
public:
    using KeyType = Key;
    using ValueType = Value;
    using EntryType = CacheEntry<Value>;
    
    /**
     * @brief Constructor
     * @param maxSize Maximum number of entries
     * @param defaultTtl Default time-to-live for entries
     */
    explicit LRUCache(size_t maxSize, std::chrono::milliseconds defaultTtl = std::chrono::minutes(10));

    /**
     * @brief Get a value from the cache
     * @param key Key to look up
     * @return Optional value if found and not expired
     */
    std::optional<Value> get(const Key& key);

    /**
     * @brief Put a value into the cache
     * @param key Key to store
     * @param value Value to store
     * @param ttl Time-to-live (optional, uses default if not specified)
     */
    void put(const Key& key, const Value& value, 
             std::optional<std::chrono::milliseconds> ttl = std::nullopt);

    /**
     * @brief Remove a value from the cache
     * @param key Key to remove
     * @return True if the key was found and removed
     */
    bool remove(const Key& key);

    /**
     * @brief Clear all entries from the cache
     */
    void clear();

    /**
     * @brief Get cache statistics
     * @return JSON object with cache statistics
     */
    nlohmann::json getStats() const;

    /**
     * @brief Get current cache size
     * @return Number of entries in cache
     */
    size_t size() const;

    /**
     * @brief Get maximum cache size
     * @return Maximum number of entries
     */
    size_t maxSize() const;

    /**
     * @brief Check if cache is empty
     * @return True if cache is empty
     */
    bool empty() const;

    /**
     * @brief Clean up expired entries
     * @return Number of entries removed
     */
    size_t cleanup();

private:
    using ListIterator = typename std::list<Key>::iterator;
    
    struct CacheNode {
        EntryType entry;
        ListIterator listPos;
    };

    void moveToFront(const Key& key);
    void evictLRU();

    mutable std::mutex mutex_;
    size_t maxSize_;
    std::chrono::milliseconds defaultTtl_;
    
    std::unordered_map<Key, CacheNode> cache_;
    std::list<Key> lruList_;
    
    // Statistics
    mutable size_t hits_ = 0;
    mutable size_t misses_ = 0;
    mutable size_t evictions_ = 0;
};

/**
 * @class QueryCache
 * @brief Specialized cache for database query results
 */
class QueryCache {
public:
    using QueryKey = std::string;
    using QueryResult = nlohmann::json;
    
    /**
     * @brief Constructor
     * @param maxSize Maximum number of cached queries
     * @param defaultTtl Default TTL for query results
     */
    explicit QueryCache(size_t maxSize = 1000, 
                       std::chrono::milliseconds defaultTtl = std::chrono::minutes(5));

    /**
     * @brief Get cached query result
     * @param query SQL query string
     * @param params Query parameters
     * @return Cached result if available
     */
    std::optional<QueryResult> get(const std::string& query, 
                                  const std::vector<std::string>& params = {});

    /**
     * @brief Cache a query result
     * @param query SQL query string
     * @param params Query parameters
     * @param result Query result to cache
     * @param ttl Optional TTL override
     */
    void put(const std::string& query, 
             const std::vector<std::string>& params,
             const QueryResult& result,
             std::optional<std::chrono::milliseconds> ttl = std::nullopt);

    /**
     * @brief Invalidate cache entries matching a pattern
     * @param pattern Pattern to match (simple string contains)
     */
    void invalidate(const std::string& pattern);

    /**
     * @brief Get cache statistics
     * @return JSON object with cache statistics
     */
    nlohmann::json getStats() const;

    /**
     * @brief Clear all cached queries
     */
    void clear();

private:
    std::string makeKey(const std::string& query, const std::vector<std::string>& params) const;
    
    LRUCache<QueryKey, QueryResult> cache_;
};

// Template implementation for LRUCache
template<typename Key, typename Value>
LRUCache<Key, Value>::LRUCache(size_t maxSize, std::chrono::milliseconds defaultTtl)
    : maxSize_(maxSize), defaultTtl_(defaultTtl) {
    if (maxSize_ == 0) {
        maxSize_ = 100; // Default size
    }
}

template<typename Key, typename Value>
std::optional<Value> LRUCache<Key, Value>::get(const Key& key) {
    std::lock_guard<std::mutex> lock(mutex_);

    auto it = cache_.find(key);
    if (it == cache_.end()) {
        misses_++;
        return std::nullopt;
    }

    auto& node = it->second;
    if (node.entry.isExpired()) {
        // Remove expired entry
        lruList_.erase(node.listPos);
        cache_.erase(it);
        misses_++;
        return std::nullopt;
    }

    // Update access statistics
    node.entry.updateAccess();

    // Move to front of LRU list
    moveToFront(key);

    hits_++;
    return node.entry.value;
}

template<typename Key, typename Value>
void LRUCache<Key, Value>::put(const Key& key, const Value& value,
                              std::optional<std::chrono::milliseconds> ttl) {
    std::lock_guard<std::mutex> lock(mutex_);

    auto expiry = std::chrono::steady_clock::now() + (ttl ? *ttl : defaultTtl_);

    auto it = cache_.find(key);
    if (it != cache_.end()) {
        // Update existing entry
        auto& node = it->second;
        node.entry.value = value;
        node.entry.expiry = expiry;
        node.entry.updateAccess();
        moveToFront(key);
    } else {
        // Add new entry
        if (cache_.size() >= maxSize_) {
            evictLRU();
        }

        lruList_.push_front(key);
        cache_[key] = {
            {value, expiry, std::chrono::steady_clock::now(), 1},
            lruList_.begin()
        };
    }
}

template<typename Key, typename Value>
bool LRUCache<Key, Value>::remove(const Key& key) {
    std::lock_guard<std::mutex> lock(mutex_);

    auto it = cache_.find(key);
    if (it == cache_.end()) {
        return false;
    }

    lruList_.erase(it->second.listPos);
    cache_.erase(it);
    return true;
}

template<typename Key, typename Value>
void LRUCache<Key, Value>::clear() {
    std::lock_guard<std::mutex> lock(mutex_);
    cache_.clear();
    lruList_.clear();
    hits_ = 0;
    misses_ = 0;
    evictions_ = 0;
}

template<typename Key, typename Value>
nlohmann::json LRUCache<Key, Value>::getStats() const {
    std::lock_guard<std::mutex> lock(mutex_);

    nlohmann::json stats;
    stats["size"] = cache_.size();
    stats["max_size"] = maxSize_;
    stats["hits"] = hits_;
    stats["misses"] = misses_;
    stats["evictions"] = evictions_;
    stats["hit_rate"] = (hits_ + misses_ > 0) ?
        static_cast<double>(hits_) / (hits_ + misses_) : 0.0;

    return stats;
}

template<typename Key, typename Value>
size_t LRUCache<Key, Value>::size() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return cache_.size();
}

template<typename Key, typename Value>
size_t LRUCache<Key, Value>::maxSize() const {
    return maxSize_;
}

template<typename Key, typename Value>
bool LRUCache<Key, Value>::empty() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return cache_.empty();
}

template<typename Key, typename Value>
size_t LRUCache<Key, Value>::cleanup() {
    std::lock_guard<std::mutex> lock(mutex_);

    size_t removed = 0;
    auto now = std::chrono::steady_clock::now();

    auto it = cache_.begin();
    while (it != cache_.end()) {
        if (it->second.entry.expiry < now) {
            lruList_.erase(it->second.listPos);
            it = cache_.erase(it);
            removed++;
        } else {
            ++it;
        }
    }

    return removed;
}

template<typename Key, typename Value>
void LRUCache<Key, Value>::moveToFront(const Key& key) {
    auto it = cache_.find(key);
    if (it != cache_.end()) {
        lruList_.erase(it->second.listPos);
        lruList_.push_front(key);
        it->second.listPos = lruList_.begin();
    }
}

template<typename Key, typename Value>
void LRUCache<Key, Value>::evictLRU() {
    if (!lruList_.empty()) {
        auto lru_key = lruList_.back();
        lruList_.pop_back();
        cache_.erase(lru_key);
        evictions_++;
    }
}

} // namespace dbservice::utils
