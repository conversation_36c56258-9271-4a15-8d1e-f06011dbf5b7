
function Invoke-BuildUI {
    [CmdletBinding()]
    param (
        [string]$ProjectRoot = "D:\Augment\project-tracker\database-service-ui",
        [string]$Environment = "production"
    )

    Write-Host "[Build-UI] Starting UI build in $ProjectRoot (Environment: $Environment)" -ForegroundColor Cyan

    # Initialize variables
    $script:UseGlobalVite = $false

    if (-Not (Test-Path $ProjectRoot)) {
        Write-Host "[Build-UI] UI project directory not found: $ProjectRoot" -ForegroundColor Red
        Read-Host "Press Enter to continue..." | Out-Null
        return $null
    }

    # Check for Node.js and npm
    try {
        $nodeVersion = node --version 2>$null
        if (-not $nodeVersion) {
            Write-Host "[Build-UI] Node.js is not installed or not in PATH" -ForegroundColor Red
            Read-Host "Press Enter to continue..." | Out-Null
            return $null
        }
        Write-Host "[Build-UI] Node.js version: $nodeVersion" -ForegroundColor Green
    } catch {
        Write-Host "[Build-UI] Node.js is not available: $_" -ForegroundColor Red
        Read-Host "Press Enter to continue..." | Out-Null
        return $null
    }

    try {
        $npmVersion = npm --version 2>$null
        if (-not $npmVersion) {
            Write-Host "[Build-UI] npm is not installed or not in PATH" -ForegroundColor Red
            Read-Host "Press Enter to continue..." | Out-Null
            return $null
        }
        Write-Host "[Build-UI] npm version: $npmVersion" -ForegroundColor Green
    } catch {
        Write-Host "[Build-UI] npm is not available: $_" -ForegroundColor Red
        Read-Host "Press Enter to continue..." | Out-Null
        return $null
    }

    Push-Location $ProjectRoot
    try {
        # Validate project structure
        if (-Not (Test-Path "package.json")) {
            Write-Host "[Build-UI] package.json not found in $ProjectRoot" -ForegroundColor Red
            Read-Host "Press Enter to continue..." | Out-Null
            return $null
        }

        if (-Not (Test-Path "vite.config.ts")) {
            Write-Host "[Build-UI] vite.config.ts not found in $ProjectRoot" -ForegroundColor Red
            Read-Host "Press Enter to continue..." | Out-Null
            return $null
        }

        # Clean previous build with enhanced error handling
        Write-Host "[Build-UI] Cleaning previous build..." -ForegroundColor Yellow
        $buildPath = Join-Path -Path $ProjectRoot -ChildPath "build"
        if (Test-Path $buildPath) {
            try {
                # Force remove with retry logic
                for ($i = 1; $i -le 3; $i++) {
                    try {
                        Remove-Item -Path $buildPath -Recurse -Force -ErrorAction Stop
                        Write-Host "[Build-UI] Previous build cleaned successfully" -ForegroundColor Green
                        break
                    } catch {
                        if ($i -eq 3) {
                            Write-Host "[Build-UI] Warning: Could not clean previous build after 3 attempts. Continuing..." -ForegroundColor Yellow
                            Write-Host "[Build-UI] Error: $_" -ForegroundColor Yellow
                        } else {
                            Write-Host "[Build-UI] Clean attempt $i failed, retrying..." -ForegroundColor Yellow
                            Start-Sleep -Seconds 2
                        }
                    }
                }
            } catch {
                Write-Host "[Build-UI] Warning: Could not clean previous build: $_" -ForegroundColor Yellow
                Write-Host "[Build-UI] Continuing with build..." -ForegroundColor Yellow
            }
        }

        # Set environment variables for production build
        if ($Environment -eq "production") {
            Write-Host "[Build-UI] Setting production environment variables..." -ForegroundColor Yellow
            $env:VITE_API_URL = "https://db.chcit.org/api"
            $env:VITE_APP_TITLE = "Database Service Management"
            $env:VITE_APP_VERSION = "2.0.0"
            $env:NODE_ENV = "production"
        } else {
            Write-Host "[Build-UI] Setting development environment variables..." -ForegroundColor Yellow
            $env:VITE_API_URL = "http://localhost:8080/api"
            $env:VITE_APP_TITLE = "Database Service Management (Dev)"
            $env:VITE_APP_VERSION = "2.0.0-dev"
            $env:NODE_ENV = "development"
        }

        # Check if vite is already installed, if not force a clean install
        $nodeModulesBin = Join-Path -Path $ProjectRoot -ChildPath "node_modules\.bin"
        $viteCmdPath = Join-Path -Path $nodeModulesBin -ChildPath "vite.cmd"
        $viteExePath = Join-Path -Path $nodeModulesBin -ChildPath "vite"

        $viteInstalled = (Test-Path $viteCmdPath) -or (Test-Path $viteExePath)

        if (-not $viteInstalled) {
            Write-Host "[Build-UI] Vite not found in node_modules, performing clean install..." -ForegroundColor Yellow

            # Remove node_modules and package-lock.json for clean install
            $nodeModulesPath = Join-Path -Path $ProjectRoot -ChildPath "node_modules"
            $packageLockPath = Join-Path -Path $ProjectRoot -ChildPath "package-lock.json"

            if (Test-Path $nodeModulesPath) {
                Write-Host "[Build-UI] Removing existing node_modules..." -ForegroundColor Yellow
                try {
                    Remove-Item -Path $nodeModulesPath -Recurse -Force -ErrorAction Stop
                    Write-Host "[Build-UI] node_modules removed successfully" -ForegroundColor Green
                } catch {
                    Write-Host "[Build-UI] Warning: Could not remove node_modules: $_" -ForegroundColor Yellow
                }
            }

            if (Test-Path $packageLockPath) {
                Write-Host "[Build-UI] Removing package-lock.json..." -ForegroundColor Yellow
                try {
                    Remove-Item -Path $packageLockPath -Force -ErrorAction Stop
                    Write-Host "[Build-UI] package-lock.json removed successfully" -ForegroundColor Green
                } catch {
                    Write-Host "[Build-UI] Warning: Could not remove package-lock.json: $_" -ForegroundColor Yellow
                }
            }
        }

        # Install dependencies using cmd.exe for better Windows compatibility
        Write-Host "[Build-UI] Installing dependencies..." -ForegroundColor Yellow

        # Use cmd.exe to execute npm commands for better Windows compatibility
        $installCommand = "npm install"
        $installResult = cmd.exe /c $installCommand 2>&1
        $installExitCode = $LASTEXITCODE

        if ($installExitCode -ne 0) {
            Write-Host "[Build-UI] npm install failed with exit code: $installExitCode" -ForegroundColor Red
            Write-Host "[Build-UI] Output:" -ForegroundColor Red
            $installResult | Write-Host
            Read-Host "Press Enter to continue..." | Out-Null
            return $null
        }

        Write-Host "[Build-UI] Dependencies installed successfully" -ForegroundColor Green
        if ($installResult) {
            Write-Host "[Build-UI] Install output:" -ForegroundColor Cyan
            $installResult | Write-Host
        }

        # Post-install verification: Check if vite is now installed
        Write-Host "[Build-UI] Post-install verification:" -ForegroundColor Cyan
        Write-Host "  - node_modules\.bin exists: $(Test-Path $nodeModulesBin)" -ForegroundColor Cyan
        Write-Host "  - vite.cmd exists: $(Test-Path $viteCmdPath)" -ForegroundColor Cyan
        Write-Host "  - vite exists: $(Test-Path $viteExePath)" -ForegroundColor Cyan

        if (Test-Path $nodeModulesBin) {
            $binFiles = Get-ChildItem -Path $nodeModulesBin -Name | Where-Object { $_ -like "*vite*" }
            Write-Host "  - Vite-related files in .bin: $($binFiles -join ', ')" -ForegroundColor Cyan

            # List all files in .bin for debugging
            $allBinFiles = Get-ChildItem -Path $nodeModulesBin -Name | Sort-Object
            Write-Host "  - All files in .bin: $($allBinFiles -join ', ')" -ForegroundColor Gray
        }

        # Check if vite package is installed in node_modules
        $vitePackagePath = Join-Path -Path $ProjectRoot -ChildPath "node_modules\vite"
        Write-Host "  - vite package directory exists: $(Test-Path $vitePackagePath)" -ForegroundColor Cyan

        # If vite is still not found, try multiple installation approaches
        if (-not ((Test-Path $viteCmdPath) -or (Test-Path $viteExePath))) {
            Write-Host "[Build-UI] Vite still not found after npm install, trying multiple approaches..." -ForegroundColor Yellow

            # Approach 1: Install vite explicitly
            Write-Host "[Build-UI] Approach 1: Installing vite explicitly..." -ForegroundColor Cyan
            $viteInstallCommand = "npm install vite@^4.4.5 --save-dev"
            $viteInstallResult = cmd.exe /c $viteInstallCommand 2>&1
            $viteInstallExitCode = $LASTEXITCODE

            Write-Host "[Build-UI] Vite install output:" -ForegroundColor Gray
            $viteInstallResult | Write-Host

            if ($viteInstallExitCode -eq 0) {
                Write-Host "[Build-UI] Explicit vite install completed with exit code 0" -ForegroundColor Green
            } else {
                Write-Host "[Build-UI] Explicit vite install failed with exit code: $viteInstallExitCode" -ForegroundColor Red
            }

            # Check again after explicit install
            Write-Host "  - vite.cmd exists now: $(Test-Path $viteCmdPath)" -ForegroundColor Cyan
            Write-Host "  - vite exists now: $(Test-Path $viteExePath)" -ForegroundColor Cyan
            Write-Host "  - vite package directory exists now: $(Test-Path $vitePackagePath)" -ForegroundColor Cyan

            # If still not found, try approach 2
            if (-not ((Test-Path $viteCmdPath) -or (Test-Path $viteExePath))) {
                Write-Host "[Build-UI] Approach 2: Installing all devDependencies explicitly..." -ForegroundColor Cyan
                $devInstallCommand = "npm install --include=dev"
                $devInstallResult = cmd.exe /c $devInstallCommand 2>&1
                $devInstallExitCode = $LASTEXITCODE

                Write-Host "[Build-UI] Dev dependencies install output (exit code: $devInstallExitCode):" -ForegroundColor Gray
                $devInstallResult | Write-Host

                # Check again after dev install
                Write-Host "  - vite.cmd exists after dev install: $(Test-Path $viteCmdPath)" -ForegroundColor Cyan
                Write-Host "  - vite exists after dev install: $(Test-Path $viteExePath)" -ForegroundColor Cyan

                # If still not found, try approach 3
                if (-not ((Test-Path $viteCmdPath) -or (Test-Path $viteExePath))) {
                    Write-Host "[Build-UI] Approach 3: Installing vite globally as fallback..." -ForegroundColor Cyan
                    $globalViteCommand = "npm install -g vite@^4.4.5"
                    $globalViteResult = cmd.exe /c $globalViteCommand 2>&1
                    $globalViteExitCode = $LASTEXITCODE

                    Write-Host "[Build-UI] Global vite install output:" -ForegroundColor Gray
                    $globalViteResult | Write-Host

                    if ($globalViteExitCode -eq 0) {
                        Write-Host "[Build-UI] Global vite install successful - will use global vite" -ForegroundColor Green
                        # Set a flag to use global vite
                        $script:UseGlobalVite = $true
                    } else {
                        Write-Host "[Build-UI] All vite installation approaches failed" -ForegroundColor Red
                        Read-Host "Press Enter to continue..." | Out-Null
                        return $null
                    }
                }
            }
        }

        # Run build using multiple approaches
        Write-Host "[Build-UI] Building UI for $Environment..." -ForegroundColor Yellow

        # Initialize build variables
        $buildExitCode = 1
        $buildResult = ""

        # Check if we should use global vite
        if ($script:UseGlobalVite) {
            Write-Host "[Build-UI] Using global vite for build..." -ForegroundColor Cyan

            # Try different global vite approaches
            $globalBuildSuccess = $false

            # Approach 1: Direct global vite command
            Write-Host "[Build-UI] Trying global vite build..." -ForegroundColor Cyan
            $buildCommand = "vite build"
            $buildResult = cmd.exe /c $buildCommand 2>&1
            $buildExitCode = $LASTEXITCODE

            if ($buildExitCode -eq 0) {
                $globalBuildSuccess = $true
                Write-Host "[Build-UI] Global vite build successful!" -ForegroundColor Green
            } else {
                Write-Host "[Build-UI] Global vite build failed, trying npx with global..." -ForegroundColor Yellow

                # Approach 2: npx with global vite
                $buildCommand = "npx --prefer-global vite build"
                $buildResult = cmd.exe /c $buildCommand 2>&1
                $buildExitCode = $LASTEXITCODE

                if ($buildExitCode -eq 0) {
                    $globalBuildSuccess = $true
                    Write-Host "[Build-UI] Global npx vite build successful!" -ForegroundColor Green
                } else {
                    Write-Host "[Build-UI] Global npx vite build also failed" -ForegroundColor Yellow
                }
            }

            # If global approaches failed, fall back to local attempts
            if (-not $globalBuildSuccess) {
                Write-Host "[Build-UI] Global vite approaches failed, falling back to local attempts..." -ForegroundColor Yellow
                $script:UseGlobalVite = $false
            }
        }

        # If not using global vite or global vite failed, try local approaches
        if (-not $script:UseGlobalVite) {
            # First try with npx vite build directly
            Write-Host "[Build-UI] Attempting build with npx vite build..." -ForegroundColor Cyan
            $buildCommand = "npx vite build"
            $buildResult = cmd.exe /c $buildCommand 2>&1
            $buildExitCode = $LASTEXITCODE

            if ($buildExitCode -ne 0) {
                Write-Host "[Build-UI] npx vite build failed, trying alternative approach..." -ForegroundColor Yellow

                # Alternative: Use node_modules/.bin/vite directly
                $vitePath = Join-Path -Path $ProjectRoot -ChildPath "node_modules\.bin\vite.cmd"
                if (Test-Path $vitePath) {
                    Write-Host "[Build-UI] Using local vite.cmd: $vitePath" -ForegroundColor Cyan
                    $buildCommand = "`"$vitePath`" build"
                    $buildResult = cmd.exe /c $buildCommand 2>&1
                    $buildExitCode = $LASTEXITCODE
                } else {
                    Write-Host "[Build-UI] vite.cmd not found at: $vitePath" -ForegroundColor Red

                    # Final fallback: Try to run npm run build with explicit PATH
                    $nodeModulesBin = Join-Path -Path $ProjectRoot -ChildPath "node_modules\.bin"
                    if (Test-Path $nodeModulesBin) {
                        Write-Host "[Build-UI] Adding node_modules\.bin to PATH and retrying npm run build..." -ForegroundColor Cyan
                        $originalPath = $env:PATH
                        $env:PATH = "$nodeModulesBin;$env:PATH"

                        $buildCommand = "npm run build"
                        $buildResult = cmd.exe /c $buildCommand 2>&1
                        $buildExitCode = $LASTEXITCODE

                        # Restore original PATH
                        $env:PATH = $originalPath
                    } else {
                        Write-Host "[Build-UI] node_modules\.bin directory not found" -ForegroundColor Red
                        Read-Host "Press Enter to continue..." | Out-Null
                        return $null
                    }
                }
            }
        }

        if ($buildExitCode -ne 0) {
            Write-Host "[Build-UI] All build attempts failed with exit code: $buildExitCode" -ForegroundColor Red
            Write-Host "[Build-UI] Final output:" -ForegroundColor Red
            $buildResult | Write-Host
            Read-Host "Press Enter to continue..." | Out-Null
            return $null
        }

        Write-Host "[Build-UI] Build completed successfully!" -ForegroundColor Green
        if ($buildResult) {
            Write-Host "[Build-UI] Build output:" -ForegroundColor Green
            $buildResult | Write-Host
        }

        # Validate build output
        $buildOutputPath = Join-Path -Path $ProjectRoot -ChildPath "build"
        if (-Not (Test-Path $buildOutputPath)) {
            Write-Host "[Build-UI] Build output directory not found: $buildOutputPath" -ForegroundColor Red
            Read-Host "Press Enter to continue..." | Out-Null
            return $null
        }

        # Validate build contents
        $indexHtml = Join-Path -Path $buildOutputPath -ChildPath "index.html"
        $assetsDir = Join-Path -Path $buildOutputPath -ChildPath "assets"

        if (-Not (Test-Path $indexHtml)) {
            Write-Host "[Build-UI] index.html not found in build output" -ForegroundColor Red
            Read-Host "Press Enter to continue..." | Out-Null
            return $null
        }

        if (-Not (Test-Path $assetsDir)) {
            Write-Host "[Build-UI] assets directory not found in build output" -ForegroundColor Red
            Read-Host "Press Enter to continue..." | Out-Null
            return $null
        }

        # Check for JavaScript and CSS files
        $jsFiles = Get-ChildItem -Path $assetsDir -Filter "*.js" -ErrorAction SilentlyContinue
        $cssFiles = Get-ChildItem -Path $assetsDir -Filter "*.css" -ErrorAction SilentlyContinue

        if ($jsFiles.Count -eq 0) {
            Write-Host "[Build-UI] Warning: No JavaScript files found in assets directory" -ForegroundColor Yellow
        }

        if ($cssFiles.Count -eq 0) {
            Write-Host "[Build-UI] Warning: No CSS files found in assets directory" -ForegroundColor Yellow
        }

        # Display build summary
        $buildSize = (Get-ChildItem -Path $buildOutputPath -Recurse | Measure-Object -Property Length -Sum).Sum
        $buildSizeMB = [math]::Round($buildSize / 1MB, 2)

        Write-Host "[Build-UI] Build completed successfully!" -ForegroundColor Green
        Write-Host "[Build-UI] Output directory: $buildOutputPath" -ForegroundColor Green
        Write-Host "[Build-UI] Build size: $buildSizeMB MB" -ForegroundColor Green
        Write-Host "[Build-UI] Files created:" -ForegroundColor Green
        Write-Host "  - index.html: $(Test-Path $indexHtml)" -ForegroundColor Green
        Write-Host "  - JavaScript files: $($jsFiles.Count)" -ForegroundColor Green
        Write-Host "  - CSS files: $($cssFiles.Count)" -ForegroundColor Green
        Write-Host "  - Environment: $Environment" -ForegroundColor Green

        return $buildOutputPath

    } catch {
        Write-Host "[Build-UI] Error during UI build: $_" -ForegroundColor Red
        Write-Host "[Build-UI] Stack trace: $($_.ScriptStackTrace)" -ForegroundColor Red
        Read-Host "Press Enter to continue..." | Out-Null
        return $null
    } finally {
        Pop-Location
        # Clean up environment variables
        Remove-Item Env:VITE_API_URL -ErrorAction SilentlyContinue
        Remove-Item Env:VITE_APP_TITLE -ErrorAction SilentlyContinue
        Remove-Item Env:VITE_APP_VERSION -ErrorAction SilentlyContinue
        Remove-Item Env:NODE_ENV -ErrorAction SilentlyContinue
    }
}

Export-ModuleMember -Function Invoke-BuildUI
