Running CMake configuration...
-- The CXX compiler identification is GNU 14.2.0
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /usr/bin/g++-14 - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found PkgConfig: /usr/bin/pkg-config (found version "1.8.1") 
-- Found Boost: /usr/lib/x86_64-linux-gnu/cmake/Boost-1.83.0/BoostConfig.cmake (found version "1.83.0") found components: system program_options 
-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.13")  
-- Checking for module 'libpqxx'
--   Found libpqxx, version 7.8.1
-- Found nlohmann_json: /usr/share/cmake/nlohmann_json/nlohmann_jsonConfig.cmake (found version "3.11.3") 
-- Configuring done (0.3s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/auth-service-build/build
Starting compilation...
[ 12%] Building CXX object CMakeFiles/auth-service.dir/src/database_manager.cpp.o
[ 25%] Building CXX object CMakeFiles/auth-service.dir/src/auth_service.cpp.o
[ 37%] Building CXX object CMakeFiles/auth-service.dir/src/security_manager.cpp.o
[ 50%] Building CXX object CMakeFiles/auth-service.dir/src/main.cpp.o
[ 62%] Building CXX object CMakeFiles/auth-service.dir/src/config_manager.cpp.o
[ 75%] Building CXX object CMakeFiles/auth-service.dir/src/http_server.cpp.o
[ 87%] Building CXX object CMakeFiles/auth-service.dir/src/user_manager.cpp.o
[100%] Linking CXX executable auth-service
/usr/bin/ld: CMakeFiles/auth-service.dir/src/security_manager.cpp.o: in function `SecurityManager::hash_password_with_salt(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&) const':
security_manager.cpp:(.text+0x1f7a): undefined reference to `argon2id_hash_raw'
/usr/bin/ld: security_manager.cpp:(.text+0x1fac): undefined reference to `argon2_error_message'
collect2: error: ld returned 1 exit status
make[2]: *** [CMakeFiles/auth-service.dir/build.make:197: auth-service] Error 1
make[1]: *** [CMakeFiles/Makefile2:83: CMakeFiles/auth-service.dir/all] Error 2
make: *** [Makefile:136: all] Error 2
