# Database Integration Testing Script - Step 6
# Tests complete database operations and security

param(
    [string]$Environment = "development",
    [string]$Server = "dev.chcit.org",
    [int]$Port = 8082,
    [switch]$Verbose
)

Write-Host "=== Database Integration Testing (Step 6) ===" -ForegroundColor Cyan
Write-Host "Environment: $Environment" -ForegroundColor Yellow
Write-Host "Server: $Server" -ForegroundColor Yellow
Write-Host "Port: $Port" -ForegroundColor Yellow
Write-Host ""

# Test configuration
$baseUrl = "http://${Server}:${Port}"
$testUser = @{
    username = "testuser"
    email = "<EMAIL>"
    password = "testpassword"
}

# Test results tracking
$testResults = @{
    total = 0
    passed = 0
    failed = 0
    errors = @()
}

function Write-TestResult {
    param(
        [string]$TestName,
        [bool]$Success,
        [string]$Message = ""
    )
    
    $testResults.total++
    
    if ($Success) {
        $testResults.passed++
        Write-Host "✅ $TestName" -ForegroundColor Green
        if ($Message -and $Verbose) {
            Write-Host "   $Message" -ForegroundColor Gray
        }
    } else {
        $testResults.failed++
        $testResults.errors += "$TestName - $Message"
        Write-Host "❌ $TestName" -ForegroundColor Red
        if ($Message) {
            Write-Host "   Error: $Message" -ForegroundColor Red
        }
    }
}

function Test-HttpEndpoint {
    param(
        [string]$Url,
        [string]$Method = "GET",
        [hashtable]$Headers = @{},
        [string]$Body = $null
    )
    
    try {
        $params = @{
            Uri = $Url
            Method = $Method
            Headers = $Headers
            UseBasicParsing = $true
            TimeoutSec = 30
        }
        
        if ($Body) {
            $params.Body = $Body
            $params.ContentType = "application/json"
        }
        
        $response = Invoke-WebRequest @params
        return @{
            Success = $true
            StatusCode = $response.StatusCode
            Content = $response.Content
            Headers = $response.Headers
        }
    } catch {
        return @{
            Success = $false
            Error = $_.Exception.Message
            StatusCode = if ($_.Exception.Response) { $_.Exception.Response.StatusCode } else { 0 }
        }
    }
}

# Test 1: Database Connection via Health Check
Write-Host "1. Testing database connection via health check..." -ForegroundColor Yellow
$healthTest = Test-HttpEndpoint -Url "$baseUrl/health" -Method "GET"
Write-TestResult -TestName "Health Check Response" -Success $healthTest.Success -Message $healthTest.Error

if ($healthTest.Success) {
    try {
        $healthResponse = $healthTest.Content | ConvertFrom-Json
        Write-TestResult -TestName "Service Status" -Success ($healthResponse.status -eq "healthy") -Message "Status: $($healthResponse.status)"
        Write-TestResult -TestName "Database Connectivity" -Success ($healthResponse.oauth2_endpoints.Count -eq 4) -Message "All endpoints available"
    } catch {
        Write-TestResult -TestName "Health Response Parsing" -Success $false -Message $_.Exception.Message
    }
}

# Test 2: Database User Authentication
Write-Host "`n2. Testing database user authentication..." -ForegroundColor Yellow

$tokenRequestBody = @{
    username = $testUser.username
    password = $testUser.password
    grant_type = "password"
} | ConvertTo-Json

$tokenTest = Test-HttpEndpoint -Url "$baseUrl/oauth/token" -Method "POST" -Body $tokenRequestBody
Write-TestResult -TestName "Database User Lookup" -Success $tokenTest.Success -Message $tokenTest.Error

$accessToken = $null
$refreshToken = $null
$userId = $null

if ($tokenTest.Success) {
    try {
        $tokenResponse = $tokenTest.Content | ConvertFrom-Json
        $accessToken = $tokenResponse.access_token
        $refreshToken = $tokenResponse.refresh_token
        
        Write-TestResult -TestName "JWT Token Generation" -Success ($null -ne $accessToken) -Message "Token length: $($accessToken.Length)"
        Write-TestResult -TestName "Database Token Storage" -Success ($null -ne $refreshToken) -Message "Refresh token stored"
        Write-TestResult -TestName "OAuth 2.0 Compliance" -Success ($tokenResponse.token_type -eq "Bearer") -Message "Bearer token type"
        
        if ($Verbose) {
            Write-Host "   Access Token: $($accessToken.Substring(0, [Math]::Min(50, $accessToken.Length)))..." -ForegroundColor Gray
        }
    } catch {
        Write-TestResult -TestName "Token Response Parsing" -Success $false -Message $_.Exception.Message
    }
}

# Test 3: Database Token Validation
Write-Host "`n3. Testing database token validation..." -ForegroundColor Yellow

if ($accessToken) {
    $headers = @{
        "Authorization" = "Bearer $accessToken"
    }
    
    $validateTest = Test-HttpEndpoint -Url "$baseUrl/oauth/validate" -Method "POST" -Headers $headers
    Write-TestResult -TestName "Token Database Lookup" -Success $validateTest.Success -Message $validateTest.Error
    
    if ($validateTest.Success) {
        try {
            $validationResponse = $validateTest.Content | ConvertFrom-Json
            $userId = $validationResponse.user_id
            
            Write-TestResult -TestName "Token Validation" -Success ($validationResponse.valid -eq $true) -Message "Token is valid"
            Write-TestResult -TestName "UUID User ID" -Success ($userId.Length -eq 36) -Message "User ID: $userId"
            Write-TestResult -TestName "Scope Retrieval" -Success ($validationResponse.scopes.Count -gt 0) -Message "Scopes: $($validationResponse.scopes -join ', ')"
            Write-TestResult -TestName "Expiration Check" -Success ($validationResponse.expires_at -gt 0) -Message "Expires: $($validationResponse.expires_at)"
        } catch {
            Write-TestResult -TestName "Validation Response Parsing" -Success $false -Message $_.Exception.Message
        }
    }
} else {
    Write-TestResult -TestName "Token Database Lookup" -Success $false -Message "No access token available"
}

# Test 4: Database Token Refresh
Write-Host "`n4. Testing database token refresh..." -ForegroundColor Yellow

if ($refreshToken) {
    $refreshRequestBody = @{
        refresh_token = $refreshToken
        grant_type = "refresh_token"
    } | ConvertTo-Json
    
    $refreshTest = Test-HttpEndpoint -Url "$baseUrl/oauth/refresh" -Method "POST" -Body $refreshRequestBody
    Write-TestResult -TestName "Refresh Token Lookup" -Success $refreshTest.Success -Message $refreshTest.Error
    
    if ($refreshTest.Success) {
        try {
            $refreshResponse = $refreshTest.Content | ConvertFrom-Json
            $newAccessToken = $refreshResponse.access_token
            $newRefreshToken = $refreshResponse.refresh_token
            
            Write-TestResult -TestName "New Token Generation" -Success ($null -ne $newAccessToken) -Message "New access token created"
            Write-TestResult -TestName "New Token Storage" -Success ($null -ne $newRefreshToken) -Message "New refresh token stored"
            Write-TestResult -TestName "Token Rotation" -Success ($newAccessToken -ne $accessToken) -Message "Tokens properly rotated"
            
        } catch {
            Write-TestResult -TestName "Refresh Response Parsing" -Success $false -Message $_.Exception.Message
        }
    }
} else {
    Write-TestResult -TestName "Refresh Token Lookup" -Success $false -Message "No refresh token available"
}

# Test 5: Database Token Revocation
Write-Host "`n5. Testing database token revocation..." -ForegroundColor Yellow

if ($accessToken) {
    $revokeRequestBody = @{
        token = $accessToken
    } | ConvertTo-Json
    
    $revokeTest = Test-HttpEndpoint -Url "$baseUrl/oauth/revoke" -Method "POST" -Body $revokeRequestBody
    Write-TestResult -TestName "Token Revocation" -Success $revokeTest.Success -Message $revokeTest.Error
    
    if ($revokeTest.Success) {
        Write-TestResult -TestName "Database Update" -Success ($revokeTest.StatusCode -eq 200) -Message "Token marked as revoked"
        
        # Test that revoked token is no longer valid
        Start-Sleep -Seconds 1
        
        $headers = @{
            "Authorization" = "Bearer $accessToken"
        }
        
        $revokedValidateTest = Test-HttpEndpoint -Url "$baseUrl/oauth/validate" -Method "POST" -Headers $headers
        Write-TestResult -TestName "Revoked Token Rejection" -Success (-not $revokedValidateTest.Success) -Message "Revoked token properly rejected"
    }
} else {
    Write-TestResult -TestName "Token Revocation" -Success $false -Message "No access token available"
}

# Test 6: Database Security Checks
Write-Host "`n6. Testing database security..." -ForegroundColor Yellow

# Test SQL injection protection
$sqlInjectionBody = @{
    username = "admin'; DROP TABLE auth_users; --"
    password = "password"
    grant_type = "password"
} | ConvertTo-Json

$sqlInjectionTest = Test-HttpEndpoint -Url "$baseUrl/oauth/token" -Method "POST" -Body $sqlInjectionBody
Write-TestResult -TestName "SQL Injection Protection" -Success (-not $sqlInjectionTest.Success) -Message "Malicious SQL rejected"

# Test invalid user
$invalidUserBody = @{
    username = "nonexistentuser12345"
    password = "password"
    grant_type = "password"
} | ConvertTo-Json

$invalidUserTest = Test-HttpEndpoint -Url "$baseUrl/oauth/token" -Method "POST" -Body $invalidUserBody
Write-TestResult -TestName "Invalid User Handling" -Success (-not $invalidUserTest.Success) -Message "Invalid user properly rejected"

# Test wrong password
$wrongPasswordBody = @{
    username = $testUser.username
    password = "wrongpassword"
    grant_type = "password"
} | ConvertTo-Json

$wrongPasswordTest = Test-HttpEndpoint -Url "$baseUrl/oauth/token" -Method "POST" -Body $wrongPasswordBody
Write-TestResult -TestName "Password Verification" -Success (-not $wrongPasswordTest.Success) -Message "Wrong password rejected"

# Test Summary
Write-Host "`n=== Database Integration Testing Summary ===" -ForegroundColor Cyan
Write-Host "Total Tests: $($testResults.total)" -ForegroundColor White
Write-Host "Passed: $($testResults.passed)" -ForegroundColor Green
Write-Host "Failed: $($testResults.failed)" -ForegroundColor Red

if ($testResults.failed -gt 0) {
    Write-Host "`nFailed Tests:" -ForegroundColor Red
    foreach ($testError in $testResults.errors) {
        Write-Host "  - $testError" -ForegroundColor Red
    }
}

$successRate = if ($testResults.total -gt 0) { 
    [math]::Round(($testResults.passed / $testResults.total) * 100, 2) 
} else { 0 }

Write-Host "`nSuccess Rate: $successRate%" -ForegroundColor $(if ($successRate -ge 90) { "Green" } elseif ($successRate -ge 70) { "Yellow" } else { "Red" })

if ($successRate -ge 90) {
    Write-Host "`n🎉 Database integration is working excellently!" -ForegroundColor Green
    Write-Host "✅ PostgreSQL connection established" -ForegroundColor Green
    Write-Host "✅ User authentication with database" -ForegroundColor Green
    Write-Host "✅ JWT token storage and retrieval" -ForegroundColor Green
    Write-Host "✅ Database security measures working" -ForegroundColor Green
} elseif ($successRate -ge 70) {
    Write-Host "`n⚠️  Database integration has some issues that need attention." -ForegroundColor Yellow
} else {
    Write-Host "`n❌ Database integration needs significant fixes." -ForegroundColor Red
}

Write-Host "`nStep 6: Database Operations Integration testing completed." -ForegroundColor Cyan
