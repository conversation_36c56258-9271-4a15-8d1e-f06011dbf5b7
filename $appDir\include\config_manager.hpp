﻿#pragma once
#include <string>
#include <nlohmann/json.hpp>

class ConfigManager {
public:
    explicit ConfigManager(const std::string& config_file);
    std::string get_database_host() const;
    int get_database_port() const;
    std::string get_database_name() const;
    std::string get_database_user() const;
    std::string get_database_password() const;
    int get_server_port() const;
    std::string get_log_level() const;
private:
    nlohmann::json config_;
    void load_config(const std::string& config_file);
};
