#pragma once
#include <string>

class DatabaseManager;
class SecurityManager;

class UserManager {
public:
    UserManager(DatabaseManager* db_manager, SecurityManager* sec_manager);
    ~UserManager();
    
    bool create_user(const std::string& username, const std::string& password);
    bool authenticate_user(const std::string& username, const std::string& password);
    bool delete_user(const std::string& username);
    
private:
    DatabaseManager* db_manager_;
    SecurityManager* sec_manager_;
};
