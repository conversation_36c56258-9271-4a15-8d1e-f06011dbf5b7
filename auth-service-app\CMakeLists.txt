cmake_minimum_required(VERSION 3.20)
project(auth-service VERSION 1.0.0 LANGUAGES CXX)

# Set C++23 standard
set(CMAKE_CXX_STANDARD 23)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Compiler-specific options
if(CMAKE_CXX_COMPILER_ID STREQUAL "GNU")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -O2")
endif()

# Find required packages
find_package(PkgConfig REQUIRED)
find_package(Boost REQUIRED COMPONENTS system program_options)
find_package(OpenSSL REQUIRED)

# Find PostgreSQL
pkg_check_modules(PQXX REQUIRED libpqxx)

# Find nlohmann/json
find_package(nlohmann_json REQUIRED)

# Include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include)

# Source files
set(SOURCES
    src/main.cpp
    src/auth_service.cpp
    src/database_manager.cpp
    src/security_manager.cpp
    src/config_manager.cpp
    src/http_server.cpp
    src/user_manager.cpp
)

# Create executable
add_executable(auth-service ${SOURCES})

# Link libraries
target_link_libraries(auth-service
    ${Boost_LIBRARIES}
    ${PQXX_LIBRARIES}
    OpenSSL::SSL
    OpenSSL::Crypto
    nlohmann_json::nlohmann_json
    pthread
)

# Include directories for dependencies
target_include_directories(auth-service PRIVATE
    ${Boost_INCLUDE_DIRS}
    ${PQXX_INCLUDE_DIRS}
)

# Compiler definitions
target_compile_definitions(auth-service PRIVATE
    ${PQXX_CFLAGS_OTHER}
)

# Installation
install(TARGETS auth-service
    RUNTIME DESTINATION bin
)

# Install configuration files
install(FILES config/auth-service.conf
    DESTINATION etc/auth-service
)
