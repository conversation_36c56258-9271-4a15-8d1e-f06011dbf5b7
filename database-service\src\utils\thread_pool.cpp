#include "database-service/utils/thread_pool.hpp"
#include <format>

namespace dbservice::utils {

ThreadPool::ThreadPool(size_t threads) : stop_(false), activeTasks_(0) {
    if (threads == 0) {
        threads = std::thread::hardware_concurrency();
        if (threads == 0) {
            threads = 4; // Fallback to 4 threads
        }
    }

    workers_.reserve(threads);
    
    for (size_t i = 0; i < threads; ++i) {
        workers_.emplace_back([this] {
            for (;;) {
                std::function<void()> task;

                {
                    std::unique_lock<std::mutex> lock(this->queueMutex_);
                    this->condition_.wait(lock, [this] { 
                        return this->stop_ || !this->tasks_.empty(); 
                    });
                    
                    if (this->stop_ && this->tasks_.empty()) {
                        return;
                    }
                    
                    task = std::move(this->tasks_.front());
                    this->tasks_.pop();
                }

                // Increment active task counter
                this->activeTasks_.fetch_add(1);
                
                try {
                    task();
                } catch (const std::exception& e) {
                    // Log error but don't let it crash the worker thread
                    // In a real implementation, you might want to use a proper logger
                    // For now, we'll just silently handle the exception
                } catch (...) {
                    // Handle any other exceptions
                }
                
                // Decrement active task counter
                this->activeTasks_.fetch_sub(1);
            }
        });
    }
}

ThreadPool::~ThreadPool() {
    {
        std::unique_lock<std::mutex> lock(queueMutex_);
        stop_ = true;
    }
    
    condition_.notify_all();
    
    for (std::thread &worker : workers_) {
        if (worker.joinable()) {
            worker.join();
        }
    }
}

size_t ThreadPool::getThreadCount() const {
    return workers_.size();
}

size_t ThreadPool::getPendingTaskCount() const {
    std::unique_lock<std::mutex> lock(queueMutex_);
    return tasks_.size();
}

size_t ThreadPool::getActiveTaskCount() const {
    return activeTasks_.load();
}

bool ThreadPool::isShuttingDown() const {
    return stop_.load();
}

} // namespace dbservice::utils
