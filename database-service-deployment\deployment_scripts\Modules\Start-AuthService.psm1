# Start-AuthService.psm1 - Module for starting and managing the auth service

<#
.SYNOPSIS
    Provides functionality for starting and managing the auth service.

.DESCRIPTION
    This module handles starting, stopping, and monitoring the auth service,
    including status checks and log monitoring.

.NOTES
    File Name      : Start-AuthService.psm1
    Author         : Auth Service Deployment Team
    Prerequisite   : PowerShell 5.1 or later
    Copyright      : (c) 2025 Augment
#>

# Import required modules
try {
    # Import common module
    if (-not (Get-Module -Name "Common")) {
        Import-Module -Name "$PSScriptRoot\..\Common.psm1" -Force -ErrorAction Stop
    }

    # Import Logger module
    if (-not (Get-Module -Name "Logger")) {
        Import-Module -Name "$PSScriptRoot\Logger\Logger.psm1" -Force -ErrorAction Stop
    }

    # Import SSHManager module
    if (-not (Get-Module -Name "SSHManager")) {
        Import-Module -Name "$PSScriptRoot\SSHManager.psm1" -Force -ErrorAction Stop
    }
} catch {
    Write-Host "Error loading required modules: $_" -ForegroundColor Red
}

function Start-AuthService {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory=$false)]
        [switch]$Stop,
        
        [Parameter(Mandatory=$false)]
        [switch]$Restart,
        
        [Parameter(Mandatory=$false)]
        [switch]$Status,
        
        [Parameter(Mandatory=$false)]
        [switch]$Logs
    )

    Write-Host "=== Auth Service Management ===" -ForegroundColor Cyan
    Write-Host ""

    $serviceName = $script:Config.service.name

    if ($Stop) {
        Write-Host "Stopping auth service..." -ForegroundColor Yellow
        
        $stopCmd = "sudo systemctl stop $serviceName"
        
        try {
            $sshCommand = "ssh -i `"$($script:Config.ssh.key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$($script:Config.ssh.host) `"$stopCmd`""
            $result = Invoke-Expression $sshCommand
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ Auth service stopped successfully" -ForegroundColor Green
            } else {
                Write-Host "❌ Failed to stop auth service" -ForegroundColor Red
                return $false
            }
        } catch {
            Write-Host "❌ Error stopping service: $_" -ForegroundColor Red
            return $false
        }
    }
    elseif ($Restart) {
        Write-Host "Restarting auth service..." -ForegroundColor Yellow
        
        $restartCmd = "sudo systemctl restart $serviceName"
        
        try {
            $sshCommand = "ssh -i `"$($script:Config.ssh.key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$($script:Config.ssh.host) `"$restartCmd`""
            $result = Invoke-Expression $sshCommand
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ Auth service restarted successfully" -ForegroundColor Green
            } else {
                Write-Host "❌ Failed to restart auth service" -ForegroundColor Red
                return $false
            }
        } catch {
            Write-Host "❌ Error restarting service: $_" -ForegroundColor Red
            return $false
        }
    }
    elseif ($Status) {
        Write-Host "Checking auth service status..." -ForegroundColor Yellow
        
        $statusCmd = "sudo systemctl status $serviceName --no-pager"
        
        try {
            $sshCommand = "ssh -i `"$($script:Config.ssh.key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$($script:Config.ssh.host) `"$statusCmd`""
            $result = Invoke-Expression $sshCommand
            
            Write-Host $result -ForegroundColor White
        } catch {
            Write-Host "❌ Error checking service status: $_" -ForegroundColor Red
            return $false
        }
    }
    elseif ($Logs) {
        Write-Host "Showing auth service logs..." -ForegroundColor Yellow
        
        $logsCmd = "sudo journalctl -u $serviceName --no-pager -n 50"
        
        try {
            $sshCommand = "ssh -i `"$($script:Config.ssh.key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$($script:Config.ssh.host) `"$logsCmd`""
            $result = Invoke-Expression $sshCommand
            
            Write-Host $result -ForegroundColor White
        } catch {
            Write-Host "❌ Error retrieving service logs: $_" -ForegroundColor Red
            return $false
        }
    }
    else {
        # Default action: start the service
        Write-Host "Starting auth service..." -ForegroundColor Yellow
        
        $startCmd = "sudo systemctl start $serviceName"
        
        try {
            $sshCommand = "ssh -i `"$($script:Config.ssh.key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$($script:Config.ssh.host) `"$startCmd`""
            $result = Invoke-Expression $sshCommand
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ Auth service started successfully" -ForegroundColor Green
                
                # Check status after starting
                Start-Sleep -Seconds 2
                Write-Host ""
                Write-Host "Service status:" -ForegroundColor Yellow
                Start-AuthService -Status
            } else {
                Write-Host "❌ Failed to start auth service" -ForegroundColor Red
                return $false
            }
        } catch {
            Write-Host "❌ Error starting service: $_" -ForegroundColor Red
            return $false
        }
    }

    return $true
}

# Export the function
Export-ModuleMember -Function Start-AuthService
