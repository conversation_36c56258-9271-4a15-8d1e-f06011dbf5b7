Running CMake configuration...
-- The CXX compiler identification is GNU 14.2.0
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /usr/bin/g++-14 - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found PkgConfig: /usr/bin/pkg-config (found version "1.8.1") 
-- Found Boost: /usr/lib/x86_64-linux-gnu/cmake/Boost-1.83.0/BoostConfig.cmake (found version "1.83.0") found components: system program_options 
-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.13")  
-- Checking for module 'libpqxx'
--   Found libpqxx, version 7.8.1
-- Found nlohmann_json: /usr/share/cmake/nlohmann_json/nlohmann_jsonConfig.cmake (found version "3.11.3") 
-- Configuring done (0.3s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/auth-service-build/build
Starting compilation...
[ 12%] Building CXX object CMakeFiles/auth-service.dir/src/main.cpp.o
[ 25%] Building CXX object CMakeFiles/auth-service.dir/src/database_manager.cpp.o
[ 37%] Building CXX object CMakeFiles/auth-service.dir/src/auth_service.cpp.o
[ 50%] Building CXX object CMakeFiles/auth-service.dir/src/security_manager.cpp.o
[ 62%] Building CXX object CMakeFiles/auth-service.dir/src/config_manager.cpp.o
[ 75%] Building CXX object CMakeFiles/auth-service.dir/src/http_server.cpp.o
[ 87%] Building CXX object CMakeFiles/auth-service.dir/src/user_manager.cpp.o
[100%] Linking CXX executable auth-service
[100%] Built target auth-service
