-- Setup Test User for Database Integration Testing
-- Step 6: Database Operations Integration

-- Create a test user with known credentials
-- Username: test_user
-- Email: <EMAIL>  
-- Password: TestPassword123! (will be hashed by application)

-- First, let's check if the user already exists
DO $$
BEGIN
    -- Delete existing test user if present
    DELETE FROM jwt_tokens WHERE user_id IN (SELECT id FROM users WHERE username = 'test_user');
    DELETE FROM jwt_token_blacklist WHERE token_hash IN (
        SELECT token_hash FROM jwt_tokens WHERE user_id IN (SELECT id FROM users WHERE username = 'test_user')
    );
    DELETE FROM users WHERE username = 'test_user';
    
    -- Insert test user with pre-hashed password
    -- Password: TestPassword123!
    -- This is a sample Argon2id hash - in real usage, the application will hash the password
    INSERT INTO users (username, email, password_hash, created_at, updated_at, is_active)
    VALUES (
        'test_user',
        '<EMAIL>',
        '$argon2id$v=19$m=65536,t=3,p=4$sample_salt$sample_hash_placeholder',
        NOW(),
        NOW(),
        true
    );
    
    RAISE NOTICE 'Test user created successfully: test_user';
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Error creating test user: %', SQLERRM;
END $$;

-- Verify the test user was created
SELECT 
    id,
    username,
    email,
    created_at,
    is_active
FROM users 
WHERE username = 'test_user';

-- Show current database schema status
SELECT 
    'users' as table_name,
    COUNT(*) as record_count
FROM users
UNION ALL
SELECT 
    'jwt_tokens' as table_name,
    COUNT(*) as record_count
FROM jwt_tokens
UNION ALL
SELECT 
    'jwt_token_blacklist' as table_name,
    COUNT(*) as record_count
FROM jwt_token_blacklist;
