#include "database-service/security/credential_store.hpp"
#include "database-service/utils/logger.hpp"
#include <format>
#include <openssl/evp.h>
#include <openssl/rand.h>
#include <openssl/sha.h>
#include <vector>
#include <sstream>
#include <iomanip>

namespace dbservice::security {

CredentialStore& CredentialStore::getInstance() {
    static CredentialStore instance;
    return instance;
}

std::shared_ptr<CredentialStore> CredentialStore::getSharedInstance() {
    // Create a shared_ptr that doesn't delete the singleton
    return std::shared_ptr<CredentialStore>(&getInstance(), [](CredentialStore*){});
}

CredentialStore::CredentialStore()
    : initialized_(false) {
}

CredentialStore::~CredentialStore() {
    clear();
}

bool CredentialStore::initialize(const std::string& encryptionKey) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (initialized_) {
        return true;
    }
    
    if (encryptionKey.empty()) {
        utils::Logger::error("Cannot initialize credential store with empty encryption key");
        return false;
    }
    
    // Derive a key from the provided encryption key
    unsigned char key[SHA256_DIGEST_LENGTH];
    SHA256_CTX sha256;
    SHA256_Init(&sha256);
    SHA256_Update(&sha256, encryptionKey.c_str(), encryptionKey.length());
    SHA256_Final(key, &sha256);
    
    // Convert to hex string
    std::stringstream ss;
    for (int i = 0; i < SHA256_DIGEST_LENGTH; i++) {
        ss << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(key[i]);
    }
    
    encryptionKey_ = ss.str();
    initialized_ = true;
    
    utils::Logger::info("Credential store initialized successfully");
    return true;
}

bool CredentialStore::storeCredential(const std::string& key, const std::string& value) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!initialized_) {
        utils::Logger::error("Cannot store credential: credential store not initialized");
        return false;
    }
    
    if (key.empty()) {
        utils::Logger::error("Cannot store credential with empty key");
        return false;
    }
    
    try {
        std::string encryptedValue = encrypt(value);
        credentials_[key] = encryptedValue;
        return true;
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Failed to store credential: {}", e.what()));
        return false;
    }
}

std::string CredentialStore::getCredential(const std::string& key, const std::string& defaultValue) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!initialized_) {
        utils::Logger::error("Cannot get credential: credential store not initialized");
        return defaultValue;
    }
    
    auto it = credentials_.find(key);
    if (it == credentials_.end()) {
        return defaultValue;
    }
    
    try {
        return decrypt(it->second);
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Failed to decrypt credential: {}", e.what()));
        return defaultValue;
    }
}

bool CredentialStore::removeCredential(const std::string& key) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!initialized_) {
        utils::Logger::error("Cannot remove credential: credential store not initialized");
        return false;
    }
    
    auto it = credentials_.find(key);
    if (it == credentials_.end()) {
        return false;
    }
    
    credentials_.erase(it);
    return true;
}

bool CredentialStore::hasCredential(const std::string& key) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!initialized_) {
        utils::Logger::error("Cannot check credential: credential store not initialized");
        return false;
    }
    
    return credentials_.find(key) != credentials_.end();
}

void CredentialStore::clear() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    credentials_.clear();
}

std::string CredentialStore::encrypt(const std::string& input) {
    if (input.empty()) {
        return "";
    }
    
    // Generate a random IV
    unsigned char iv[EVP_MAX_IV_LENGTH];
    if (RAND_bytes(iv, EVP_MAX_IV_LENGTH) != 1) {
        throw std::runtime_error("Failed to generate random IV");
    }
    
    // Convert IV to hex string
    std::stringstream ivss;
    for (int i = 0; i < EVP_MAX_IV_LENGTH; i++) {
        ivss << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(iv[i]);
    }
    std::string ivStr = ivss.str();
    
    // Create and initialize the context
    EVP_CIPHER_CTX* ctx = EVP_CIPHER_CTX_new();
    if (!ctx) {
        throw std::runtime_error("Failed to create cipher context");
    }
    
    // Initialize the encryption operation
    if (EVP_EncryptInit_ex(ctx, EVP_aes_256_cbc(), nullptr, 
                          reinterpret_cast<const unsigned char*>(encryptionKey_.c_str()), iv) != 1) {
        EVP_CIPHER_CTX_free(ctx);
        throw std::runtime_error("Failed to initialize encryption");
    }
    
    // Allocate memory for the ciphertext
    std::vector<unsigned char> ciphertext(input.length() + EVP_CIPHER_block_size(EVP_aes_256_cbc()));
    int len = 0;
    int ciphertext_len = 0;
    
    // Encrypt the plaintext
    if (EVP_EncryptUpdate(ctx, ciphertext.data(), &len, 
                         reinterpret_cast<const unsigned char*>(input.c_str()), input.length()) != 1) {
        EVP_CIPHER_CTX_free(ctx);
        throw std::runtime_error("Failed to encrypt data");
    }
    ciphertext_len = len;
    
    // Finalize the encryption
    if (EVP_EncryptFinal_ex(ctx, ciphertext.data() + len, &len) != 1) {
        EVP_CIPHER_CTX_free(ctx);
        throw std::runtime_error("Failed to finalize encryption");
    }
    ciphertext_len += len;
    
    // Clean up
    EVP_CIPHER_CTX_free(ctx);
    
    // Convert ciphertext to hex string
    std::stringstream ss;
    for (int i = 0; i < ciphertext_len; i++) {
        ss << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(ciphertext[i]);
    }
    
    // Return IV + ciphertext
    return ivStr + ss.str();
}

std::string CredentialStore::decrypt(const std::string& input) {
    if (input.empty()) {
        return "";
    }
    
    // Extract IV from the input
    if (input.length() < EVP_MAX_IV_LENGTH * 2) {
        throw std::runtime_error("Invalid encrypted data");
    }
    
    std::string ivHex = input.substr(0, EVP_MAX_IV_LENGTH * 2);
    std::string ciphertextHex = input.substr(EVP_MAX_IV_LENGTH * 2);
    
    // Convert IV from hex to binary
    unsigned char iv[EVP_MAX_IV_LENGTH];
    for (size_t i = 0; i < EVP_MAX_IV_LENGTH; i++) {
        std::string byteStr = ivHex.substr(i * 2, 2);
        iv[i] = static_cast<unsigned char>(std::stoi(byteStr, nullptr, 16));
    }
    
    // Convert ciphertext from hex to binary
    std::vector<unsigned char> ciphertext(ciphertextHex.length() / 2);
    for (size_t i = 0; i < ciphertext.size(); i++) {
        std::string byteStr = ciphertextHex.substr(i * 2, 2);
        ciphertext[i] = static_cast<unsigned char>(std::stoi(byteStr, nullptr, 16));
    }
    
    // Create and initialize the context
    EVP_CIPHER_CTX* ctx = EVP_CIPHER_CTX_new();
    if (!ctx) {
        throw std::runtime_error("Failed to create cipher context");
    }
    
    // Initialize the decryption operation
    if (EVP_DecryptInit_ex(ctx, EVP_aes_256_cbc(), nullptr, 
                          reinterpret_cast<const unsigned char*>(encryptionKey_.c_str()), iv) != 1) {
        EVP_CIPHER_CTX_free(ctx);
        throw std::runtime_error("Failed to initialize decryption");
    }
    
    // Allocate memory for the plaintext
    std::vector<unsigned char> plaintext(ciphertext.size());
    int len = 0;
    int plaintext_len = 0;
    
    // Decrypt the ciphertext
    if (EVP_DecryptUpdate(ctx, plaintext.data(), &len, 
                         ciphertext.data(), ciphertext.size()) != 1) {
        EVP_CIPHER_CTX_free(ctx);
        throw std::runtime_error("Failed to decrypt data");
    }
    plaintext_len = len;
    
    // Finalize the decryption
    if (EVP_DecryptFinal_ex(ctx, plaintext.data() + len, &len) != 1) {
        EVP_CIPHER_CTX_free(ctx);
        throw std::runtime_error("Failed to finalize decryption");
    }
    plaintext_len += len;
    
    // Clean up
    EVP_CIPHER_CTX_free(ctx);
    
    // Return the plaintext
    return std::string(reinterpret_cast<char*>(plaintext.data()), plaintext_len);
}

} // namespace dbservice::security
