# Setup-AuthServiceSSL.psm1
# PowerShell module for managing SSL certificates in auth-service deployment
#
# LOCATION: Copy to D:\Coding_Projects\auth-service\auth-service-deployment\deployment_scripts\Modules\

function Setup-AuthServiceSSL {
    <#
    .SYNOPSIS
    Sets up SSL certificate infrastructure for auth-service
    
    .DESCRIP<PERSON>ON
    Creates certificate directories, sets permissions, and prepares the server for SSL certificate deployment
    
    .PARAMETER Environment
    Target environment (development or production)
    
    .EXAMPLE
    Setup-AuthServiceSSL -Environment "development"
    #>
    
    param(
        [Parameter(Mandatory=$true)]
        [ValidateSet("development", "production")]
        [string]$Environment
    )
    
    Write-Host "=== Setting up SSL certificates for auth-service ($Environment) ===" -ForegroundColor Cyan
    
    # Load configuration
    if (-not $script:Config) {
        Write-Host "❌ Configuration not loaded. Please run Set-Environment first." -ForegroundColor Red
        return $false
    }
    
    $targetHost = $script:Config.ssh.host
    Write-Host "Target Host: $targetHost" -ForegroundColor Yellow
    Write-Host "Environment: $Environment" -ForegroundColor Yellow
    Write-Host ""
    
    # Create SSL certificate directory structure
    Write-Host "Creating SSL certificate directories..." -ForegroundColor Cyan
    
    $certSetupCommands = @(
        "sudo mkdir -p /etc/ssl/certs/auth-service",
        "sudo mkdir -p /opt/auth-service/logs",
        "sudo mkdir -p /opt/auth-service/scripts",
        "sudo chown auth-service:auth-service /etc/ssl/certs/auth-service",
        "sudo chmod 750 /etc/ssl/certs/auth-service",
        "sudo chown auth-service:auth-service /opt/auth-service/logs",
        "sudo chmod 755 /opt/auth-service/logs"
    )
    
    foreach ($cmd in $certSetupCommands) {
        Write-Host "Executing: $cmd" -ForegroundColor Gray
        $result = Invoke-SSHCommand -Command $cmd
        
        if (-not $result.Success) {
            Write-Host "❌ Failed to execute: $cmd" -ForegroundColor Red
            Write-Host "Error: $($result.Error)" -ForegroundColor Red
            return $false
        }
    }
    
    Write-Host "✅ SSL certificate directories created successfully" -ForegroundColor Green
    
    # Verify directory structure
    Write-Host "Verifying directory structure..." -ForegroundColor Cyan
    
    $verifyCmd = @"
ls -la /etc/ssl/certs/auth-service/ 2>/dev/null && echo "CERT_DIR_OK" || echo "CERT_DIR_MISSING"
ls -la /opt/auth-service/logs/ 2>/dev/null && echo "LOG_DIR_OK" || echo "LOG_DIR_MISSING"
"@
    
    $verifyResult = Invoke-SSHCommand -Command $verifyCmd
    
    if ($verifyResult.Success -and $verifyResult.Output -match "CERT_DIR_OK" -and $verifyResult.Output -match "LOG_DIR_OK") {
        Write-Host "✅ Directory structure verified" -ForegroundColor Green
    } else {
        Write-Host "⚠️ Directory structure verification failed" -ForegroundColor Yellow
        Write-Host "Output: $($verifyResult.Output)" -ForegroundColor Gray
    }
    
    Write-Host ""
    Write-Host "✅ SSL certificate setup completed for $Environment environment" -ForegroundColor Green
    Write-Host "Next step: Run Sync-AuthServiceCertificates to deploy certificates" -ForegroundColor Yellow
    
    return $true
}

function Sync-AuthServiceCertificates {
    <#
    .SYNOPSIS
    Synchronizes SSL certificates to auth-service servers
    
    .DESCRIPTION
    Triggers certificate synchronization from the certificate source server to auth-service environments
    
    .PARAMETER Environment
    Target environment (development, production, or all)
    
    .EXAMPLE
    Sync-AuthServiceCertificates -Environment "development"
    Sync-AuthServiceCertificates -Environment "all"
    #>
    
    param(
        [Parameter(Mandatory=$false)]
        [ValidateSet("development", "production", "all")]
        [string]$Environment = "all"
    )
    
    Write-Host "=== Syncing SSL certificates for auth-service ($Environment) ===" -ForegroundColor Cyan
    
    # Certificate sync is triggered from project-tracker.chcit.org
    $sourceServer = "project-tracker.chcit.org"
    $syncUser = "btaylor-admin"
    $syncScript = "/opt/auth-service/scripts/sync-auth-certificates.sh"
    
    Write-Host "Source Server: $sourceServer" -ForegroundColor Yellow
    Write-Host "Target Environment: $Environment" -ForegroundColor Yellow
    Write-Host ""
    
    # Build the sync command
    $syncCmd = if ($Environment -eq "all") {
        "ssh $syncUser@$sourceServer '$syncScript'"
    } else {
        "ssh $syncUser@$sourceServer '$syncScript $Environment'"
    }
    
    Write-Host "Executing certificate sync..." -ForegroundColor Cyan
    Write-Host "Command: $syncCmd" -ForegroundColor Gray
    
    # Execute the sync command
    $result = Invoke-SSHCommand -Command $syncCmd
    
    if ($result.Success) {
        Write-Host "✅ Certificate sync completed successfully" -ForegroundColor Green
        Write-Host "Sync Output:" -ForegroundColor Cyan
        Write-Host $result.Output -ForegroundColor White
        
        # Test SSL endpoints after sync
        Test-AuthServiceSSL -Environment $Environment
        
    } else {
        Write-Host "❌ Certificate sync failed" -ForegroundColor Red
        Write-Host "Error: $($result.Error)" -ForegroundColor Red
        return $false
    }
    
    return $true
}

function Test-AuthServiceSSL {
    <#
    .SYNOPSIS
    Tests SSL certificate configuration for auth-service
    
    .DESCRIPTION
    Verifies SSL certificates are properly installed and HTTPS endpoints are accessible
    
    .PARAMETER Environment
    Target environment to test (development, production, or all)
    
    .EXAMPLE
    Test-AuthServiceSSL -Environment "development"
    #>
    
    param(
        [Parameter(Mandatory=$false)]
        [ValidateSet("development", "production", "all")]
        [string]$Environment = "all"
    )
    
    Write-Host "=== Testing SSL configuration for auth-service ($Environment) ===" -ForegroundColor Cyan
    
    # Define test endpoints
    $endpoints = @{}
    
    if ($Environment -eq "development" -or $Environment -eq "all") {
        $endpoints["development"] = "https://auth-dev.chcit.org:8082"
    }
    
    if ($Environment -eq "production" -or $Environment -eq "all") {
        $endpoints["production"] = "https://auth.chcit.org:8082"
    }
    
    $allSuccess = $true
    
    foreach ($env in $endpoints.Keys) {
        $url = $endpoints[$env]
        Write-Host "Testing $env environment: $url" -ForegroundColor Yellow
        
        # Test HTTPS connectivity
        $testCmd = "curl -s -o /dev/null -w '%{http_code}' --connect-timeout 10 --max-time 30 '$url/health' || echo 'CURL_FAILED'"
        
        $result = Invoke-SSHCommand -Command $testCmd
        
        if ($result.Success) {
            $httpCode = $result.Output.Trim()
            
            if ($httpCode -match "^[2-5][0-9][0-9]$") {
                Write-Host "✅ HTTPS endpoint accessible (HTTP $httpCode)" -ForegroundColor Green
            } elseif ($httpCode -eq "CURL_FAILED") {
                Write-Host "❌ HTTPS endpoint not accessible (connection failed)" -ForegroundColor Red
                $allSuccess = $false
            } else {
                Write-Host "⚠️ HTTPS endpoint responded with unexpected code: $httpCode" -ForegroundColor Yellow
            }
        } else {
            Write-Host "❌ Failed to test HTTPS endpoint" -ForegroundColor Red
            Write-Host "Error: $($result.Error)" -ForegroundColor Red
            $allSuccess = $false
        }
        
        Write-Host ""
    }
    
    if ($allSuccess) {
        Write-Host "✅ All SSL tests completed successfully" -ForegroundColor Green
    } else {
        Write-Host "⚠️ Some SSL tests failed - check auth-service configuration" -ForegroundColor Yellow
    }
    
    return $allSuccess
}

function Verify-AuthServiceCertificates {
    <#
    .SYNOPSIS
    Verifies SSL certificates are properly installed on auth-service servers
    
    .DESCRIPTION
    Checks certificate files, permissions, and expiry dates on target servers
    
    .PARAMETER Environment
    Target environment to verify (development, production, or all)
    
    .EXAMPLE
    Verify-AuthServiceCertificates -Environment "production"
    #>
    
    param(
        [Parameter(Mandatory=$false)]
        [ValidateSet("development", "production", "all")]
        [string]$Environment = "all"
    )
    
    Write-Host "=== Verifying SSL certificates for auth-service ($Environment) ===" -ForegroundColor Cyan
    
    # Load configuration
    if (-not $script:Config) {
        Write-Host "❌ Configuration not loaded. Please run Set-Environment first." -ForegroundColor Red
        return $false
    }
    
    $certPath = "/etc/ssl/certs/auth-service"
    $requiredFiles = @("cert.pem", "privkey.pem", "chain.pem", "fullchain.pem")
    
    Write-Host "Certificate Path: $certPath" -ForegroundColor Yellow
    Write-Host "Required Files: $($requiredFiles -join ', ')" -ForegroundColor Yellow
    Write-Host ""
    
    # Verify certificate files exist
    Write-Host "Checking certificate files..." -ForegroundColor Cyan
    
    $allSuccess = $true
    
    foreach ($file in $requiredFiles) {
        $checkCmd = "sudo test -f $certPath/$file && echo 'EXISTS' || echo 'MISSING'"
        $result = Invoke-SSHCommand -Command $checkCmd
        
        if ($result.Success -and $result.Output.Trim() -eq "EXISTS") {
            Write-Host "✅ $file - Found" -ForegroundColor Green
        } else {
            Write-Host "❌ $file - Missing" -ForegroundColor Red
            $allSuccess = $false
        }
    }
    
    if (-not $allSuccess) {
        Write-Host ""
        Write-Host "❌ Some certificate files are missing. Run Sync-AuthServiceCertificates to deploy certificates." -ForegroundColor Red
        return $false
    }
    
    # Check certificate permissions
    Write-Host ""
    Write-Host "Checking certificate permissions..." -ForegroundColor Cyan
    
    $permCmd = "sudo ls -la $certPath/"
    $permResult = Invoke-SSHCommand -Command $permCmd
    
    if ($permResult.Success) {
        Write-Host "Certificate file permissions:" -ForegroundColor White
        Write-Host $permResult.Output -ForegroundColor Gray
    }
    
    # Check certificate expiry
    Write-Host ""
    Write-Host "Checking certificate expiry..." -ForegroundColor Cyan
    
    $expiryCmd = "sudo openssl x509 -in $certPath/cert.pem -noout -enddate"
    $expiryResult = Invoke-SSHCommand -Command $expiryCmd
    
    if ($expiryResult.Success) {
        Write-Host "✅ Certificate expiry: $($expiryResult.Output)" -ForegroundColor Green
    } else {
        Write-Host "⚠️ Could not check certificate expiry" -ForegroundColor Yellow
    }
    
    Write-Host ""
    Write-Host "✅ Certificate verification completed" -ForegroundColor Green
    
    return $true
}

# Export module functions
Export-ModuleMember -Function Setup-AuthServiceSSL, Sync-AuthServiceCertificates, Test-AuthServiceSSL, Verify-AuthServiceCertificates
