# Fix GCC 14 Installation for Auth-Service
# This script properly installs GCC 14 on Ubuntu 24.04

Write-Host "🔧 Fixing GCC 14 Installation for Auth-Service" -ForegroundColor Yellow
Write-Host "=" * 60 -ForegroundColor Yellow

# SSH connection details
$sshHost = "dev.chcit.org"
$sshUser = "btaylor-admin"
$sshKey = "C:\Users\<USER>\.ssh\id_rsa"

Write-Host "📡 Connecting to $sshHost..." -ForegroundColor Cyan

# Function to execute SSH commands
function Invoke-SSHCommand {
    param(
        [string]$Command,
        [string]$Description = ""
    )
    
    if ($Description) {
        Write-Host "  ▶️ $Description" -ForegroundColor White
    }
    
    $sshCmd = "ssh -i `"$sshKey`" -o StrictHostKeyChecking=no $sshUser@$sshHost `"$Command`""
    
    try {
        $result = Invoke-Expression $sshCmd 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  ✅ Success" -ForegroundColor Green
            if ($result) {
                Write-Host "     Output: $result" -ForegroundColor Gray
            }
            return $true
        } else {
            Write-Host "  ❌ Failed (Exit Code: $LASTEXITCODE)" -ForegroundColor Red
            if ($result) {
                Write-Host "     Error: $result" -ForegroundColor Red
            }
            return $false
        }
    } catch {
        Write-Host "  ❌ Exception: $_" -ForegroundColor Red
        return $false
    }
}

Write-Host "`n🔍 Step 1: Check Current GCC Installation" -ForegroundColor Yellow

# Check current GCC version
Invoke-SSHCommand "gcc --version | head -1" "Checking current GCC version"
Invoke-SSHCommand "g++ --version | head -1" "Checking current G++ version"

# Check if GCC 14 is already available
$gcc14Available = Invoke-SSHCommand "g++-14 --version" "Checking if GCC 14 is already installed"

if ($gcc14Available) {
    Write-Host "✅ GCC 14 is already installed!" -ForegroundColor Green
    
    # Set up alternatives
    Write-Host "`n🔧 Step 2: Setting up GCC alternatives" -ForegroundColor Yellow
    Invoke-SSHCommand "sudo update-alternatives --install /usr/bin/gcc gcc /usr/bin/gcc-14 100" "Setting GCC 14 as alternative"
    Invoke-SSHCommand "sudo update-alternatives --install /usr/bin/g++ g++ /usr/bin/g++-14 100" "Setting G++ 14 as alternative"
    
    # Verify
    Write-Host "`n✅ Verification:" -ForegroundColor Green
    Invoke-SSHCommand "gcc --version | head -1" "Final GCC version check"
    Invoke-SSHCommand "g++ --version | head -1" "Final G++ version check"
    
} else {
    Write-Host "`n📦 Step 2: Installing GCC 14 from Ubuntu Toolchain PPA" -ForegroundColor Yellow
    
    # Update package lists
    Invoke-SSHCommand "sudo apt update" "Updating package lists"
    
    # Install software-properties-common if not present
    Invoke-SSHCommand "sudo apt install -y software-properties-common" "Installing software-properties-common"
    
    # Add Ubuntu Toolchain PPA
    Invoke-SSHCommand "sudo add-apt-repository ppa:ubuntu-toolchain-r/test -y" "Adding Ubuntu Toolchain PPA"
    
    # Update package lists again
    Invoke-SSHCommand "sudo apt update" "Updating package lists after PPA addition"
    
    # Install GCC 14
    Write-Host "`n🔨 Step 3: Installing GCC 14 packages" -ForegroundColor Yellow
    Invoke-SSHCommand "sudo apt install -y gcc-14 g++-14" "Installing GCC 14 and G++ 14"
    
    # Verify installation
    $installSuccess = Invoke-SSHCommand "g++-14 --version" "Verifying GCC 14 installation"
    
    if ($installSuccess) {
        Write-Host "`n🔧 Step 4: Setting up GCC alternatives" -ForegroundColor Yellow
        
        # Set up alternatives to make gcc-14 the default
        Invoke-SSHCommand "sudo update-alternatives --install /usr/bin/gcc gcc /usr/bin/gcc-14 100" "Setting GCC 14 as default alternative"
        Invoke-SSHCommand "sudo update-alternatives --install /usr/bin/g++ g++ /usr/bin/g++-14 100" "Setting G++ 14 as default alternative"
        
        # Also set up alternatives for the older version if it exists
        $hasGcc13 = Invoke-SSHCommand "which gcc-13" "Checking for GCC 13"
        if ($hasGcc13) {
            Invoke-SSHCommand "sudo update-alternatives --install /usr/bin/gcc gcc /usr/bin/gcc-13 50" "Adding GCC 13 as alternative (lower priority)"
            Invoke-SSHCommand "sudo update-alternatives --install /usr/bin/g++ g++ /usr/bin/g++-13 50" "Adding G++ 13 as alternative (lower priority)"
        }
        
        Write-Host "`n✅ Step 5: Final Verification" -ForegroundColor Green
        Invoke-SSHCommand "gcc --version | head -1" "Final GCC version check"
        Invoke-SSHCommand "g++ --version | head -1" "Final G++ version check"
        Invoke-SSHCommand "g++-14 --version | head -1" "Direct GCC 14 version check"
        
        Write-Host "`n🎉 GCC 14 Installation Complete!" -ForegroundColor Green
        Write-Host "✅ GCC 14 is now installed and set as the default compiler" -ForegroundColor Green
        Write-Host "✅ C++23 features are now available" -ForegroundColor Green
        
    } else {
        Write-Host "`n❌ GCC 14 Installation Failed!" -ForegroundColor Red
        Write-Host "Please check the error messages above and try manual installation:" -ForegroundColor Yellow
        Write-Host "  1. SSH to the server: ssh <EMAIL>" -ForegroundColor White
        Write-Host "  2. Run: sudo add-apt-repository ppa:ubuntu-toolchain-r/test -y" -ForegroundColor White
        Write-Host "  3. Run: sudo apt update" -ForegroundColor White
        Write-Host "  4. Run: sudo apt install -y gcc-14 g++-14" -ForegroundColor White
    }
}

Write-Host "`n📋 Step 6: Installing Additional C++23 Dependencies" -ForegroundColor Yellow

# Install other required packages for C++23 development
$additionalPackages = @(
    "build-essential",
    "cmake", 
    "libboost-all-dev",
    "libssl-dev",
    "libpqxx-dev",
    "nlohmann-json3-dev",
    "pkg-config"
)

foreach ($package in $additionalPackages) {
    Invoke-SSHCommand "sudo apt install -y $package" "Installing $package"
}

Write-Host "`n🧪 Step 7: Testing C++23 Compilation" -ForegroundColor Yellow

# Create a simple C++23 test program
$testProgram = @'
#include <iostream>
#include <string>

int main() {
    std::string message = "C++23 compilation test successful!";
    std::cout << message << std::endl;
    return 0;
}
'@

# Write test program to server
$encodedProgram = [System.Convert]::ToBase64String([System.Text.Encoding]::UTF8.GetBytes($testProgram))
Invoke-SSHCommand "echo '$encodedProgram' | base64 -d > /tmp/cpp23_test.cpp" "Creating C++23 test program"

# Compile with C++23 standard
$compileSuccess = Invoke-SSHCommand "g++ -std=c++23 -o /tmp/cpp23_test /tmp/cpp23_test.cpp" "Compiling with C++23 standard"

if ($compileSuccess) {
    # Run the test program
    Invoke-SSHCommand "/tmp/cpp23_test" "Running C++23 test program"
    
    # Clean up
    Invoke-SSHCommand "rm -f /tmp/cpp23_test /tmp/cpp23_test.cpp" "Cleaning up test files"
    
    Write-Host "`n🎉 SUCCESS: C++23 compilation is working!" -ForegroundColor Green
} else {
    Write-Host "`n❌ C++23 compilation test failed" -ForegroundColor Red
}

Write-Host "`n📊 Installation Summary:" -ForegroundColor Cyan
Write-Host "=" * 40 -ForegroundColor Cyan
Invoke-SSHCommand "gcc --version | head -1" "GCC Version"
Invoke-SSHCommand "g++ --version | head -1" "G++ Version"  
Invoke-SSHCommand "cmake --version | head -1" "CMake Version"
Invoke-SSHCommand "dpkg -l | grep -E '(gcc-14|g\+\+-14)'" "GCC 14 Package Status"

Write-Host "`n✅ GCC 14 installation and setup complete!" -ForegroundColor Green
Write-Host "You can now proceed with auth-service compilation testing." -ForegroundColor White
