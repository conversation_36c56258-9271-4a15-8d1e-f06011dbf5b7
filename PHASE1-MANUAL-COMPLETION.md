# Phase 1 Manual Completion Instructions

## 🎯 **Objective**
Copy SSL files to auth-service project directories

## 📁 **Directory Structure to Create**

Run these PowerShell commands:

```powershell
# Create the auth-service directory structure
New-Item -Path "D:\Coding_Projects\auth-service" -ItemType Directory -Force
New-Item -Path "D:\Coding_Projects\auth-service\cert_sync_helper_app" -ItemType Directory -Force
New-Item -Path "D:\Coding_Projects\auth-service\auth-service-deployment" -ItemType Directory -Force
New-Item -Path "D:\Coding_Projects\auth-service\auth-service-deployment\deployment_scripts" -ItemType Directory -Force
New-Item -Path "D:\Coding_Projects\auth-service\auth-service-deployment\deployment_scripts\Modules" -ItemType Directory -Force
```

## 📄 **Files to Copy**

The SSL files should be recreated in the current directory (`d:\Augment\project-tracker\`). Copy them as follows:

### **1. C++23 Certificate Sync Helper**
- **Source**: `auth_cert_sync_helper.cpp` (in current directory)
- **Destination**: `D:\Coding_Projects\auth-service\cert_sync_helper_app\auth_cert_sync_helper.cpp`

### **2. Certificate Sync Script**
- **Source**: `sync-auth-certificates.sh` (in current directory)
- **Destination**: `D:\Coding_Projects\auth-service\cert_sync_helper_app\sync-auth-certificates.sh`

### **3. PowerShell SSL Module**
- **Source**: `Setup-AuthServiceSSL.psm1` (in current directory)
- **Destination**: `D:\Coding_Projects\auth-service\auth-service-deployment\deployment_scripts\Modules\Setup-AuthServiceSSL.psm1`

## 🔧 **Copy Commands**

```powershell
# Copy C++ files to cert_sync_helper_app
Copy-Item "auth_cert_sync_helper.cpp" "D:\Coding_Projects\auth-service\cert_sync_helper_app\"
Copy-Item "sync-auth-certificates.sh" "D:\Coding_Projects\auth-service\cert_sync_helper_app\"

# Copy PowerShell module to Modules directory
Copy-Item "Setup-AuthServiceSSL.psm1" "D:\Coding_Projects\auth-service\auth-service-deployment\deployment_scripts\Modules\"
```

## ✅ **Verification**

After copying, verify the files exist:

```powershell
# Check cert_sync_helper_app directory
Get-ChildItem "D:\Coding_Projects\auth-service\cert_sync_helper_app"

# Check Modules directory
Get-ChildItem "D:\Coding_Projects\auth-service\auth-service-deployment\deployment_scripts\Modules"
```

## 🚀 **Next Steps**

Once Phase 1 is complete:

1. **Compile C++23 Helper**: `g++ -std=c++23 -o auth_cert_sync_helper auth_cert_sync_helper.cpp`
2. **Deploy to project-tracker.chcit.org**: Copy compiled binary and sync script
3. **Integrate SSL into auth-service**: Add OpenSSL support to C++23 application
4. **Test HTTPS endpoints**: Verify SSL certificate functionality

## 📋 **SSL Endpoints**

Once implemented:
- **Development**: `https://auth-dev.chcit.org:8082`
- **Production**: `https://auth.chcit.org:8082`

## 🔒 **Security Benefits**

- **Reuses existing *.chcit.org wildcard certificate**
- **Builds on proven cert_sync_helper pattern**
- **Automated certificate distribution and renewal**
- **Production-grade SSL security for authentication endpoints**
