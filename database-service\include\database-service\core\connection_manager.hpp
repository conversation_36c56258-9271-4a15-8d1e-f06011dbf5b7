#pragma once

// Standard Library Headers
#include <string>
#include <memory>
#include <deque>
#include <mutex>
#include <condition_variable>
#include <functional>
#include <optional>
#include <chrono>
#include <expected>
#include <span>
#include <format>
#include <source_location>
#include <stop_token>
#include <barrier>
#include <latch>
#include <filesystem>

// Third-party Headers
#include <nlohmann/json.hpp>

// Project Headers
#include "database-service/utils/cache.hpp"
#include "database-service/utils/logger.hpp"

namespace dbservice::core {

// Forward declarations
class Connection;
class Transaction;

/**
 * @namespace dbservice::core
 * @brief Core database service components
 * 
 * This namespace contains the core functionality of the database service,
 * including connection management, transaction handling, and query execution.
 */

/**
 * @defgroup connection_pool Connection Pooling
 * @brief Components for managing database connection pooling
 * 
 * This module provides thread-safe connection pooling to efficiently manage
 * database connections and optimize resource usage.
 */

/**
 * @enum SSLMode
 * @brief SSL/TLS connection verification modes
 * 
 * Defines the level of verification performed on the server's SSL/TLS certificate.
 * The verification level increases from Disable to VerifyFull, with each level
 * providing stronger security guarantees but potentially requiring more configuration.
 * 
 * @ingroup connection_pool
 */
enum class SSLMode {
    Disable,       ///< SSL/TLS is completely disabled (not recommended for production)
    Allow,         ///< Use SSL/TLS if available, but don't verify the certificate
    Prefer,        ///< Try SSL/TLS first, fall back to non-SSL if unavailable
    Require,       ///< Require SSL/TLS, but don't verify the certificate (encryption only)
    VerifyCa,      ///< Verify that the server certificate is issued by a trusted CA
    VerifyFull     ///< Full verification including hostname validation (most secure)
};

/**
 * @brief Converts SSLMode enum to string representation
 * @param mode The SSLMode to convert
 * @return String representation of the SSLMode
 * @throws std::invalid_argument if mode is invalid
 */
std::string sslModeToString(SSLMode mode);

/**
 * @enum TransactionIsolationLevel
 * @brief Transaction isolation levels for database transactions
 *
 * Defines the isolation level for database transactions, controlling
 * the visibility of changes made by other concurrent transactions.
 *
 * @ingroup connection_pool
 */
enum class TransactionIsolationLevel {
    ReadUncommitted,  ///< Lowest isolation level - allows dirty reads
    ReadCommitted,    ///< Default level - prevents dirty reads
    RepeatableRead,   ///< Prevents dirty and non-repeatable reads
    Serializable      ///< Highest isolation level - prevents all phenomena
};

/**
 * @brief Converts TransactionIsolationLevel enum to string representation
 * @param level The TransactionIsolationLevel to convert
 * @return String representation of the isolation level
 * @throws std::invalid_argument if level is invalid
 */
std::string isolationLevelToString(TransactionIsolationLevel level);

/**
 * @struct SSLConfig
 * @brief SSL/TLS configuration options for database connections
 *
 * This structure holds all configuration parameters needed to establish
 * secure SSL/TLS connections to the database server.
 *
 * @ingroup connection_pool
 */
struct SSLConfig {
    bool enabled = false;              ///< Whether to enable SSL/TLS
    SSLMode mode = SSLMode::VerifyFull; ///< Verification mode for SSL/TLS
    std::string certPath;              ///< Path to client certificate file (PEM format)
    std::string keyPath;               ///< Path to client private key file (PEM format)
    std::string caPath;                ///< Path to CA certificate file (PEM format)
    std::string crlPath;               ///< Path to certificate revocation list (CRL) file
    bool rejectExpired = true;          ///< Whether to reject expired certificates

    /**
     * @brief Validates the SSL configuration
     * @return std::expected<void, std::string> Success or error message
     */
    std::expected<void, std::string> validate() const {
        if (!enabled) {
            return {};
        }

        if (mode == SSLMode::VerifyCa || mode == SSLMode::VerifyFull) {
            if (caPath.empty()) {
                return std::unexpected("CA certificate path is required for verification");
            }
            if (!std::filesystem::exists(caPath)) {
                return std::unexpected(std::format("CA certificate file not found: {}", caPath));
            }
        }

        if (!certPath.empty() && !std::filesystem::exists(certPath)) {
            return std::unexpected(std::format("Client certificate file not found: {}", certPath));
        }

        if (!keyPath.empty() && !std::filesystem::exists(keyPath)) {
            return std::unexpected(std::format("Client key file not found: {}", keyPath));
        }

        if (!crlPath.empty() && !std::filesystem::exists(crlPath)) {
            return std::unexpected(std::format("CRL file not found: {}", crlPath));
        }

        return {};
    }
};

/**
 * @class ConnectionManager
 * @brief Thread-safe database connection pool manager
 * 
 * This class implements a thread-safe connection pool for managing database connections.
 * It handles connection creation, pooling, and lifecycle management with support for
 * SSL/TLS, connection validation, and metrics collection.
 * 
 * @note This class is thread-safe and can be used concurrently from multiple threads.
 * @ingroup connection_pool
 * 
 * @example
 * ```cpp
 * // Create a connection manager with SSL
 * SSLConfig sslConfig{
 *     true,                           // enabled
 *     SSLMode::VerifyFull,           // verify full
 *     "/path/to/cert.pem",          // cert path
 *     "/path/to/key.pem",           // key path
 *     "/path/to/ca.pem",            // CA path
 *     "/path/to/crl.pem",           // CRL path
 *     true                           // reject expired
 * };
 * 
 * ConnectionManager manager("dbname=mydb user=postgres", 10, sslConfig);
 * 
 * // Get a connection
 * auto conn = manager.getConnection();
 * if (conn) {
 *     // Use the connection
 *     auto result = conn->executeQuery("SELECT * FROM users");
 *     // ...
 *     // Return the connection to the pool when done
 *     manager.returnConnection(std::move(*conn));
 * }
 * ```
 */
class ConnectionManager {
    // Make ManagedConnection a friend to allow private access
    friend class ManagedConnection;
public:
    /**
     * @brief Construct a new Connection Manager with detailed SSL configuration
     * @param connectionString Database connection string (may be modified by SSL settings)
     * @param maxConnections Maximum number of connections in the pool
     * @param sslConfig SSL/TLS configuration for secure connections
     * @throws std::invalid_argument if maxConnections is zero or SSL configuration is invalid
     * @throws std::runtime_error if connection pool initialization fails
     * 
     * @note The connection string may be modified to include SSL parameters.
     *       Use getConnectionString() to get the final connection string.
     */
    ConnectionManager(std::string connectionString, size_t maxConnections, const SSLConfig& sslConfig);

    /**
     * @brief Construct a new Connection Manager with simplified SSL configuration
     * @param connectionString Database connection string
     * @param maxConnections Maximum number of connections in the pool
     * @param useSSL Whether to use SSL with default settings
     * 
     * @note This constructor uses default SSL certificate paths. For production use,
     *       prefer the constructor that takes an SSLConfig parameter.
     */
    ConnectionManager(const std::string& connectionString, size_t maxConnections, bool useSSL = false);

    // Prevent copying and assignment
    ConnectionManager(const ConnectionManager&) = delete;
    ConnectionManager& operator=(const ConnectionManager&) = delete;
    
    // Allow moving
    ConnectionManager(ConnectionManager&&) = default;
    ConnectionManager& operator=(ConnectionManager&&) = default;

    /**
     * @brief Destroy the Connection Manager
     * 
     * @note This will close all connections in the pool and wait for all
     *       borrowed connections to be returned.
     */
    ~ConnectionManager();

    /**
     * @brief Get a connection from the pool
     * @param timeout Maximum time to wait for a connection (default: 30 seconds)
     * @return std::expected<std::shared_ptr<Connection>, std::string> 
     *         A shared pointer to a Connection on success, or an error message on failure
     * 
     * @note The returned connection must be returned to the pool using returnConnection()
     *       when no longer needed. For automatic management, use ManagedConnection.
     * 
     * @throws std::system_error If the wait is interrupted by a system error
     * @throws std::bad_alloc If memory allocation fails
     * 
     * @example
     * ```cpp
     * auto conn = manager.getConnection(std::chrono::seconds(5));
     * if (!conn) {
     *     std::cerr << "Failed to get connection: " << conn.error() << std::endl;
     *     return;
     * }
     * // Use the connection...
     * manager.returnConnection(std::move(*conn));
     * ```
     */
    std::expected<std::shared_ptr<Connection>, std::string> 
    getConnection(std::chrono::milliseconds timeout = std::chrono::seconds(30));

    /**
     * @brief Return a connection to the pool
     * @param connection Connection to return
     * 
     * @note The connection is reset before being returned to the pool.
     *       If the connection is in a bad state, it will be discarded.
     *       After this call, the shared_ptr is no longer valid.
     * 
     * @warning The connection must have been obtained from this ConnectionManager.
     *          Returning a connection to a different manager results in undefined behavior.
     */
    void returnConnection(std::shared_ptr<Connection> connection);

    /**
     * @brief Execute a query and return the results
     * @param query SQL query string (may contain placeholders like $1, $2, etc.)
     * @param params Query parameters (will be properly escaped)
     * @return std::expected<std::vector<std::vector<std::string>>, std::string> 
     *         A vector of rows, where each row is a vector of strings, or an error message
     * 
     * @note This is a convenience method that gets a connection, executes the query,
     *       and returns the connection to the pool. For better performance with multiple
     *       queries, consider using a transaction.
     * 
     * @throws std::bad_alloc If memory allocation fails
     * 
     * @example
     * ```cpp
     * auto result = manager.executeQuery(
     *     "SELECT id, name FROM users WHERE age > $1",
     *     {"18"}
     * );
     * if (result) {
     *     for (const auto& row : *result) {
     *         // Process row
     *     }
     * }
     * ```
     */
    std::expected<std::vector<std::vector<std::string>>, std::string> 
    executeQuery(const std::string& query, const std::vector<std::string>& params = {});

    /**
     * @brief Execute a query and invoke a callback for each row
     * @param query SQL query string (may contain placeholders)
     * @param callback Function to call for each row (receives a span of column values)
     * @param params Query parameters (will be properly escaped)
     * @return std::expected<void, std::string> Success or error message
     * 
     * @note This is more memory-efficient than executeQuery() for large result sets.
     *       The callback is invoked for each row as it's received from the database.
     * 
     * @example
     * ```cpp
     * auto result = manager.executeQueryWithCallback(
     *     "SELECT id, name FROM users",
     *     [](std::span<const std::string> row) {
     *         std::cout << "ID: " << row[0] << ", Name: " << row[1] << '\n';
     *     }
     * );
     * ```
     */
    std::expected<void, std::string> 
    executeQueryWithCallback(
        const std::string& query, 
        std::function<void(std::span<const std::string>)> callback, 
        const std::vector<std::string>& params = {});

    /**
     * @brief Execute a statement that doesn't return results (INSERT, UPDATE, DELETE, etc.)
     * @param statement SQL statement to execute (may contain placeholders)
     * @param params Statement parameters (will be properly escaped)
     * @return std::expected<int, std::string> 
     *         Number of rows affected on success, or an error message
     * 
     * @note For statements that return results (SELECT), use executeQuery() instead.
     * 
     * @example
     * ```cpp
     * // Insert a new user
     * auto result = manager.executeNonQuery(
     *     "INSERT INTO users (name, email) VALUES ($1, $2)",
     *     {"John Doe", "<EMAIL>"}
     * );
     * if (result) {
     *     std::cout << "Inserted " << *result << " rows\n";
     * }
     * ```
     */
    std::expected<int, std::string> 
    executeNonQuery(const std::string& statement, const std::vector<std::string>& params = {});

    /**
     * @brief List all databases on the server (admin operation)
     * @return std::expected<std::vector<std::string>, std::string> 
     *         List of database names on success, or an error message
     * 
     * @note Requires superuser or pg_read_all_stats role.
     * @warning This operation connects to the 'postgres' database internally.
     * 
     * @throws std::runtime_error If the query fails
     * @throws std::bad_alloc If memory allocation fails
     */
    std::expected<std::vector<std::string>, std::string> listDatabases();

    /**
     * @brief Create a new database (admin operation)
     * @param dbName The name of the database to create
     * @param templateName Optional template database (defaults to 'template1')
     * @param owner Optional owner for the new database
     * @param encoding Optional character encoding (e.g., 'UTF8')
     * @param lcCollate Optional collation order (e.g., 'en_US.UTF-8')
     * @param lcCtype Optional character classification (e.g., 'en_US.UTF-8')
     * @param tablespace Optional tablespace name
     * @param connectionLimit Optional connection limit (-1 for no limit)
     * @param isTemplate Whether the database can be used as a template
     * @return std::expected<void, std::string> Success or error message
     * 
     * @note Requires CREATEDB privilege or superuser status.
     * @warning This operation cannot be executed inside a transaction block.
     * 
     * @throws std::invalid_argument If dbName is empty or invalid
     * @throws std::runtime_error If database creation fails
     */
    std::expected<void, std::string> createDatabase(
        const std::string& dbName,
        const std::string& templateName = "template1",
        const std::optional<std::string>& owner = std::nullopt,
        const std::optional<std::string>& encoding = std::nullopt,
        const std::optional<std::string>& lcCollate = std::nullopt,
        const std::optional<std::string>& lcCtype = std::nullopt,
        const std::optional<std::string>& tablespace = std::nullopt,
        int connectionLimit = -1,
        bool isTemplate = false);

    /**
     * @brief Drop an existing database (admin operation)
     * @param dbName The name of the database to drop
     * @param ifExists If true, don't report an error if the database doesn't exist
     * @param force If true, terminate all connections to the database first
     * @return std::expected<void, std::string> Success or error message
     * 
     * @note The database must not be in use when this function is called,
     *       unless force is set to true.
     * @warning This operation cannot be executed inside a transaction block.
     * 
     * @throws std::invalid_argument If dbName is empty or invalid
     * @throws std::runtime_error If database deletion fails
     */
    std::expected<void, std::string> dropDatabase(
        const std::string& dbName,
        bool ifExists = false,
        bool force = false);


    /**
     * @brief Begin a new transaction
     * @param isolationLevel Transaction isolation level (default: ReadCommitted)
     * @param readOnly Whether the transaction is read-only (default: false)
     * @param deferrable Whether the transaction is deferrable (default: false)
     * @return std::expected<std::shared_ptr<Transaction>, std::string> 
     *         A shared pointer to a Transaction on success, or an error message
     * 
     * @note The transaction must be committed or rolled back before the connection
     *       is returned to the pool. For automatic management, use executeInTransaction.
     * 
     * @example
     * ```cpp
     * auto txn = manager.beginTransaction(TransactionIsolationLevel::Serializable);
     * if (txn) {
     *     try {
     *         // Execute statements...
     *         txn->commit();
     *     } catch (const std::exception& e) {
     *         txn->rollback();
     *     }
     * }
     * ```
     */
    std::expected<std::shared_ptr<Transaction>, std::string> 
    beginTransaction(
        TransactionIsolationLevel isolationLevel = TransactionIsolationLevel::ReadCommitted,
        bool readOnly = false,
        bool deferrable = false);

    /**
     * @brief Execute a function within a transaction
     * @tparam F Function type (deduced)
     * @param func Function to execute (receives a Transaction&)
     * @param isolationLevel Transaction isolation level (default: ReadCommitted)
     * @param readOnly Whether the transaction is read-only (default: false)
     * @param deferrable Whether the transaction is deferrable (default: false)
     * @return std::expected<FuncRet, std::string> 
     *         The result of the function or an error message
     * 
     * @note The transaction is automatically committed if the function returns
     *       successfully, or rolled back if an exception is thrown.
     */
    template<typename F>
    auto executeInTransaction(
        F&& func,
        TransactionIsolationLevel isolationLevel = TransactionIsolationLevel::ReadCommitted,
        bool readOnly = false,
        bool deferrable = false) -> std::expected<std::invoke_result_t<F, Transaction&>, std::string>;

    /**
     * @brief Shut down the connection manager
     * 
     * This will:
     * 1. Prevent new connection requests
     * 2. Wait for all borrowed connections to be returned
     * 3. Close all idle connections
     * 4. Clean up resources
     * 
     * @note Any threads waiting for a connection will receive an error.
     * @warning Do not call any other methods after shutdown().
     */
    void shutdown();

    /**
     * @brief Get the current SSL/TLS configuration
     * @return const SSLConfig& Reference to the SSL configuration
     * 
     * @note The returned reference is valid for the lifetime of the ConnectionManager.
     */
    const SSLConfig& getSSLConfig() const; 
    
    /**
     * @brief Get the connection string with SSL parameters
     * @return std::string The connection string including SSL parameters
     * 
     * @note This returns the actual connection string being used, which may
     *       include additional parameters added by the ConnectionManager.
     */
    std::string getConnectionString() const;

    /**
     * @brief Get the current number of active connections
     * @return size_t Number of connections currently in use
     * 
     * @note This is a point-in-time snapshot; the actual count may change
     *       immediately after this function returns.
     */
    size_t getActiveConnectionCount() const;

    /**
     * @brief Get the current number of idle connections
     * @return size_t Number of idle connections in the pool
     * 
     * @note This is a point-in-time snapshot; the actual count may change
     *       immediately after this function returns.
     */
    size_t getIdleConnectionCount() const;

    /**
     * @brief Get the current number of threads waiting for a connection
     * @return size_t Number of waiting connection requests
     * 
     * @note This can be used to detect connection pool contention.
     *       A high number may indicate the need to increase maxConnections.
     */
    size_t getWaitingConnectionCount() const;

    /**
     * @brief Get the maximum number of connections in the pool
     * @return size_t Maximum number of connections
     * 
     * @note This is the value passed to the constructor, which may be less
     *       than the actual database server's max_connections setting.
     */
    size_t getMaxConnections() const;
    
    /**
     * @brief Check if the connection manager has been shut down
     * @return bool True if shutdown() has been called, false otherwise
     */
    bool isShutdown() const;

    /**
     * @brief Update internal metrics
     * 
     * This method updates various metrics about the connection pool,
     * including connection counts, wait times, and error rates.
     * 
     * @note This is called automatically at regular intervals.
     *       You only need to call this if you need up-to-date metrics.
     */
    void updateMetrics();

    /**
     * @brief Get detailed metrics about the connection pool
     * @return std::expected<nlohmann::json, std::string> 
     *         JSON object containing metrics on success, or an error message
     * 
     * The returned JSON includes:
     * - active_connections: Number of active connections
     * - idle_connections: Number of idle connections
     * - waiting_requests: Number of threads waiting for a connection
     * - total_connections: Total connections (active + idle)
     * - max_connections: Maximum pool size
     * - wait_time_avg: Average wait time for a connection (ms)
     * - wait_time_max: Maximum wait time for a connection (ms)
     * - query_count: Total number of queries executed
     * - error_count: Number of failed queries
     * - avg_query_time: Average query execution time (ms)
     * - last_error: Last error message (if any)
     * - timestamp: When the metrics were last updated (ISO 8601)
     * 
     * @note This is a snapshot in time; values may change immediately after return.
     */
    std::expected<nlohmann::json, std::string> getMetrics() const;

    /**
     * @brief Execute a query with result caching
     * @param query SQL query to execute
     * @param params Query parameters (will be part of the cache key)
     * @param ttl Time-to-live for the cached result (default: 5 minutes)
     * @return std::expected<nlohmann::json, std::string> 
     *         Cached or fresh query result as JSON, or an error message
     * 
     * @note The cache key is a combination of the query string and parameters.
     *       Different parameter values will result in different cache entries.
     *       The cache is thread-safe and uses a least-recently-used (LRU) eviction policy.
     * 
     * @warning Only use caching for read-only queries that return small result sets.
     *          The entire result set is stored in memory.
     */
    std::expected<nlohmann::json, std::string> executeCachedQuery(
        const std::string& query,
        const std::vector<std::string>& params = {},
        std::chrono::milliseconds ttl = std::chrono::minutes(5));

    /**
     * @brief Clear the query cache
     * 
     * This immediately removes all entries from the query cache.
     * New queries will repopulate the cache as needed.
     */
    void clearCache();

    /**
     * @brief Get statistics about the query cache
     * @return nlohmann::json JSON object with cache statistics
     *
     * The returned JSON includes:
     * - entry_count: Number of entries in the cache
     * - max_entries: Maximum number of entries before eviction
     * - hit_count: Total number of cache hits
     * - miss_count: Total number of cache misses
     * - hit_rate: Cache hit rate (hits / (hits + misses))
     * - size_bytes: Approximate memory usage of the cache
     * - eviction_count: Number of entries evicted due to size limits
     * - oldest_entry: Timestamp of the oldest entry (ISO 8601)
     * - ttl_default: Default time-to-live for cache entries (ms)
     */
    nlohmann::json getCacheStats() const;

    /**
     * @brief Enable or disable connection pool auto-scaling
     * @param enabled Whether to enable auto-scaling
     * @param minConnections Minimum number of connections to maintain
     * @param maxConnections Maximum number of connections allowed
     * @param scaleUpThreshold Percentage of active connections that triggers scale-up (0.0-1.0)
     * @param scaleDownThreshold Percentage of idle connections that triggers scale-down (0.0-1.0)
     * @param checkIntervalMs Interval between auto-scaling checks in milliseconds
     */
    void configureAutoScaling(
        bool enabled,
        size_t minConnections = 2,
        size_t maxConnections = 50,
        double scaleUpThreshold = 0.8,
        double scaleDownThreshold = 0.3,
        std::chrono::milliseconds checkIntervalMs = std::chrono::seconds(30));

    /**
     * @brief Get auto-scaling configuration and statistics
     * @return nlohmann::json JSON object with auto-scaling information
     */
    nlohmann::json getAutoScalingStats() const;

private:
    /**
     * @brief Create a new connection
     * @return New connection or error message
     */
    std::expected<std::shared_ptr<Connection>, std::string> createConnection();

    /**
     * @brief Build connection string with SSL parameters
     * @return Connection string with SSL parameters
     */
    std::string buildConnectionString() const;

    /**
     * @brief Convert SSLMode to string
     * @param mode SSL mode
     * @return String representation of SSL mode
     */
    static std::string sslModeToString(SSLMode mode);

    std::string connectionString_;
    size_t maxConnections_;
    SSLConfig sslConfig_;
        std::deque<std::shared_ptr<Connection>> connections_;
    mutable std::mutex mutex_;
    std::condition_variable cv_;
    std::chrono::milliseconds connectionTimeout_{5000}; // Default timeout for acquiring a connection
    bool shutdown_;

    // Metrics tracking
    size_t activeConnections_;
    size_t waitingConnections_;
    std::chrono::steady_clock::time_point lastMetricsUpdate_;

    // Query cache
    std::unique_ptr<utils::QueryCache> queryCache_;

    // Auto-scaling configuration
    bool autoScalingEnabled_;
    size_t minConnections_;
    size_t autoScalingMaxConnections_;
    double scaleUpThreshold_;
    double scaleDownThreshold_;
    std::chrono::milliseconds autoScalingCheckInterval_;
    std::chrono::steady_clock::time_point lastAutoScalingCheck_;

    // Auto-scaling statistics
    size_t scaleUpEvents_;
    size_t scaleDownEvents_;
    std::chrono::steady_clock::time_point lastScaleEvent_;

    /**
     * @brief Perform auto-scaling check and adjustment
     */
    void performAutoScaling();

    /**
     * @brief Scale up the connection pool
     * @param targetSize Target number of connections
     */
    void scaleUp(size_t targetSize);

    /**
     * @brief Scale down the connection pool
     * @param targetSize Target number of connections
     */
    void scaleDown(size_t targetSize);
};

class ManagedConnection {
public:
    explicit ManagedConnection(ConnectionManager& manager);
    ~ManagedConnection();

    ManagedConnection(ManagedConnection&& other) noexcept;
    ManagedConnection& operator=(ManagedConnection&& other) noexcept;

    ManagedConnection(const ManagedConnection&) = delete;
    ManagedConnection& operator=(const ManagedConnection&) = delete;

    Connection* operator->() const;
    std::shared_ptr<Connection> get() const;
    explicit operator bool() const; // Checks if connection is valid

    // Allows checking the error if connection acquisition failed
    const std::string* getError() const; 

private:
    ConnectionManager* manager_; // Non-owning pointer to the manager
    std::expected<std::shared_ptr<Connection>, std::string> connection_expected_;
    bool moved_from_; // To prevent returning connection in destructor if moved
};

} // namespace dbservice::core
