﻿#pragma once
#include <memory>

class ConfigManager;
class DatabaseManager;
class SecurityManager;
class JWTManager;
class HttpServer;
class UserManager;

class AuthService {
public:
    explicit AuthService(std::unique_ptr<ConfigManager> config);
    ~AuthService();
    void start(int port);
    void stop();
private:
    std::unique_ptr<ConfigManager> config_manager_;
    std::unique_ptr<DatabaseManager> database_manager_;
    std::unique_ptr<SecurityManager> security_manager_;
    std::unique_ptr<JWTManager> jwt_manager_;
    std::unique_ptr<HttpServer> http_server_;
    std::unique_ptr<UserManager> user_manager_;
};
