#pragma once

#include <string>
#include <vector>
#include <memory>
#include <expected>
#include <chrono>
#include <nlohmann/json.hpp>

namespace dbservice::core {
    class ConnectionManager;
}

namespace dbservice::security {

// Error types for audit logging
enum class AuditError {
    DATABASE_ERROR,
    INVALID_PARAMETERS,
    PER<PERSON><PERSON>ION_DENIED,
    INITIALIZATION_FAILED
};

// Audit event types
enum class AuditEventType {
    USER_LOGIN,
    USER_LOGOUT,
    USER_CREATED,
    USER_UPDATED,
    USER_DELETED,
    APPLICATION_REGISTERED,
    APPLICATION_UPDATED,
    APPLICATION_DEACTIVATED,
    DATABASE_QUERY,
    DATABASE_EXECUTE,
    DATABASE_TRANSACTION,
    API_KEY_GENERATED,
    API_KEY_VALIDATED,
    PERMISSION_GRANTED,
    PERMISSION_REVOKED,
    SECURITY_VIOLATION,
    CONFIGURATION_CHANGED,
    SYSTEM_ERROR
};

// Audit log entry
struct AuditEntry {
    int id;
    std::chrono::system_clock::time_point timestamp;
    AuditEventType eventType;
    int userId;
    int applicationId;
    std::string action;
    std::string resource;
    std::string ipAddress;
    std::string userAgent;
    bool success;
    std::string errorMessage;
    nlohmann::json details;
    std::chrono::system_clock::time_point createdAt;
};

// Audit query parameters
struct AuditQuery {
    std::optional<std::chrono::system_clock::time_point> fromTime;
    std::optional<std::chrono::system_clock::time_point> toTime;
    std::optional<AuditEventType> eventType;
    std::optional<int> userId;
    std::optional<int> applicationId;
    std::optional<std::string> action;
    std::optional<std::string> resource;
    std::optional<bool> success;
    int limit = 100;
    int offset = 0;
};

/**
 * @brief Manages audit logging for security compliance and operation tracking
 * 
 * The AuditLogger is responsible for:
 * - Logging all database operations
 * - Tracking user actions and authentication events
 * - Recording security events and violations
 * - Providing audit trail queries for compliance
 * - Maintaining audit log integrity
 */
class AuditLogger {
public:
    /**
     * @brief Constructor
     * @param connectionManager Database connection manager
     */
    explicit AuditLogger(std::shared_ptr<dbservice::core::ConnectionManager> connectionManager);

    /**
     * @brief Destructor
     */
    ~AuditLogger() = default;

    // Disable copy constructor and assignment operator
    AuditLogger(const AuditLogger&) = delete;
    AuditLogger& operator=(const AuditLogger&) = delete;

    /**
     * @brief Initialize the audit logger
     * @return true if initialization successful, false otherwise
     */
    bool initialize();

    /**
     * @brief Log a user action
     * @param userId User ID (0 for system actions)
     * @param action Action performed
     * @param resource Resource affected
     * @param success Whether the action was successful
     * @param details Additional details as JSON
     * @param ipAddress Client IP address
     * @param userAgent Client user agent
     * @return void on success, error on failure
     */
    std::expected<void, AuditError> logUserAction(
        int userId,
        const std::string& action,
        const std::string& resource,
        bool success = true,
        const nlohmann::json& details = nlohmann::json::object(),
        const std::string& ipAddress = "",
        const std::string& userAgent = ""
    );

    /**
     * @brief Log an application action
     * @param applicationId Application ID
     * @param action Action performed
     * @param resource Resource affected
     * @param success Whether the action was successful
     * @param details Additional details as JSON
     * @param ipAddress Client IP address
     * @return void on success, error on failure
     */
    std::expected<void, AuditError> logApplicationAction(
        int applicationId,
        const std::string& action,
        const std::string& resource,
        bool success = true,
        const nlohmann::json& details = nlohmann::json::object(),
        const std::string& ipAddress = ""
    );

    /**
     * @brief Log a security event
     * @param eventType Type of security event
     * @param action Action that triggered the event
     * @param resource Resource involved
     * @param success Whether the action was successful
     * @param details Additional details as JSON
     * @param userId User ID (optional)
     * @param applicationId Application ID (optional)
     * @param ipAddress Client IP address
     * @param userAgent Client user agent
     * @return void on success, error on failure
     */
    std::expected<void, AuditError> logSecurityEvent(
        AuditEventType eventType,
        const std::string& action,
        const std::string& resource,
        bool success = true,
        const nlohmann::json& details = nlohmann::json::object(),
        int userId = 0,
        int applicationId = 0,
        const std::string& ipAddress = "",
        const std::string& userAgent = ""
    );

    /**
     * @brief Log a database operation
     * @param applicationId Application ID performing the operation
     * @param operation Type of operation (SELECT, INSERT, UPDATE, DELETE, etc.)
     * @param query SQL query or description
     * @param success Whether the operation was successful
     * @param details Additional details as JSON
     * @param ipAddress Client IP address
     * @return void on success, error on failure
     */
    std::expected<void, AuditError> logDatabaseOperation(
        int applicationId,
        const std::string& operation,
        const std::string& query,
        bool success = true,
        const nlohmann::json& details = nlohmann::json::object(),
        const std::string& ipAddress = ""
    );

    /**
     * @brief Get audit trail based on query parameters
     * @param query Audit query parameters
     * @return Vector of audit entries on success, error on failure
     */
    std::expected<std::vector<AuditEntry>, AuditError> getAuditTrail(const AuditQuery& query);

    /**
     * @brief Get audit statistics
     * @param fromTime Start time for statistics
     * @param toTime End time for statistics
     * @return Statistics as JSON on success, error on failure
     */
    std::expected<nlohmann::json, AuditError> getAuditStatistics(
        const std::chrono::system_clock::time_point& fromTime,
        const std::chrono::system_clock::time_point& toTime
    );

    /**
     * @brief Clean up old audit logs
     * @param retentionDays Number of days to retain logs
     * @return Number of deleted entries on success, error on failure
     */
    std::expected<int, AuditError> cleanupOldLogs(int retentionDays);

    /**
     * @brief Verify audit log integrity
     * @return true if integrity check passes, false otherwise
     */
    std::expected<bool, AuditError> verifyIntegrity();

private:
    std::shared_ptr<dbservice::core::ConnectionManager> connectionManager_;
    bool initialized_;

    /**
     * @brief Log an audit entry
     * @param entry Audit entry to log
     * @return void on success, error on failure
     */
    std::expected<void, AuditError> logEntry(const AuditEntry& entry);

    /**
     * @brief Convert audit event type to string
     * @param eventType Event type
     * @return String representation
     */
    std::string eventTypeToString(AuditEventType eventType);

    /**
     * @brief Convert string to audit event type
     * @param eventTypeStr String representation
     * @return Event type
     */
    AuditEventType stringToEventType(const std::string& eventTypeStr);

    /**
     * @brief Convert database row to AuditEntry object
     * @param row Database row data
     * @return AuditEntry object
     */
    AuditEntry rowToAuditEntry(const nlohmann::json& row);

    /**
     * @brief Build WHERE clause for audit query
     * @param query Audit query parameters
     * @param params Output parameter list
     * @return WHERE clause string
     */
    std::string buildWhereClause(const AuditQuery& query, std::vector<std::string>& params);
};

} // namespace dbservice::security
