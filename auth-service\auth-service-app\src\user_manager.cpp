﻿#include "user_manager.hpp"
#include "database_manager.hpp"
#include "security_manager.hpp"
#include <iostream>
#include <regex>

UserManager::UserManager(DatabaseManager* db_manager, SecurityManager* sec_manager)
    : db_manager_(db_manager), sec_manager_(sec_manager) {
    std::cout << "UserManager initialized with database integration" << std::endl;
}

UserManager::~UserManager() = default;

int UserManager::create_user(const std::string& username, const std::string& email, const std::string& password) {
    std::cout << "Creating user with database integration: " << username << std::endl;

    // Validate input
    if (!is_valid_username(username)) {
        std::cout << "Invalid username format: " << username << std::endl;
        return -1;
    }

    if (!is_valid_email(email)) {
        std::cout << "Invalid email format: " << email << std::endl;
        return -1;
    }

    if (password.length() < 8) {
        std::cout << "Password too short (minimum 8 characters)" << std::endl;
        return -1;
    }

    // Hash the password
    std::string hashed_password = sec_manager_->hash_password(password);

    // Create user in database
    int user_id = db_manager_->create_user(username, email, hashed_password);

    if (user_id > 0) {
        std::cout << "User created successfully: " << username << " (ID: " << user_id << ")" << std::endl;
    } else {
        std::cout << "Failed to create user: " << username << std::endl;
    }

    return user_id;
}

bool UserManager::create_user(const std::string& username, const std::string& password) {
    // Legacy method - generate email from username
    std::string email = username + "@example.com";
    int user_id = create_user(username, email, password);
    return user_id > 0;
}

UserManager::AuthResult UserManager::authenticate_user(const std::string& username, const std::string& password) {
    std::cout << "Authenticating user with database lookup: " << username << std::endl;

    AuthResult result;
    result.success = false;
    result.user_id = -1;

    // Get user from database
    auto user_opt = db_manager_->get_user_by_username(username);

    if (!user_opt.has_value()) {
        result.error_message = "User not found";
        std::cout << "Authentication failed - user not found: " << username << std::endl;
        return result;
    }

    auto user = user_opt.value();

    // Verify password
    bool password_valid = sec_manager_->verify_password(password, user.password_hash);

    if (password_valid) {
        result.success = true;
        result.user_id = user.id;
        result.username = user.username;
        result.email = user.email;

        // Update last login time
        db_manager_->update_user_last_login(user.id);

        std::cout << "User authenticated successfully: " << username << " (ID: " << user.id << ")" << std::endl;
    } else {
        result.error_message = "Invalid password";
        std::cout << "Authentication failed - invalid password: " << username << std::endl;
    }

    return result;
}

bool UserManager::authenticate_user_simple(const std::string& username, const std::string& password) {
    auto result = authenticate_user(username, password);
    return result.success;
}

int UserManager::get_user_id(const std::string& username) {
    auto user_opt = db_manager_->get_user_by_username(username);
    if (user_opt.has_value()) {
        return user_opt.value().id;
    }
    return -1;
}

bool UserManager::update_last_login(int user_id) {
    return db_manager_->update_user_last_login(user_id);
}

bool UserManager::delete_user(const std::string& username) {
    std::cout << "Deleting user: " << username << std::endl;

    // Get user ID first
    int user_id = get_user_id(username);
    if (user_id <= 0) {
        std::cout << "User not found: " << username << std::endl;
        return false;
    }

    return delete_user_by_id(user_id);
}

bool UserManager::delete_user_by_id(int user_id) {
    bool success = db_manager_->delete_user(user_id);

    if (success) {
        std::cout << "User deleted successfully (ID: " << user_id << ")" << std::endl;
    } else {
        std::cout << "Failed to delete user (ID: " << user_id << ")" << std::endl;
    }

    return success;
}

bool UserManager::is_valid_email(const std::string& email) {
    // Simple email validation regex
    const std::regex email_pattern(R"([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})");
    return std::regex_match(email, email_pattern);
}

bool UserManager::is_valid_username(const std::string& username) {
    // Username: 3-50 characters, alphanumeric and underscore only
    if (username.length() < 3 || username.length() > 50) {
        return false;
    }

    const std::regex username_pattern(R"([a-zA-Z0-9_]+)");
    return std::regex_match(username, username_pattern);
}
