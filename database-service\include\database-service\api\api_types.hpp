#pragma once
#include <string>
#include <unordered_map>
#include <nlohmann/json.hpp>

#include <functional> // For std::function
#include <string>     // For std::string
#include <expected>   // For std::expected

namespace dbservice::api {

// Forward declarations
struct ParsedRequest;
struct Response;

/**
 * @enum ApiErrorType
 * @brief Enum for different API error types
 */
enum class ApiErrorType {
    Unknown,
    WinsockInit,
    SocketCreation,
    SocketOption,
    SocketBind,
    SocketListen,
    RequestParsing,
    ResponseCreation
};

/**
 * @struct ApiError
 * @brief Represents an API error with a type, message, and code
 */
struct ApiError {
    ApiErrorType type;
    std::string message;
    int code = 0; // Optional error code (e.g., errno)
};

/**
 * @struct Response
 * @brief Represents an API response
 */
struct Response {
    std::unordered_map<std::string, std::string> headers;
    std::string body;
    int statusCode = 200; // HTTP status code
};

/**
 * @struct ParsedRequest
 * @brief Represents a parsed HTTP request
 */
struct ParsedRequest {
    std::string method;
    std::string path;
    std::unordered_map<std::string, std::string> headers;
    std::string body;
    std::unordered_map<std::string, std::string> path_params;
};

// Define the handler for API routes (after Response is defined)
using RouteHandler = std::function<std::expected<Response, std::string>(const ParsedRequest&)>;

} // namespace dbservice::api


