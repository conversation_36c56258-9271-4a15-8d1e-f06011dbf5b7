Running CMake configuration...
-- The CXX compiler identification is GNU 14.2.0
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /usr/bin/g++-14 - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found PostgreSQL: /usr/lib/x86_64-linux-gnu/libpq.so (found version "17.5")
-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.13")
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE
-- pqxx package not found, will attempt to use system libraries
-- Tests disabled. Use -DBUILD_TESTS=ON to enable.
-- 
-- === Database Service Configuration Summary ===
-- Version: 1.0.0
-- Build type: Release
-- C++ standard: 23
-- Compiler: GNU 14.2.0
-- Build tests: OFF
-- Code coverage: OFF
-- Install prefix: /usr/local
-- 
-- Dependencies:
--   Boost: 1.83.0
--   PostgreSQL: 
--   OpenSSL: 3.0.13
--   nlohmann/json: Found
--   pqxx: System library
-- ===============================================
-- 
-- Configuring done (0.4s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/database-service-build/build
Starting compilation...
[  9%] Building CXX object CMakeFiles/database-service.dir/src/api/api_server.cpp.o
[  9%] Building CXX object CMakeFiles/database-service.dir/src/api/route_controller.cpp.o
/home/<USER>/database-service-build/src/api/route_controller.cpp: In member function ‘std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> > dbservice::api::RouteController::handleListUsers(const dbservice::api::ParsedRequest&)’:
/home/<USER>/database-service-build/src/api/route_controller.cpp:1072:13: error: no match for ‘operator!’ (operand type is ‘std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >’)
 1072 |         if (!queryResult) {
      |             ^~~~~~~~~~~~
/home/<USER>/database-service-build/src/api/route_controller.cpp:1072:13: note: candidate: ‘operator!(bool)’ (built-in)
/home/<USER>/database-service-build/src/api/route_controller.cpp:1072:13: note:   no known conversion for argument 1 from ‘std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >’ to ‘bool’
/home/<USER>/database-service-build/src/api/route_controller.cpp:1077:35: error: ‘class std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >’ has no member named ‘value’
 1077 |         auto result = queryResult.value();
      |                                   ^~~~~
/home/<USER>/database-service-build/src/api/route_controller.cpp: In member function ‘std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> > dbservice::api::RouteController::handleGetUserProfile(const dbservice::api::ParsedRequest&)’:
/home/<USER>/database-service-build/src/api/route_controller.cpp:1121:48: error: conversion from ‘int’ to non-scalar type ‘std::string’ {aka ‘std::__cxx11::basic_string<char>’} requested
 1121 |         std::string username = authResult.value(); // This should be the username from the token
      |                                ~~~~~~~~~~~~~~~~^~
/home/<USER>/database-service-build/src/api/route_controller.cpp:1133:13: error: no match for ‘operator!’ (operand type is ‘std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >’)
 1133 |         if (!queryResult) {
      |             ^~~~~~~~~~~~
/home/<USER>/database-service-build/src/api/route_controller.cpp:1133:13: note: candidate: ‘operator!(bool)’ (built-in)
/home/<USER>/database-service-build/src/api/route_controller.cpp:1133:13: note:   no known conversion for argument 1 from ‘std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >’ to ‘bool’
/home/<USER>/database-service-build/src/api/route_controller.cpp:1137:35: error: ‘class std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >’ has no member named ‘value’
 1137 |         auto result = queryResult.value();
      |                                   ^~~~~
/home/<USER>/database-service-build/src/api/route_controller.cpp: In member function ‘std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> > dbservice::api::RouteController::handleChangePassword(const dbservice::api::ParsedRequest&)’:
/home/<USER>/database-service-build/src/api/route_controller.cpp:1190:48: error: conversion from ‘int’ to non-scalar type ‘std::string’ {aka ‘std::__cxx11::basic_string<char>’} requested
 1190 |         std::string username = authResult.value();
      |                                ~~~~~~~~~~~~~~~~^~
/home/<USER>/database-service-build/src/api/route_controller.cpp:1205:17: error: no match for ‘operator!’ (operand type is ‘std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >’)
 1205 |             if (!adminResult || adminResult.value().rows.empty()) {
      |                 ^~~~~~~~~~~~
/home/<USER>/database-service-build/src/api/route_controller.cpp:1205:17: note: candidate: ‘operator!(bool)’ (built-in)
/home/<USER>/database-service-build/src/api/route_controller.cpp:1205:17: note:   no known conversion for argument 1 from ‘std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >’ to ‘bool’
/home/<USER>/database-service-build/src/api/route_controller.cpp:1205:45: error: ‘class std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >’ has no member named ‘value’
 1205 |             if (!adminResult || adminResult.value().rows.empty()) {
      |                                             ^~~~~
/home/<USER>/database-service-build/src/api/route_controller.cpp:1209:40: error: ‘class std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >’ has no member named ‘value’
 1209 |             bool isAdmin = adminResult.value().rows[0].at("is_admin").get<bool>();
      |                                        ^~~~~
/home/<USER>/database-service-build/src/api/route_controller.cpp:1209:75: error: expected primary-expression before ‘bool’
 1209 |             bool isAdmin = adminResult.value().rows[0].at("is_admin").get<bool>();
      |                                                                           ^~~~
/home/<USER>/database-service-build/src/api/route_controller.cpp:1224:57: error: ‘std::expected<std::__cxx11::basic_string<char>, dbservice::security::SecurityError> dbservice::security::SecurityManager::hashPassword(const std::string&)’ is private within this context
 1224 |         auto hashResult = securityManager_->hashPassword(newPassword);
      |                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~
In file included from /home/<USER>/database-service-build/include/database-service/api/route_controller.hpp:5,
                 from /home/<USER>/database-service-build/src/api/route_controller.cpp:1:
/home/<USER>/database-service-build/include/database-service/security/security_manager.hpp:215:47: note: declared private here
  215 |     std::expected<std::string, SecurityError> hashPassword(const std::string& password);
      |                                               ^~~~~~~~~~~~
/home/<USER>/database-service-build/src/api/route_controller.cpp:1240:43: error: request for member ‘value’ in ‘updateResult’, which is of non-class type ‘int’
 1240 |         if (!updateResult || updateResult.value() <= 0) {
      |                                           ^~~~~
/home/<USER>/database-service-build/src/api/route_controller.cpp: In member function ‘std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> > dbservice::api::RouteController::handleUpdateUserProfile(const dbservice::api::ParsedRequest&)’:
/home/<USER>/database-service-build/src/api/route_controller.cpp:1276:48: error: conversion from ‘int’ to non-scalar type ‘std::string’ {aka ‘std::__cxx11::basic_string<char>’} requested
 1276 |         std::string username = authResult.value();
      |                                ~~~~~~~~~~~~~~~~^~
/home/<USER>/database-service-build/src/api/route_controller.cpp:1291:17: error: no match for ‘operator!’ (operand type is ‘std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >’)
 1291 |             if (!adminResult || adminResult.value().rows.empty()) {
      |                 ^~~~~~~~~~~~
/home/<USER>/database-service-build/src/api/route_controller.cpp:1291:17: note: candidate: ‘operator!(bool)’ (built-in)
/home/<USER>/database-service-build/src/api/route_controller.cpp:1291:17: note:   no known conversion for argument 1 from ‘std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >’ to ‘bool’
/home/<USER>/database-service-build/src/api/route_controller.cpp:1291:45: error: ‘class std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >’ has no member named ‘value’
 1291 |             if (!adminResult || adminResult.value().rows.empty()) {
      |                                             ^~~~~
/home/<USER>/database-service-build/src/api/route_controller.cpp:1295:40: error: ‘class std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >’ has no member named ‘value’
 1295 |             bool isAdmin = adminResult.value().rows[0].at("is_admin").get<bool>();
      |                                        ^~~~~
/home/<USER>/database-service-build/src/api/route_controller.cpp:1295:75: error: expected primary-expression before ‘bool’
 1295 |             bool isAdmin = adminResult.value().rows[0].at("is_admin").get<bool>();
      |                                                                           ^~~~
/home/<USER>/database-service-build/src/api/route_controller.cpp:1331:43: error: request for member ‘value’ in ‘updateResult’, which is of non-class type ‘int’
 1331 |         if (!updateResult || updateResult.value() <= 0) {
      |                                           ^~~~~
/home/<USER>/database-service-build/src/api/route_controller.cpp: In member function ‘std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> > dbservice::api::RouteController::handleDeleteUser(const dbservice::api::ParsedRequest&)’:
/home/<USER>/database-service-build/src/api/route_controller.cpp:1373:55: error: conversion from ‘int’ to non-scalar type ‘std::string’ {aka ‘std::__cxx11::basic_string<char>’} requested
 1373 |         std::string currentUsername = authResult.value();
      |                                       ~~~~~~~~~~~~~~~~^~
/home/<USER>/database-service-build/src/api/route_controller.cpp:1391:13: error: no match for ‘operator!’ (operand type is ‘std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >’)
 1391 |         if (!adminResult || adminResult.value().rows.empty()) {
      |             ^~~~~~~~~~~~
/home/<USER>/database-service-build/src/api/route_controller.cpp:1391:13: note: candidate: ‘operator!(bool)’ (built-in)
/home/<USER>/database-service-build/src/api/route_controller.cpp:1391:13: note:   no known conversion for argument 1 from ‘std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >’ to ‘bool’
/home/<USER>/database-service-build/src/api/route_controller.cpp:1391:41: error: ‘class std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >’ has no member named ‘value’
 1391 |         if (!adminResult || adminResult.value().rows.empty()) {
      |                                         ^~~~~
/home/<USER>/database-service-build/src/api/route_controller.cpp:1395:36: error: ‘class std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >’ has no member named ‘value’
 1395 |         bool isAdmin = adminResult.value().rows[0].at("is_admin").get<bool>();
      |                                    ^~~~~
/home/<USER>/database-service-build/src/api/route_controller.cpp:1395:71: error: expected primary-expression before ‘bool’
 1395 |         bool isAdmin = adminResult.value().rows[0].at("is_admin").get<bool>();
      |                                                                       ^~~~
/home/<USER>/database-service-build/src/api/route_controller.cpp:1405:43: error: request for member ‘value’ in ‘deleteResult’, which is of non-class type ‘int’
 1405 |         if (!deleteResult || deleteResult.value() <= 0) {
      |                                           ^~~~~
/home/<USER>/database-service-build/src/api/route_controller.cpp: In member function ‘std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> > dbservice::api::RouteController::handleActivateUser(const dbservice::api::ParsedRequest&)’:
/home/<USER>/database-service-build/src/api/route_controller.cpp:1444:55: error: conversion from ‘int’ to non-scalar type ‘std::string’ {aka ‘std::__cxx11::basic_string<char>’} requested
 1444 |         std::string currentUsername = authResult.value();
      |                                       ~~~~~~~~~~~~~~~~^~
/home/<USER>/database-service-build/src/api/route_controller.cpp:1457:13: error: no match for ‘operator!’ (operand type is ‘std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >’)
 1457 |         if (!adminResult || adminResult.value().rows.empty()) {
      |             ^~~~~~~~~~~~
/home/<USER>/database-service-build/src/api/route_controller.cpp:1457:13: note: candidate: ‘operator!(bool)’ (built-in)
/home/<USER>/database-service-build/src/api/route_controller.cpp:1457:13: note:   no known conversion for argument 1 from ‘std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >’ to ‘bool’
/home/<USER>/database-service-build/src/api/route_controller.cpp:1457:41: error: ‘class std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >’ has no member named ‘value’
 1457 |         if (!adminResult || adminResult.value().rows.empty()) {
      |                                         ^~~~~
/home/<USER>/database-service-build/src/api/route_controller.cpp:1461:36: error: ‘class std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >’ has no member named ‘value’
 1461 |         bool isAdmin = adminResult.value().rows[0].at("is_admin").get<bool>();
      |                                    ^~~~~
/home/<USER>/database-service-build/src/api/route_controller.cpp:1461:71: error: expected primary-expression before ‘bool’
 1461 |         bool isAdmin = adminResult.value().rows[0].at("is_admin").get<bool>();
      |                                                                       ^~~~
/home/<USER>/database-service-build/src/api/route_controller.cpp:1471:43: error: request for member ‘value’ in ‘updateResult’, which is of non-class type ‘int’
 1471 |         if (!updateResult || updateResult.value() <= 0) {
      |                                           ^~~~~
/home/<USER>/database-service-build/src/api/route_controller.cpp: In member function ‘std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> > dbservice::api::RouteController::handleDeactivateUser(const dbservice::api::ParsedRequest&)’:
/home/<USER>/database-service-build/src/api/route_controller.cpp:1511:55: error: conversion from ‘int’ to non-scalar type ‘std::string’ {aka ‘std::__cxx11::basic_string<char>’} requested
 1511 |         std::string currentUsername = authResult.value();
      |                                       ~~~~~~~~~~~~~~~~^~
/home/<USER>/database-service-build/src/api/route_controller.cpp:1529:13: error: no match for ‘operator!’ (operand type is ‘std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >’)
 1529 |         if (!adminResult || adminResult.value().rows.empty()) {
      |             ^~~~~~~~~~~~
/home/<USER>/database-service-build/src/api/route_controller.cpp:1529:13: note: candidate: ‘operator!(bool)’ (built-in)
/home/<USER>/database-service-build/src/api/route_controller.cpp:1529:13: note:   no known conversion for argument 1 from ‘std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >’ to ‘bool’
/home/<USER>/database-service-build/src/api/route_controller.cpp:1529:41: error: ‘class std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >’ has no member named ‘value’
 1529 |         if (!adminResult || adminResult.value().rows.empty()) {
      |                                         ^~~~~
/home/<USER>/database-service-build/src/api/route_controller.cpp:1533:36: error: ‘class std::vector<std::vector<std::__cxx11::basic_string<char>, std::allocator<std::__cxx11::basic_string<char> > > >’ has no member named ‘value’
 1533 |         bool isAdmin = adminResult.value().rows[0].at("is_admin").get<bool>();
      |                                    ^~~~~
/home/<USER>/database-service-build/src/api/route_controller.cpp:1533:71: error: expected primary-expression before ‘bool’
 1533 |         bool isAdmin = adminResult.value().rows[0].at("is_admin").get<bool>();
      |                                                                       ^~~~
/home/<USER>/database-service-build/src/api/route_controller.cpp:1543:43: error: request for member ‘value’ in ‘updateResult’, which is of non-class type ‘int’
 1543 |         if (!updateResult || updateResult.value() <= 0) {
      |                                           ^~~~~
make[2]: *** [CMakeFiles/database-service.dir/build.make:93: CMakeFiles/database-service.dir/src/api/route_controller.cpp.o] Error 1
make[2]: *** Waiting for unfinished jobs....
make[1]: *** [CMakeFiles/Makefile2:87: CMakeFiles/database-service.dir/all] Error 2
make: *** [Makefile:136: all] Error 2
