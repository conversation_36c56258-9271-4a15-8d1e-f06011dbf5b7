#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <chrono>
#include <thread>

#include "database-service/core/connection_manager.hpp"
#include "database-service/security/security_manager.hpp"
#include "database-service/utils/cache.hpp"
#include "database-service/utils/config_manager.hpp"

using namespace dbservice;
using namespace std::chrono_literals;

class ImprovementsTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Set up test configuration
        auto& config = utils::ConfigManager::getInstance();
        config.loadFromString(R"({
            "database": {
                "connection_string": "postgresql://test:test@localhost:5432/test_db",
                "max_connections": 10,
                "ssl": {
                    "enabled": false
                }
            },
            "security": {
                "jwt_secret": "test-secret-key-for-testing-only",
                "token_expiration_seconds": 3600
            }
        })");
    }
};

// Test 1: Auto-scaling functionality
TEST_F(ImprovementsTest, ConnectionPoolAutoScaling) {
    core::SSLConfig sslConfig{false, core::SSLMode::Disable, "", "", ""};
    auto connectionManager = std::make_unique<core::ConnectionManager>(
        "postgresql://test:test@localhost:5432/test_db", 5, sslConfig);

    // Configure auto-scaling
    connectionManager->configureAutoScaling(
        true,    // enabled
        2,       // min connections
        20,      // max connections
        0.8,     // scale up threshold
        0.3,     // scale down threshold
        100ms    // check interval
    );

    // Get auto-scaling stats
    auto stats = connectionManager->getAutoScalingStats();
    EXPECT_TRUE(stats["enabled"].get<bool>());
    EXPECT_EQ(stats["min_connections"].get<size_t>(), 2);
    EXPECT_EQ(stats["max_connections"].get<size_t>(), 20);
    EXPECT_DOUBLE_EQ(stats["scale_up_threshold"].get<double>(), 0.8);
    EXPECT_DOUBLE_EQ(stats["scale_down_threshold"].get<double>(), 0.3);
}

// Test 2: Query caching functionality
TEST_F(ImprovementsTest, QueryCaching) {
    utils::QueryCache cache(100, 5s);

    // Test cache miss
    auto result1 = cache.get("SELECT * FROM users", {"param1"});
    EXPECT_FALSE(result1.has_value());

    // Add to cache
    nlohmann::json testData = {{"id", 1}, {"name", "test"}};
    cache.put("SELECT * FROM users", {"param1"}, testData);

    // Test cache hit
    auto result2 = cache.get("SELECT * FROM users", {"param1"});
    EXPECT_TRUE(result2.has_value());
    EXPECT_EQ(result2->at("id"), 1);
    EXPECT_EQ(result2->at("name"), "test");

    // Test cache stats
    auto stats = cache.getStats();
    EXPECT_EQ(stats["size"].get<size_t>(), 1);
    EXPECT_GT(stats["hits"].get<size_t>(), 0);
    EXPECT_GT(stats["misses"].get<size_t>(), 0);
}

// Test 3: JWT token validation (fixed hard-coded secret)
TEST_F(ImprovementsTest, JWTTokenValidation) {
    auto connectionManager = std::make_shared<core::ConnectionManager>(
        "postgresql://test:test@localhost:5432/test_db", 5, 
        core::SSLConfig{false, core::SSLMode::Disable, "", "", ""});

    auto securityManager = std::make_unique<security::SecurityManager>(connectionManager);

    // Test JWT token validation with proper secret
    std::unordered_map<std::string, std::string> payload;
    
    // This should not use hard-coded secret anymore
    bool result = securityManager->validateJwtToken("invalid.token.here", payload);
    EXPECT_FALSE(result); // Should fail for invalid token, but not due to hard-coded secret
}

// Test 4: CORS configuration (fixed duplicate configuration)
TEST_F(ImprovementsTest, CORSConfiguration) {
    // This test verifies that CORS configuration is not duplicated
    // We can't easily test the internal implementation, but we can verify
    // that the configuration loading doesn't cause issues
    
    auto& config = utils::ConfigManager::getInstance();
    config.loadFromString(R"({
        "api": {
            "cors": {
                "enabled": true,
                "allowed_origins": ["http://localhost:3000"],
                "allowed_methods": ["GET", "POST"],
                "allowed_headers": ["Content-Type", "Authorization"],
                "allow_credentials": true,
                "max_age": 86400
            }
        }
    })");

    // Verify configuration can be loaded without issues
    bool corsEnabled = config.getBool("api.cors.enabled", false);
    EXPECT_TRUE(corsEnabled);
    
    int maxAge = config.getInt("api.cors.max_age", 0);
    EXPECT_EQ(maxAge, 86400);
}

// Test 5: Cache expiration and cleanup
TEST_F(ImprovementsTest, CacheExpiration) {
    utils::QueryCache cache(100, 100ms); // Very short TTL for testing

    // Add item to cache
    nlohmann::json testData = {{"id", 1}, {"name", "test"}};
    cache.put("SELECT * FROM users", {}, testData, 50ms);

    // Should be available immediately
    auto result1 = cache.get("SELECT * FROM users", {});
    EXPECT_TRUE(result1.has_value());

    // Wait for expiration
    std::this_thread::sleep_for(100ms);

    // Should be expired now
    auto result2 = cache.get("SELECT * FROM users", {});
    EXPECT_FALSE(result2.has_value());
}

// Test 6: Connection manager metrics with auto-scaling
TEST_F(ImprovementsTest, ConnectionManagerMetrics) {
    core::SSLConfig sslConfig{false, core::SSLMode::Disable, "", "", ""};
    auto connectionManager = std::make_unique<core::ConnectionManager>(
        "postgresql://test:test@localhost:5432/test_db", 5, sslConfig);

    // Enable auto-scaling
    connectionManager->configureAutoScaling(true, 2, 10, 0.8, 0.3, 100ms);

    // Update metrics (this should trigger auto-scaling check)
    connectionManager->updateMetrics();

    // Get metrics
    auto metricsResult = connectionManager->getMetrics();
    EXPECT_TRUE(metricsResult.has_value());

    auto metrics = metricsResult.value();
    EXPECT_TRUE(metrics.contains("max_connections"));
    EXPECT_TRUE(metrics.contains("available_connections"));
}

// Test 7: Cache statistics and performance
TEST_F(ImprovementsTest, CachePerformance) {
    utils::QueryCache cache(1000, 1min);

    // Add multiple entries
    for (int i = 0; i < 100; ++i) {
        nlohmann::json data = {{"id", i}, {"value", "test" + std::to_string(i)}};
        cache.put("SELECT * FROM table" + std::to_string(i), {}, data);
    }

    // Test cache stats
    auto stats = cache.getStats();
    EXPECT_EQ(stats["size"].get<size_t>(), 100);

    // Test cache hits
    for (int i = 0; i < 50; ++i) {
        auto result = cache.get("SELECT * FROM table" + std::to_string(i), {});
        EXPECT_TRUE(result.has_value());
    }

    // Check hit rate
    auto finalStats = cache.getStats();
    double hitRate = finalStats["hit_rate"].get<double>();
    EXPECT_GT(hitRate, 0.0);
}
