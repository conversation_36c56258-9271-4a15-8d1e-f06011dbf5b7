# Auth-Service Technical Implementation Guide

**Document Version**: 1.0  
**Created**: 2025-01-07  
**Author**: CHCIT DevOps Team  
**Purpose**: Technical implementation details for the auth-service microservice

---

## 🎯 **Implementation Overview**

This document provides detailed technical implementation information for the auth-service microservice, including code architecture, deployment strategies, and operational procedures.

---

## 🏗️ **Project Structure**

### **Directory Organization**
```
D:\Coding_Projects\auth-service\
├── cert_sync_helper_app\           # SSL certificate management
│   ├── auth_cert_sync_helper.cpp   # C++23 certificate sync helper
│   ├── sync-auth-certificates.sh   # Certificate distribution script
│   └── COMPILATION-INSTRUCTIONS.md # Build instructions
├── auth-service-deployment\        # Deployment automation
│   └── deployment_scripts\
│       ├── config\                 # Environment configurations
│       │   ├── auth-service-development.json
│       │   └── auth-service-production.json
│       ├── Modules\                # PowerShell deployment modules
│       │   ├── Logger\             # Logging functionality
│       │   └── Setup-AuthServiceSSL.psm1  # SSL management
│       └── deploy-auth-service-modular.ps1 # Main deployment script
└── src\                           # Source code (to be created)
    ├── main.cpp                   # Application entry point
    ├── auth\                      # Authentication modules
    ├── api\                       # API endpoints
    ├── database\                  # Database integration
    └── security\                  # Security utilities
```

---

## 🔧 **Technology Stack Implementation**

### **C++23 Core Service**

#### **Build System: CMake**
```cmake
# CMakeLists.txt
cmake_minimum_required(VERSION 3.25)
project(auth-service VERSION 1.0.0 LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 23)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Dependencies
find_package(OpenSSL REQUIRED)
find_package(PostgreSQL REQUIRED)
find_package(nlohmann_json REQUIRED)

# Compiler flags
set(CMAKE_CXX_FLAGS_RELEASE "-O3 -DNDEBUG")
set(CMAKE_CXX_FLAGS_DEBUG "-g -O0 -DDEBUG")

# Security flags
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fstack-protector-strong -D_FORTIFY_SOURCE=2")

# Target
add_executable(auth-service
    src/main.cpp
    src/auth/authentication_manager.cpp
    src/auth/token_manager.cpp
    src/api/http_server.cpp
    src/database/connection_pool.cpp
    src/security/crypto_utils.cpp
)

target_link_libraries(auth-service
    OpenSSL::SSL
    OpenSSL::Crypto
    PostgreSQL::PostgreSQL
    nlohmann_json::nlohmann_json
)
```

#### **Core Application Architecture**
```cpp
// main.cpp - Application entry point
#include <iostream>
#include <memory>
#include <signal.h>
#include "auth/authentication_manager.hpp"
#include "api/http_server.hpp"
#include "database/connection_pool.hpp"
#include "config/configuration.hpp"

class AuthServiceApplication {
private:
    std::unique_ptr<HttpServer> http_server_;
    std::unique_ptr<AuthenticationManager> auth_manager_;
    std::unique_ptr<DatabaseConnectionPool> db_pool_;
    Configuration config_;
    
public:
    bool initialize(const std::string& config_file);
    void run();
    void shutdown();
};

int main(int argc, char* argv[]) {
    try {
        AuthServiceApplication app;
        
        // Load configuration
        std::string config_file = (argc > 1) ? argv[1] : "/etc/auth-service/config.json";
        
        if (!app.initialize(config_file)) {
            std::cerr << "Failed to initialize auth-service" << std::endl;
            return 1;
        }
        
        // Setup signal handlers
        signal(SIGINT, [](int) { /* graceful shutdown */ });
        signal(SIGTERM, [](int) { /* graceful shutdown */ });
        
        // Run the service
        app.run();
        
    } catch (const std::exception& e) {
        std::cerr << "Fatal error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
```

### **Database Integration**

#### **PostgreSQL Schema Design**
```sql
-- User management tables
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(255) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    salt VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP,
    failed_login_attempts INTEGER DEFAULT 0,
    locked_until TIMESTAMP NULL
);

-- Role-based access control
CREATE TABLE roles (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE user_roles (
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    role_id INTEGER REFERENCES roles(id) ON DELETE CASCADE,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (user_id, role_id)
);

-- Session management
CREATE TABLE sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    token_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    last_accessed TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address INET,
    user_agent TEXT,
    is_active BOOLEAN DEFAULT true
);

-- Audit logging
CREATE TABLE auth_audit_log (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    action VARCHAR(100) NOT NULL,
    ip_address INET,
    user_agent TEXT,
    success BOOLEAN NOT NULL,
    error_message TEXT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for performance
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_sessions_user_id ON sessions(user_id);
CREATE INDEX idx_sessions_token_hash ON sessions(token_hash);
CREATE INDEX idx_sessions_expires_at ON sessions(expires_at);
CREATE INDEX idx_audit_log_user_id ON auth_audit_log(user_id);
CREATE INDEX idx_audit_log_timestamp ON auth_audit_log(timestamp);
```

#### **Connection Pool Implementation**
```cpp
// database/connection_pool.hpp
#include <libpq-fe.h>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <memory>

class DatabaseConnectionPool {
private:
    std::queue<std::unique_ptr<PGconn, decltype(&PQfinish)>> available_connections_;
    std::mutex mutex_;
    std::condition_variable condition_;
    std::string connection_string_;
    size_t pool_size_;
    size_t max_connections_;
    
public:
    DatabaseConnectionPool(const std::string& connection_string, 
                          size_t initial_size = 10, 
                          size_t max_size = 50);
    
    ~DatabaseConnectionPool();
    
    // Connection management
    std::unique_ptr<PGconn, decltype(&PQfinish)> acquire_connection();
    void release_connection(std::unique_ptr<PGconn, decltype(&PQfinish)> conn);
    
    // Pool management
    bool initialize();
    void shutdown();
    size_t available_count() const;
    size_t total_count() const;
};
```

---

## 🔐 **Security Implementation**

### **Authentication Flow**

#### **JWT Token Management**
```cpp
// auth/token_manager.hpp
#include <jwt-cpp/jwt.h>
#include <chrono>
#include <string>

class TokenManager {
private:
    std::string secret_key_;
    std::chrono::seconds token_lifetime_;
    std::string issuer_;
    
public:
    TokenManager(const std::string& secret_key, 
                std::chrono::seconds lifetime = std::chrono::hours(24),
                const std::string& issuer = "auth-service");
    
    // Token operations
    std::string generate_token(const User& user);
    bool validate_token(const std::string& token);
    std::optional<UserClaims> extract_claims(const std::string& token);
    
    // Token lifecycle
    bool revoke_token(const std::string& token);
    bool is_token_revoked(const std::string& token);
    void cleanup_expired_tokens();
};

// Implementation
std::string TokenManager::generate_token(const User& user) {
    auto now = std::chrono::system_clock::now();
    auto exp = now + token_lifetime_;
    
    return jwt::create()
        .set_issuer(issuer_)
        .set_type("JWT")
        .set_issued_at(now)
        .set_expires_at(exp)
        .set_payload_claim("user_id", jwt::claim(std::to_string(user.id)))
        .set_payload_claim("username", jwt::claim(user.username))
        .set_payload_claim("roles", jwt::claim(user.roles))
        .sign(jwt::algorithm::hs256{secret_key_});
}
```

#### **Password Security**
```cpp
// security/crypto_utils.hpp
#include <openssl/evp.h>
#include <openssl/rand.h>
#include <string>
#include <vector>

class CryptoUtils {
public:
    // Password hashing
    static std::string generate_salt(size_t length = 32);
    static std::string hash_password(const std::string& password, const std::string& salt);
    static bool verify_password(const std::string& password, 
                               const std::string& hash, 
                               const std::string& salt);
    
    // Token generation
    static std::string generate_secure_token(size_t length = 32);
    static std::string generate_api_key(size_t length = 64);
    
    // Encryption/Decryption
    static std::vector<uint8_t> encrypt_data(const std::vector<uint8_t>& data, 
                                            const std::string& key);
    static std::vector<uint8_t> decrypt_data(const std::vector<uint8_t>& encrypted_data, 
                                            const std::string& key);
};

// Implementation using bcrypt-style hashing
std::string CryptoUtils::hash_password(const std::string& password, const std::string& salt) {
    const int iterations = 12; // bcrypt cost factor
    
    unsigned char hash[EVP_MAX_MD_SIZE];
    unsigned int hash_len;
    
    PKCS5_PBKDF2_HMAC(password.c_str(), password.length(),
                      reinterpret_cast<const unsigned char*>(salt.c_str()), salt.length(),
                      iterations, EVP_sha256(), sizeof(hash), hash);
    
    // Convert to hex string
    std::string result;
    for (unsigned int i = 0; i < hash_len; ++i) {
        char hex[3];
        sprintf(hex, "%02x", hash[i]);
        result += hex;
    }
    
    return result;
}
```

---

## 🌐 **API Implementation**

### **HTTP Server Architecture**
```cpp
// api/http_server.hpp
#include <httplib.h>
#include <nlohmann/json.hpp>
#include <memory>

class HttpServer {
private:
    std::unique_ptr<httplib::Server> server_;
    std::shared_ptr<AuthenticationManager> auth_manager_;
    std::string ssl_cert_path_;
    std::string ssl_key_path_;
    int port_;
    bool ssl_enabled_;
    
public:
    HttpServer(int port = 8082, bool ssl_enabled = true);
    
    // Server lifecycle
    bool initialize(const Configuration& config);
    void start();
    void stop();
    
    // Route registration
    void setup_routes();
    void setup_middleware();
    
private:
    // Authentication endpoints
    void handle_login(const httplib::Request& req, httplib::Response& res);
    void handle_logout(const httplib::Request& req, httplib::Response& res);
    void handle_validate_token(const httplib::Request& req, httplib::Response& res);
    void handle_refresh_token(const httplib::Request& req, httplib::Response& res);
    
    // User management endpoints
    void handle_register_user(const httplib::Request& req, httplib::Response& res);
    void handle_get_user_profile(const httplib::Request& req, httplib::Response& res);
    void handle_update_user_profile(const httplib::Request& req, httplib::Response& res);
    
    // Administrative endpoints
    void handle_list_users(const httplib::Request& req, httplib::Response& res);
    void handle_manage_roles(const httplib::Request& req, httplib::Response& res);
    
    // Utility methods
    bool authenticate_request(const httplib::Request& req);
    nlohmann::json create_error_response(const std::string& message, int code);
    void set_cors_headers(httplib::Response& res);
};
```

### **API Endpoints**

#### **Authentication Endpoints**
```cpp
// POST /api/v1/auth/login
void HttpServer::handle_login(const httplib::Request& req, httplib::Response& res) {
    try {
        auto json_body = nlohmann::json::parse(req.body);
        
        std::string username = json_body["username"];
        std::string password = json_body["password"];
        
        // Validate input
        if (username.empty() || password.empty()) {
            res.status = 400;
            res.set_content(create_error_response("Username and password required", 400).dump(), 
                           "application/json");
            return;
        }
        
        // Authenticate user
        auto auth_result = auth_manager_->authenticate_user(username, password);
        
        if (auth_result.success) {
            // Generate token
            auto token = auth_manager_->generate_token(auth_result.user);
            
            nlohmann::json response = {
                {"success", true},
                {"token", token},
                {"user", {
                    {"id", auth_result.user.id},
                    {"username", auth_result.user.username},
                    {"email", auth_result.user.email},
                    {"roles", auth_result.user.roles}
                }},
                {"expires_at", auth_result.expires_at}
            };
            
            res.status = 200;
            res.set_content(response.dump(), "application/json");
            
            // Log successful authentication
            auth_manager_->log_auth_event(auth_result.user.id, "login", true, req.get_header_value("X-Real-IP"));
            
        } else {
            res.status = 401;
            res.set_content(create_error_response("Invalid credentials", 401).dump(), 
                           "application/json");
            
            // Log failed authentication
            auth_manager_->log_auth_event(0, "login_failed", false, req.get_header_value("X-Real-IP"));
        }
        
    } catch (const std::exception& e) {
        res.status = 500;
        res.set_content(create_error_response("Internal server error", 500).dump(), 
                       "application/json");
    }
}

// POST /api/v1/auth/validate
void HttpServer::handle_validate_token(const httplib::Request& req, httplib::Response& res) {
    std::string auth_header = req.get_header_value("Authorization");
    
    if (auth_header.empty() || auth_header.substr(0, 7) != "Bearer ") {
        res.status = 401;
        res.set_content(create_error_response("Missing or invalid authorization header", 401).dump(), 
                       "application/json");
        return;
    }
    
    std::string token = auth_header.substr(7);
    
    auto validation_result = auth_manager_->validate_token(token);
    
    if (validation_result.valid) {
        nlohmann::json response = {
            {"valid", true},
            {"user", {
                {"id", validation_result.user.id},
                {"username", validation_result.user.username},
                {"roles", validation_result.user.roles}
            }},
            {"expires_at", validation_result.expires_at}
        };
        
        res.status = 200;
        res.set_content(response.dump(), "application/json");
    } else {
        res.status = 401;
        res.set_content(create_error_response("Invalid or expired token", 401).dump(), 
                       "application/json");
    }
}
```

---

## 🚀 **Deployment Implementation**

### **SSL Certificate Integration**

#### **Certificate Management**
The auth-service integrates with the existing CHCIT certificate infrastructure:

- **Certificate Location**: `/home/<USER>/letsencrypt_backup/live/chcit.org/`
- **Shared with**: git.chcit.org and db.chcit.org services
- **Automatic Sync**: Certificates automatically distributed via cert_sync_helper
- **SSL Endpoints**: 
  - Development: `https://auth-dev.chcit.org:8082`
  - Production: `https://auth.chcit.org:8082`

#### **SSL Configuration**
```cpp
// SSL configuration in HttpServer
bool HttpServer::initialize(const Configuration& config) {
    if (ssl_enabled_) {
        ssl_cert_path_ = config.ssl.cert_path;
        ssl_key_path_ = config.ssl.key_path;
        
        // Verify certificate files exist
        if (!std::filesystem::exists(ssl_cert_path_) || 
            !std::filesystem::exists(ssl_key_path_)) {
            throw std::runtime_error("SSL certificate files not found");
        }
        
        // Configure HTTPS server
        server_ = std::make_unique<httplib::SSLServer>(ssl_cert_path_.c_str(), 
                                                      ssl_key_path_.c_str());
    } else {
        server_ = std::make_unique<httplib::Server>();
    }
    
    setup_routes();
    setup_middleware();
    
    return true;
}
```

### **Configuration Management**

#### **Environment-Specific Configurations**
```json
// auth-service-development.json
{
  "server": {
    "port": 8082,
    "ssl_enabled": true,
    "ssl_cert_path": "/home/<USER>/letsencrypt_backup/live/chcit.org/cert.pem",
    "ssl_key_path": "/home/<USER>/letsencrypt_backup/live/chcit.org/privkey.pem",
    "ssl_ca_path": "/home/<USER>/letsencrypt_backup/live/chcit.org/chain.pem"
  },
  "database": {
    "host": "***********",
    "port": 5432,
    "name": "auth_service",
    "user": "auth_service",
    "password": "VOUGaH&Lr-p6#(oB1r$JoGXk",
    "pool_size": 10,
    "max_connections": 50
  },
  "authentication": {
    "jwt_secret": "your-jwt-secret-key-here",
    "token_lifetime_hours": 24,
    "session_timeout_minutes": 30,
    "max_failed_attempts": 5,
    "lockout_duration_minutes": 15
  },
  "security": {
    "require_https": true,
    "hsts_enabled": true,
    "cors_enabled": true,
    "allowed_origins": ["https://project-tracker.chcit.org", "https://dev.chcit.org"]
  },
  "logging": {
    "level": "info",
    "file": "/var/log/auth-service/auth-service.log",
    "max_size_mb": 100,
    "max_files": 10
  },
  "ssh": {
    "host": "dev.chcit.org",
    "username": "btaylor-admin",
    "port": 22,
    "key_path": "C:\\Users\\<USER>\\.ssh\\id_rsa"
  }
}
```

---

## 📊 **Monitoring and Observability**

### **Health Check Endpoints**
```cpp
// Health check implementation
void HttpServer::setup_routes() {
    // Health check endpoint
    server_->Get("/health", [this](const httplib::Request& req, httplib::Response& res) {
        nlohmann::json health_status = {
            {"status", "healthy"},
            {"timestamp", std::chrono::system_clock::now()},
            {"version", "1.0.0"},
            {"database", check_database_health()},
            {"ssl", ssl_enabled_ ? "enabled" : "disabled"}
        };
        
        res.set_content(health_status.dump(), "application/json");
    });
    
    // Metrics endpoint
    server_->Get("/metrics", [this](const httplib::Request& req, httplib::Response& res) {
        if (!authenticate_request(req)) {
            res.status = 401;
            return;
        }
        
        nlohmann::json metrics = collect_metrics();
        res.set_content(metrics.dump(), "application/json");
    });
}

nlohmann::json HttpServer::collect_metrics() {
    return {
        {"active_sessions", auth_manager_->get_active_session_count()},
        {"total_users", auth_manager_->get_total_user_count()},
        {"requests_per_minute", get_request_rate()},
        {"database_connections", db_pool_->available_count()},
        {"uptime_seconds", get_uptime_seconds()}
    };
}
```

---

## 🔧 **Build and Deployment**

### **Compilation Instructions**
```bash
# Build the auth-service
mkdir build && cd build
cmake -DCMAKE_BUILD_TYPE=Release ..
make -j$(nproc)

# Run tests
make test

# Install
sudo make install
```

### **Service Configuration**
```ini
# /etc/systemd/system/auth-service.service
[Unit]
Description=Auth Service
After=network.target postgresql.service

[Service]
Type=simple
User=auth-service
Group=auth-service
ExecStart=/usr/local/bin/auth-service /etc/auth-service/config.json
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
```

---

**This technical implementation provides the foundation for a robust, secure, and scalable authentication microservice that integrates seamlessly with the existing CHCIT infrastructure.**
