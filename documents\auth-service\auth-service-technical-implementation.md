# Auth-Service Technical Implementation Guide

**Document Version**: 1.0  
**Created**: 2025-01-07  
**Author**: CHCIT DevOps Team  
**Purpose**: Technical implementation details for the auth-service microservice

---

## 🎯 **Implementation Overview**

This document provides detailed technical implementation information for the auth-service microservice, including code architecture, deployment strategies, and operational procedures.

---

## 🏗️ **Project Structure**

### **Directory Organization**
```
D:\Coding_Projects\auth-service\
├── cert_sync_helper_app\           # SSL certificate management
│   ├── auth_cert_sync_helper.cpp   # C++23 certificate sync helper
│   ├── sync-auth-certificates.sh   # Certificate distribution script
│   └── COMPILATION-INSTRUCTIONS.md # Build instructions
├── auth-service-deployment\        # Deployment automation
│   └── deployment_scripts\
│       ├── config\                 # Environment configurations
│       │   ├── auth-service-development.json
│       │   └── auth-service-production.json
│       ├── Modules\                # PowerShell deployment modules
│       │   ├── Logger\             # Logging functionality
│       │   └── Setup-AuthServiceSSL.psm1  # SSL management
│       └── deploy-auth-service-modular.ps1 # Main deployment script
└── src\                           # Source code (to be created)
    ├── main.cpp                   # Application entry point
    ├── auth\                      # Authentication modules
    ├── api\                       # API endpoints
    ├── database\                  # Database integration
    └── security\                  # Security utilities
```

---

## 🔧 **Technology Stack Implementation**

### **C++23 Core Service**

#### **Build System: CMake**
```cmake
# CMakeLists.txt
cmake_minimum_required(VERSION 3.25)
project(auth-service VERSION 1.0.0 LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 23)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Dependencies
find_package(OpenSSL REQUIRED)
find_package(PostgreSQL REQUIRED)
find_package(nlohmann_json REQUIRED)
find_package(PkgConfig REQUIRED)
pkg_check_modules(VALKEY REQUIRED valkey)
find_package(argon2 REQUIRED)
find_package(jwt-cpp REQUIRED)

# Compiler flags
set(CMAKE_CXX_FLAGS_RELEASE "-O3 -DNDEBUG")
set(CMAKE_CXX_FLAGS_DEBUG "-g -O0 -DDEBUG")

# Security flags
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fstack-protector-strong -D_FORTIFY_SOURCE=2")

# Target
add_executable(auth-service
    src/main.cpp
    src/auth/oauth_server.cpp
    src/auth/oidc_provider.cpp
    src/auth/token_manager.cpp
    src/api/http_server.cpp
    src/database/connection_pool.cpp
    src/cache/valkey_client.cpp
    src/security/crypto_utils.cpp
    src/security/argon2_hasher.cpp
)

target_link_libraries(auth-service
    OpenSSL::SSL
    OpenSSL::Crypto
    PostgreSQL::PostgreSQL
    nlohmann_json::nlohmann_json
    ${VALKEY_LIBRARIES}
    argon2
    jwt-cpp::jwt-cpp
)
```

#### **Core Application Architecture**
```cpp
// main.cpp - Application entry point
#include <iostream>
#include <memory>
#include <signal.h>
#include "auth/authentication_manager.hpp"
#include "api/http_server.hpp"
#include "database/connection_pool.hpp"
#include "config/configuration.hpp"

class AuthServiceApplication {
private:
    std::unique_ptr<HttpServer> http_server_;
    std::unique_ptr<OAuthServer> oauth_server_;
    std::unique_ptr<OIDCProvider> oidc_provider_;
    std::unique_ptr<DatabaseConnectionPool> db_pool_;
    std::unique_ptr<ValkeyClient> cache_client_;
    Configuration config_;

public:
    bool initialize(const std::string& config_file);
    void run();
    void shutdown();
};

int main(int argc, char* argv[]) {
    try {
        AuthServiceApplication app;
        
        // Load configuration
        std::string config_file = (argc > 1) ? argv[1] : "/etc/auth-service/config.json";
        
        if (!app.initialize(config_file)) {
            std::cerr << "Failed to initialize auth-service" << std::endl;
            return 1;
        }
        
        // Setup signal handlers
        signal(SIGINT, [](int) { /* graceful shutdown */ });
        signal(SIGTERM, [](int) { /* graceful shutdown */ });
        
        // Run the service
        app.run();
        
    } catch (const std::exception& e) {
        std::cerr << "Fatal error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
```

### **Database Integration**

#### **PostgreSQL Schema Design**
```sql
-- User management tables
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(255) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    salt VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP,
    failed_login_attempts INTEGER DEFAULT 0,
    locked_until TIMESTAMP NULL
);

-- Role-based access control
CREATE TABLE roles (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE user_roles (
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    role_id INTEGER REFERENCES roles(id) ON DELETE CASCADE,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (user_id, role_id)
);

-- Session management
CREATE TABLE sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    token_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    last_accessed TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address INET,
    user_agent TEXT,
    is_active BOOLEAN DEFAULT true
);

-- Audit logging
CREATE TABLE auth_audit_log (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    action VARCHAR(100) NOT NULL,
    ip_address INET,
    user_agent TEXT,
    success BOOLEAN NOT NULL,
    error_message TEXT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for performance
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_sessions_user_id ON sessions(user_id);
CREATE INDEX idx_sessions_token_hash ON sessions(token_hash);
CREATE INDEX idx_sessions_expires_at ON sessions(expires_at);
CREATE INDEX idx_audit_log_user_id ON auth_audit_log(user_id);
CREATE INDEX idx_audit_log_timestamp ON auth_audit_log(timestamp);
```

#### **Connection Pool Implementation**
```cpp
// database/connection_pool.hpp
#include <libpq-fe.h>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <memory>

class DatabaseConnectionPool {
private:
    std::queue<std::unique_ptr<PGconn, decltype(&PQfinish)>> available_connections_;
    std::mutex mutex_;
    std::condition_variable condition_;
    std::string connection_string_;
    size_t pool_size_;
    size_t max_connections_;

public:
    DatabaseConnectionPool(const std::string& connection_string,
                          size_t initial_size = 10,
                          size_t max_size = 50);

    ~DatabaseConnectionPool();

    // Connection management
    std::unique_ptr<PGconn, decltype(&PQfinish)> acquire_connection();
    void release_connection(std::unique_ptr<PGconn, decltype(&PQfinish)> conn);

    // Pool management
    bool initialize();
    void shutdown();
    size_t available_count() const;
    size_t total_count() const;
};
```

### **Valkey Cache Integration**

#### **Valkey Client Implementation**
```cpp
// cache/valkey_client.hpp
#include <valkey/valkey.h>
#include <memory>
#include <string>
#include <optional>
#include <chrono>

class ValkeyClient {
private:
    valkeyContext* context_;
    std::string host_;
    int port_;
    std::string password_;

public:
    ValkeyClient(const std::string& host = "localhost",
                int port = 6379,
                const std::string& password = "");

    ~ValkeyClient();

    // Connection management
    bool connect();
    void disconnect();
    bool is_connected() const;

    // Basic operations
    bool set(const std::string& key, const std::string& value,
             std::chrono::seconds ttl = std::chrono::seconds(0));
    std::optional<std::string> get(const std::string& key);
    bool del(const std::string& key);
    bool exists(const std::string& key);

    // Session management
    bool store_session(const std::string& session_id, const SessionData& data,
                      std::chrono::seconds ttl);
    std::optional<SessionData> get_session(const std::string& session_id);
    bool invalidate_session(const std::string& session_id);

    // Token management
    bool store_refresh_token(const std::string& token_id, const RefreshTokenData& data,
                           std::chrono::seconds ttl);
    std::optional<RefreshTokenData> get_refresh_token(const std::string& token_id);
    bool revoke_refresh_token(const std::string& token_id);

    // OAuth state management
    bool store_oauth_state(const std::string& state, const OAuthStateData& data,
                          std::chrono::minutes ttl = std::chrono::minutes(10));
    std::optional<OAuthStateData> get_oauth_state(const std::string& state);
    bool consume_oauth_state(const std::string& state);
};
```

---

## 🔐 **Security Implementation**

### **Authentication Flow**

#### **JWT Token Management**
```cpp
// auth/token_manager.hpp
#include <jwt-cpp/jwt.h>
#include <chrono>
#include <string>

class TokenManager {
private:
    std::string secret_key_;
    std::chrono::seconds token_lifetime_;
    std::string issuer_;
    
public:
    TokenManager(const std::string& secret_key, 
                std::chrono::seconds lifetime = std::chrono::hours(24),
                const std::string& issuer = "auth-service");
    
    // Token operations
    std::string generate_token(const User& user);
    bool validate_token(const std::string& token);
    std::optional<UserClaims> extract_claims(const std::string& token);
    
    // Token lifecycle
    bool revoke_token(const std::string& token);
    bool is_token_revoked(const std::string& token);
    void cleanup_expired_tokens();
};

// Implementation
std::string TokenManager::generate_token(const User& user) {
    auto now = std::chrono::system_clock::now();
    auto exp = now + token_lifetime_;
    
    return jwt::create()
        .set_issuer(issuer_)
        .set_type("JWT")
        .set_issued_at(now)
        .set_expires_at(exp)
        .set_payload_claim("user_id", jwt::claim(std::to_string(user.id)))
        .set_payload_claim("username", jwt::claim(user.username))
        .set_payload_claim("roles", jwt::claim(user.roles))
        .sign(jwt::algorithm::hs256{secret_key_});
}
```

#### **Argon2id Password Security**
```cpp
// security/argon2_hasher.hpp
#include <argon2.h>
#include <string>
#include <vector>
#include <random>

class Argon2Hasher {
private:
    static constexpr uint32_t MEMORY_COST = 65536;  // 64 MB
    static constexpr uint32_t TIME_COST = 3;        // 3 iterations
    static constexpr uint32_t PARALLELISM = 4;      // 4 threads
    static constexpr uint32_t HASH_LENGTH = 32;     // 32 bytes
    static constexpr uint32_t SALT_LENGTH = 16;     // 16 bytes

public:
    struct HashResult {
        std::string encoded_hash;
        std::vector<uint8_t> salt;
        std::vector<uint8_t> hash;
    };

    // Password hashing with Argon2id
    static HashResult hash_password(const std::string& password);
    static bool verify_password(const std::string& password, const std::string& encoded_hash);

    // Utility functions
    static std::vector<uint8_t> generate_salt();
    static std::string encode_hash(const std::vector<uint8_t>& salt,
                                  const std::vector<uint8_t>& hash);
    static bool decode_hash(const std::string& encoded,
                           std::vector<uint8_t>& salt,
                           std::vector<uint8_t>& hash);
};

// Implementation
Argon2Hasher::HashResult Argon2Hasher::hash_password(const std::string& password) {
    auto salt = generate_salt();
    std::vector<uint8_t> hash(HASH_LENGTH);

    int result = argon2id_hash_raw(
        TIME_COST, MEMORY_COST, PARALLELISM,
        password.c_str(), password.length(),
        salt.data(), SALT_LENGTH,
        hash.data(), HASH_LENGTH
    );

    if (result != ARGON2_OK) {
        throw std::runtime_error("Argon2id hashing failed: " + std::string(argon2_error_message(result)));
    }

    return {
        .encoded_hash = encode_hash(salt, hash),
        .salt = salt,
        .hash = hash
    };
}

bool Argon2Hasher::verify_password(const std::string& password, const std::string& encoded_hash) {
    std::vector<uint8_t> salt, stored_hash;
    if (!decode_hash(encoded_hash, salt, stored_hash)) {
        return false;
    }

    std::vector<uint8_t> computed_hash(HASH_LENGTH);
    int result = argon2id_hash_raw(
        TIME_COST, MEMORY_COST, PARALLELISM,
        password.c_str(), password.length(),
        salt.data(), salt.size(),
        computed_hash.data(), HASH_LENGTH
    );

    if (result != ARGON2_OK) {
        return false;
    }

    // Constant-time comparison
    return std::equal(stored_hash.begin(), stored_hash.end(), computed_hash.begin());
}
```

---

## 🌐 **API Implementation**

### **HTTP Server Architecture**
```cpp
// api/http_server.hpp
#include <httplib.h>
#include <nlohmann/json.hpp>
#include <memory>

class HttpServer {
private:
    std::unique_ptr<httplib::Server> server_;
    std::shared_ptr<OAuthServer> oauth_server_;
    std::shared_ptr<OIDCProvider> oidc_provider_;
    std::string ssl_cert_path_;
    std::string ssl_key_path_;
    int port_;
    bool ssl_enabled_;

public:
    HttpServer(int port = 8082, bool ssl_enabled = true);

    // Server lifecycle
    bool initialize(const Configuration& config);
    void start();
    void stop();

    // Route registration
    void setup_oauth_routes();
    void setup_oidc_routes();
    void setup_api_routes();
    void setup_middleware();

private:
    // OAuth 2.0 endpoints (RFC 6749)
    void handle_authorize(const httplib::Request& req, httplib::Response& res);
    void handle_token(const httplib::Request& req, httplib::Response& res);
    void handle_revoke(const httplib::Request& req, httplib::Response& res);
    void handle_introspect(const httplib::Request& req, httplib::Response& res);

    // OpenID Connect endpoints
    void handle_userinfo(const httplib::Request& req, httplib::Response& res);
    void handle_jwks(const httplib::Request& req, httplib::Response& res);
    void handle_discovery(const httplib::Request& req, httplib::Response& res);

    // User management endpoints
    void handle_register_user(const httplib::Request& req, httplib::Response& res);
    void handle_get_user_profile(const httplib::Request& req, httplib::Response& res);
    void handle_update_user_profile(const httplib::Request& req, httplib::Response& res);

    // Administrative endpoints
    void handle_list_users(const httplib::Request& req, httplib::Response& res);
    void handle_manage_roles(const httplib::Request& req, httplib::Response& res);

    // Utility methods
    bool authenticate_request(const httplib::Request& req);
    nlohmann::json create_error_response(const std::string& error,
                                        const std::string& description = "",
                                        int status_code = 400);
    void set_cors_headers(httplib::Response& res);
    std::string generate_pkce_challenge(const std::string& verifier);
};
```

### **API Endpoints**

#### **OAuth 2.0 Authorization Endpoint**
```cpp
// GET /oauth/authorize - OAuth 2.0 Authorization Endpoint (RFC 6749)
void HttpServer::handle_authorize(const httplib::Request& req, httplib::Response& res) {
    try {
        // Extract OAuth 2.0 parameters
        std::string client_id = req.get_param_value("client_id");
        std::string redirect_uri = req.get_param_value("redirect_uri");
        std::string response_type = req.get_param_value("response_type");
        std::string scope = req.get_param_value("scope");
        std::string state = req.get_param_value("state");
        std::string code_challenge = req.get_param_value("code_challenge");
        std::string code_challenge_method = req.get_param_value("code_challenge_method");

        // Validate required parameters
        if (client_id.empty() || redirect_uri.empty() || response_type.empty()) {
            auto error_response = create_error_response("invalid_request",
                "Missing required parameters");
            res.status = 400;
            res.set_content(error_response.dump(), "application/json");
            return;
        }

        // Validate client and redirect URI
        auto client_validation = oauth_server_->validate_client(client_id, redirect_uri);
        if (!client_validation.valid) {
            auto error_response = create_error_response("invalid_client",
                client_validation.error_description);
            res.status = 400;
            res.set_content(error_response.dump(), "application/json");
            return;
        }

        // Check if user is authenticated
        auto session = get_user_session(req);
        if (!session) {
            // Redirect to login page with return URL
            std::string login_url = "/login?return_to=" +
                url_encode("/oauth/authorize?" + req.raw_query_string);
            res.status = 302;
            res.set_header("Location", login_url);
            return;
        }

        // Generate authorization code
        auto auth_code_result = oauth_server_->generate_authorization_code(
            client_id, session->user_id, redirect_uri, scope,
            code_challenge, code_challenge_method
        );

        if (!auth_code_result.success) {
            auto error_response = create_error_response("server_error",
                "Failed to generate authorization code");
            res.status = 500;
            res.set_content(error_response.dump(), "application/json");
            return;
        }

        // Redirect back to client with authorization code
        std::string callback_url = redirect_uri + "?code=" + auth_code_result.code;
        if (!state.empty()) {
            callback_url += "&state=" + state;
        }

        res.status = 302;
        res.set_header("Location", callback_url);

    } catch (const std::exception& e) {
        auto error_response = create_error_response("server_error", e.what());
        res.status = 500;
        res.set_content(error_response.dump(), "application/json");
    }
}

// POST /oauth/token - OAuth 2.0 Token Endpoint (RFC 6749)
void HttpServer::handle_token(const httplib::Request& req, httplib::Response& res) {
    try {
        auto json_body = nlohmann::json::parse(req.body);
        
        std::string username = json_body["username"];
        std::string password = json_body["password"];
        
        // Validate input
        if (username.empty() || password.empty()) {
            res.status = 400;
            res.set_content(create_error_response("Username and password required", 400).dump(), 
                           "application/json");
            return;
        }
        
        // Authenticate user
        auto auth_result = auth_manager_->authenticate_user(username, password);
        
        if (auth_result.success) {
            // Generate token
            auto token = auth_manager_->generate_token(auth_result.user);
            
            nlohmann::json response = {
                {"success", true},
                {"token", token},
                {"user", {
                    {"id", auth_result.user.id},
                    {"username", auth_result.user.username},
                    {"email", auth_result.user.email},
                    {"roles", auth_result.user.roles}
                }},
                {"expires_at", auth_result.expires_at}
            };
            
            res.status = 200;
            res.set_content(response.dump(), "application/json");
            
            // Log successful authentication
            auth_manager_->log_auth_event(auth_result.user.id, "login", true, req.get_header_value("X-Real-IP"));
            
        } else {
            res.status = 401;
            res.set_content(create_error_response("Invalid credentials", 401).dump(), 
                           "application/json");
            
            // Log failed authentication
            auth_manager_->log_auth_event(0, "login_failed", false, req.get_header_value("X-Real-IP"));
        }
        
    } catch (const std::exception& e) {
        res.status = 500;
        res.set_content(create_error_response("Internal server error", 500).dump(), 
                       "application/json");
    }
}

// POST /api/v1/auth/validate
void HttpServer::handle_validate_token(const httplib::Request& req, httplib::Response& res) {
    std::string auth_header = req.get_header_value("Authorization");
    
    if (auth_header.empty() || auth_header.substr(0, 7) != "Bearer ") {
        res.status = 401;
        res.set_content(create_error_response("Missing or invalid authorization header", 401).dump(), 
                       "application/json");
        return;
    }
    
    std::string token = auth_header.substr(7);
    
    auto validation_result = auth_manager_->validate_token(token);
    
    if (validation_result.valid) {
        nlohmann::json response = {
            {"valid", true},
            {"user", {
                {"id", validation_result.user.id},
                {"username", validation_result.user.username},
                {"roles", validation_result.user.roles}
            }},
            {"expires_at", validation_result.expires_at}
        };
        
        res.status = 200;
        res.set_content(response.dump(), "application/json");
    } else {
        res.status = 401;
        res.set_content(create_error_response("Invalid or expired token", 401).dump(), 
                       "application/json");
    }
}
```

---

## 🚀 **Deployment Implementation**

### **SSL Certificate Integration**

#### **Certificate Management**
The auth-service integrates with the existing CHCIT certificate infrastructure:

- **Certificate Location**: `/home/<USER>/letsencrypt_backup/live/chcit.org/`
- **Shared with**: git.chcit.org and db.chcit.org services
- **Automatic Sync**: Certificates automatically distributed via cert_sync_helper
- **SSL Endpoints**: 
  - Development: `https://auth-dev.chcit.org:8082`
  - Production: `https://auth.chcit.org:8082`

#### **SSL Configuration**
```cpp
// SSL configuration in HttpServer
bool HttpServer::initialize(const Configuration& config) {
    if (ssl_enabled_) {
        ssl_cert_path_ = config.ssl.cert_path;
        ssl_key_path_ = config.ssl.key_path;
        
        // Verify certificate files exist
        if (!std::filesystem::exists(ssl_cert_path_) || 
            !std::filesystem::exists(ssl_key_path_)) {
            throw std::runtime_error("SSL certificate files not found");
        }
        
        // Configure HTTPS server
        server_ = std::make_unique<httplib::SSLServer>(ssl_cert_path_.c_str(), 
                                                      ssl_key_path_.c_str());
    } else {
        server_ = std::make_unique<httplib::Server>();
    }
    
    setup_routes();
    setup_middleware();
    
    return true;
}
```

### **Configuration Management**

#### **OAuth 2.0 + OIDC Configuration**
```json
// auth-service-development.json
{
  "server": {
    "port": 8082,
    "ssl_enabled": true,
    "ssl_cert_path": "/home/<USER>/letsencrypt_backup/live/chcit.org/cert.pem",
    "ssl_key_path": "/home/<USER>/letsencrypt_backup/live/chcit.org/privkey.pem",
    "ssl_ca_path": "/home/<USER>/letsencrypt_backup/live/chcit.org/chain.pem"
  },
  "database": {
    "host": "***********",
    "port": 5432,
    "name": "auth_service",
    "user": "auth_service",
    "password": "VOUGaH&Lr-p6#(oB1r$JoGXk",
    "pool_size": 10,
    "max_connections": 50
  },
  "valkey": {
    "host": "***********",
    "port": 6379,
    "password": "",
    "database": 0,
    "connection_pool_size": 10,
    "connection_timeout_ms": 5000,
    "command_timeout_ms": 3000
  },
  "oauth2": {
    "issuer": "https://auth-dev.chcit.org:8082",
    "authorization_endpoint": "/oauth/authorize",
    "token_endpoint": "/oauth/token",
    "revocation_endpoint": "/oauth/revoke",
    "introspection_endpoint": "/oauth/introspect",
    "authorization_code_lifetime_seconds": 600,
    "access_token_lifetime_seconds": 3600,
    "refresh_token_lifetime_seconds": 604800,
    "require_pkce": true,
    "supported_grant_types": ["authorization_code", "refresh_token"],
    "supported_response_types": ["code"],
    "supported_scopes": ["openid", "profile", "email", "offline_access"]
  },
  "oidc": {
    "userinfo_endpoint": "/oidc/userinfo",
    "jwks_uri": "/.well-known/jwks.json",
    "discovery_endpoint": "/.well-known/openid_configuration",
    "id_token_lifetime_seconds": 3600,
    "supported_claims": ["sub", "name", "email", "email_verified", "preferred_username"],
    "subject_types_supported": ["public"],
    "id_token_signing_alg_values_supported": ["RS256"]
  },
  "jwt": {
    "private_key_path": "/etc/auth-service/keys/jwt-private.pem",
    "public_key_path": "/etc/auth-service/keys/jwt-public.pem",
    "algorithm": "RS256",
    "key_id": "auth-service-2025"
  },
  "security": {
    "argon2": {
      "memory_cost": 65536,
      "time_cost": 3,
      "parallelism": 4,
      "hash_length": 32,
      "salt_length": 16
    },
    "require_https": true,
    "hsts_enabled": true,
    "cors_enabled": true,
    "allowed_origins": ["https://project-tracker.chcit.org", "https://dev.chcit.org"],
    "max_failed_attempts": 5,
    "lockout_duration_minutes": 15,
    "rate_limiting": {
      "enabled": true,
      "requests_per_minute": 60,
      "burst_size": 10
    }
  },
  "logging": {
    "level": "info",
    "file": "/var/log/auth-service/auth-service.log",
    "max_size_mb": 100,
    "max_files": 10,
    "audit_log_file": "/var/log/auth-service/audit.log"
  },
  "ssh": {
    "host": "dev.chcit.org",
    "username": "btaylor-admin",
    "port": 22,
    "key_path": "C:\\Users\\<USER>\\.ssh\\id_rsa"
  }
}
```

---

## 📊 **Monitoring and Observability**

### **Health Check Endpoints**
```cpp
// Health check implementation
void HttpServer::setup_routes() {
    // Health check endpoint
    server_->Get("/health", [this](const httplib::Request& req, httplib::Response& res) {
        nlohmann::json health_status = {
            {"status", "healthy"},
            {"timestamp", std::chrono::system_clock::now()},
            {"version", "1.0.0"},
            {"database", check_database_health()},
            {"ssl", ssl_enabled_ ? "enabled" : "disabled"}
        };
        
        res.set_content(health_status.dump(), "application/json");
    });
    
    // Metrics endpoint
    server_->Get("/metrics", [this](const httplib::Request& req, httplib::Response& res) {
        if (!authenticate_request(req)) {
            res.status = 401;
            return;
        }
        
        nlohmann::json metrics = collect_metrics();
        res.set_content(metrics.dump(), "application/json");
    });
}

nlohmann::json HttpServer::collect_metrics() {
    return {
        {"active_sessions", auth_manager_->get_active_session_count()},
        {"total_users", auth_manager_->get_total_user_count()},
        {"requests_per_minute", get_request_rate()},
        {"database_connections", db_pool_->available_count()},
        {"uptime_seconds", get_uptime_seconds()}
    };
}
```

---

## 🔧 **Build and Deployment**

### **Compilation Instructions**
```bash
# Build the auth-service
mkdir build && cd build
cmake -DCMAKE_BUILD_TYPE=Release ..
make -j$(nproc)

# Run tests
make test

# Install
sudo make install
```

### **Service Configuration**
```ini
# /etc/systemd/system/auth-service.service
[Unit]
Description=Auth Service
After=network.target postgresql.service

[Service]
Type=simple
User=auth-service
Group=auth-service
ExecStart=/usr/local/bin/auth-service /etc/auth-service/config.json
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
```

---

**This technical implementation provides the foundation for a robust, secure, and scalable authentication microservice that integrates seamlessly with the existing CHCIT infrastructure.**
