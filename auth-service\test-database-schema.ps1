# Test Database Schema Deployment
# Step 1: OAuth 2.0 Database Schema Creation and Testing

Write-Host "🗄️ Testing OAuth 2.0 Database Schema Deployment" -ForegroundColor Yellow
Write-Host "=" * 60 -ForegroundColor Yellow

# SSH connection details
$sshHost = "dev.chcit.org"
$sshUser = "btaylor-admin"
$sshKey = "C:\Users\<USER>\.ssh\id_rsa"

# Function to execute SSH commands
function Invoke-SSHCommand {
    param(
        [string]$Command,
        [string]$Description = ""
    )
    
    if ($Description) {
        Write-Host "  ▶️ $Description" -ForegroundColor White
    }
    
    $sshCmd = "ssh -i `"$sshKey`" -o StrictHostKeyChecking=no $sshUser@$sshHost `"$Command`""
    
    try {
        $result = Invoke-Expression $sshCmd 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  ✅ Success" -ForegroundColor Green
            if ($result) {
                Write-Host "     Output: $result" -ForegroundColor Gray
            }
            return $true
        } else {
            Write-Host "  ❌ Failed (Exit Code: $LASTEXITCODE)" -ForegroundColor Red
            if ($result) {
                Write-Host "     Error: $result" -ForegroundColor Red
            }
            return $false
        }
    } catch {
        Write-Host "  ❌ Exception: $_" -ForegroundColor Red
        return $false
    }
}

Write-Host "`n📋 Step 1: Copy Schema File to Server" -ForegroundColor Yellow

# Copy the schema file to the server
$localSchemaPath = "D:\Coding_Projects\auth-service\auth-service-app\database\auth_schema.sql"
$remoteSchemaPath = "/home/<USER>/auth_schema.sql"

if (Test-Path $localSchemaPath) {
    Write-Host "  ▶️ Copying schema file to server..." -ForegroundColor White
    
    $scpCmd = "scp -i `"$sshKey`" -o StrictHostKeyChecking=no `"$localSchemaPath`" $sshUser@${sshHost}:$remoteSchemaPath"
    
    try {
        $result = Invoke-Expression $scpCmd 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  ✅ Schema file copied successfully" -ForegroundColor Green
        } else {
            Write-Host "  ❌ Failed to copy schema file" -ForegroundColor Red
            Write-Host "     Error: $result" -ForegroundColor Red
            exit 1
        }
    } catch {
        Write-Host "  ❌ Exception copying file: $_" -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host "  ❌ Schema file not found: $localSchemaPath" -ForegroundColor Red
    exit 1
}

Write-Host "`n🔍 Step 2: Check PostgreSQL Status" -ForegroundColor Yellow

# Check if PostgreSQL is running
Invoke-SSHCommand "sudo systemctl status postgresql | grep 'Active:'" "Checking PostgreSQL service status"

# Check PostgreSQL version
Invoke-SSHCommand "sudo -u postgres psql -c 'SELECT version();'" "Checking PostgreSQL version"

Write-Host "`n🗄️ Step 3: Create Database and Deploy Schema" -ForegroundColor Yellow

# Create the auth_service database if it doesn't exist
$createDbCommand = @"
sudo -u postgres psql -c "SELECT 1 FROM pg_database WHERE datname = 'auth_service';" | grep -q 1 || sudo -u postgres createdb auth_service
"@

Invoke-SSHCommand $createDbCommand "Creating auth_service database (if not exists)"

# Deploy the schema
$deploySchemaCommand = @"
sudo -u postgres psql -d auth_service -f $remoteSchemaPath
"@

Write-Host "  ▶️ Deploying OAuth 2.0 schema..." -ForegroundColor White
$schemaResult = Invoke-SSHCommand $deploySchemaCommand "Deploying schema to auth_service database"

if ($schemaResult) {
    Write-Host "  ✅ Schema deployed successfully!" -ForegroundColor Green
} else {
    Write-Host "  ❌ Schema deployment failed" -ForegroundColor Red
    exit 1
}

Write-Host "`n🔍 Step 4: Verify Schema Deployment" -ForegroundColor Yellow

# List tables
Invoke-SSHCommand "sudo -u postgres psql -d auth_service -c '\dt'" "Listing created tables"

# Check auth_users table structure
Invoke-SSHCommand "sudo -u postgres psql -d auth_service -c '\d auth_users'" "Checking auth_users table structure"

# Check auth_tokens table structure
Invoke-SSHCommand "sudo -u postgres psql -d auth_service -c '\d auth_tokens'" "Checking auth_tokens table structure"

# Check auth_sessions table structure
Invoke-SSHCommand "sudo -u postgres psql -d auth_service -c '\d auth_sessions'" "Checking auth_sessions table structure"

# Count records in each table
Invoke-SSHCommand "sudo -u postgres psql -d auth_service -c 'SELECT COUNT(*) as user_count FROM auth_users;'" "Counting users (should be 1 test user)"

Write-Host "`n🧪 Step 5: Test Database Connectivity from Auth-Service" -ForegroundColor Yellow

# Test if the auth-service can connect to the database
Invoke-SSHCommand "cd /home/<USER>/auth-service-build/build; ./auth-service --help" "Testing auth-service binary functionality"

Write-Host "`n🧹 Step 6: Cleanup" -ForegroundColor Yellow

# Remove the temporary schema file
Invoke-SSHCommand "rm -f $remoteSchemaPath" "Cleaning up temporary schema file"

Write-Host "`n📊 Step 7: Schema Deployment Summary" -ForegroundColor Cyan
Write-Host "=" * 50 -ForegroundColor Cyan

# Final verification
$verifyCommand = @"
sudo -u postgres psql -d auth_service -c "
SELECT 
    schemaname,
    tablename,
    tableowner
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename LIKE 'auth_%'
ORDER BY tablename;
"
"@

Invoke-SSHCommand $verifyCommand "Final schema verification"

Write-Host "`n✅ Database Schema Deployment Test Complete!" -ForegroundColor Green
Write-Host "=" * 50 -ForegroundColor Green

Write-Host "`n📋 Next Steps:" -ForegroundColor Cyan
Write-Host "1. ✅ Database schema is deployed and verified" -ForegroundColor White
Write-Host "2. 🎯 Ready for Step 2: Configuration Enhancement" -ForegroundColor White
Write-Host "3. 🔧 Update config_manager.hpp and .cpp for OAuth 2.0 settings" -ForegroundColor White
Write-Host "4. 🧪 Test configuration loading with new OAuth 2.0 parameters" -ForegroundColor White

Write-Host "`n🎉 Step 1 Complete: OAuth 2.0 Database Schema Ready!" -ForegroundColor Green
