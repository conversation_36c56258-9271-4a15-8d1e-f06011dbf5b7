#pragma once

#include "database-service/api/api_server.hpp"
#include "database-service/core/connection_manager.hpp"
#include "database-service/security/security_manager.hpp"
#include "database-service/security/credential_store.hpp"
#include <memory>
#include <expected>
#include <string>

// Force file size change to ensure transfer

// Forward declarations
namespace dbservice::service {
    class ApplicationManager;
    class DatabaseInstanceManager;
}

namespace dbservice::security {
    class AuditLogger;
    class RateLimiter;
}

namespace dbservice::api {

/**
 * @class RouteController
 * @brief Manages the registration of API routes
 */
class RouteController {
public:
    /**
     * @brief Constructor
     * @param connectionManager Shared pointer to the ConnectionManager
     * @param securityManager Shared pointer to the SecurityManager
     */
    RouteController(std::shared_ptr<core::ConnectionManager> connectionManager,
                    std::shared_ptr<security::SecurityManager> securityManager);

    /**
     * @brief Registers all API routes with the server
     * @param server The ApiServer instance
     */
    void registerRoutes(ApiServer& server);

private:
    std::shared_ptr<core::ConnectionManager> connectionManager_;
    std::shared_ptr<security::SecurityManager> securityManager_;
    std::shared_ptr<service::ApplicationManager> applicationManager_;
    std::shared_ptr<service::DatabaseInstanceManager> databaseInstanceManager_;
    std::shared_ptr<security::AuditLogger> auditLogger_;
    std::shared_ptr<security::RateLimiter> rateLimiter_;
    std::shared_ptr<security::CredentialStore> credentialStore_;

    /**
     * @brief Ensure database-dependent components are initialized
     * @return true if initialization successful, false otherwise
     */
    bool ensureDatabaseComponentsInitialized();

    // Route handlers
    std::expected<Response, std::string> handleHealthCheck(const ParsedRequest& request);
    std::expected<Response, std::string> handleLogin(const ParsedRequest& request);
    std::expected<Response, std::string> handleLogout(const ParsedRequest& request);
    std::expected<Response, std::string> handleQuery(const ParsedRequest& request);
    std::expected<Response, std::string> handleExecute(const ParsedRequest& request);
    std::expected<Response, std::string> handleTransaction(const ParsedRequest& request);
    std::expected<Response, std::string> handleDatabaseMetrics(const ParsedRequest& request);
    std::expected<Response, std::string> handleStoreCredentials(const ParsedRequest& request);
    std::expected<Response, std::string> handleGetCredentials(const ParsedRequest& request);
    std::expected<Response, std::string> handleRegisterApplication(const ParsedRequest& request);
    std::expected<Response, std::string> handleListApplications(const ParsedRequest& request);

    // Database management handlers
    std::expected<Response, std::string> handleListDatabases(const ParsedRequest& request);
    std::expected<Response, std::string> handleCreateDatabase(const ParsedRequest& request);
    std::expected<Response, std::string> handleDropDatabase(const ParsedRequest& request);

    // User management handlers
    std::expected<Response, std::string> handleCreateUser(const ParsedRequest& request);
    std::expected<Response, std::string> handleListUsers(const ParsedRequest& request);
    std::expected<Response, std::string> handleGetUserProfile(const ParsedRequest& request);
    std::expected<Response, std::string> handleChangePassword(const ParsedRequest& request);
    std::expected<Response, std::string> handleUpdateUserProfile(const ParsedRequest& request);
    std::expected<Response, std::string> handleDeleteUser(const ParsedRequest& request);
    std::expected<Response, std::string> handleActivateUser(const ParsedRequest& request);
    std::expected<Response, std::string> handleDeactivateUser(const ParsedRequest& request);

    // Helper methods
    std::expected<int, std::string> validateAuthentication(const ParsedRequest& request);
    std::string getClientId(const ParsedRequest& request);
    std::string getClientIp(const ParsedRequest& request);
    Response createErrorResponse(int statusCode, const std::string& message, const std::string& details = "");
};

} // namespace dbservice::api
