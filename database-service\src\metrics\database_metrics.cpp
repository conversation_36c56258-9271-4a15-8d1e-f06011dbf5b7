#include "database-service/metrics/database_metrics.hpp"
#include "database-service/utils/logger.hpp"
#include <nlohmann/json.hpp>
#include <format>
#include <limits>
#include <algorithm>

namespace dbservice::metrics {

DatabaseMetrics& DatabaseMetrics::getInstance() {
    static DatabaseMetrics instance;
    return instance;
}

DatabaseMetrics::DatabaseMetrics()
    : activeConnections_(0),
      idleConnections_(0),
      waitingConnections_(0),
      maxConnections_(0),
      connectionWaitTimeMs_(0),
      maxConnectionsReached_(0),
      commitCount_(0),
      rollbackCount_(0),
      totalTransactionTimeMs_(0),
      maxTransactionTimeMs_(0),
      authSuccessCount_(0),
      authFailureCount_(0),
      initialized_(false) {
}

DatabaseMetrics::~DatabaseMetrics() {
}

bool DatabaseMetrics::initialize() {
    if (initialized_) {
        return true;
    }
    
    // Initialize query metrics for common query types
    std::lock_guard<std::mutex> lock(queryMetricsMutex_);
    
    for (const auto& queryType : {"SELECT", "INSERT", "UPDATE", "DELETE", "OTHER"}) {
        // Use operator[] to create the entry, which will default-construct QueryMetrics
        auto& metrics = queryMetrics_[queryType];

        // Initialize the atomic values directly
        metrics.count.store(0);
        metrics.totalTimeMs.store(0.0);
        metrics.minTimeMs.store(std::numeric_limits<double>::max());
        metrics.maxTimeMs.store(0.0);
        metrics.errorCount.store(0);
    }
    
    initialized_ = true;
    utils::Logger::info("Database metrics initialized");
    
    return true;
}

void DatabaseMetrics::recordConnectionPoolMetrics(int activeConnections, int idleConnections, 
                                               int waitingConnections, int maxConnections) {
    activeConnections_ = activeConnections;
    idleConnections_ = idleConnections;
    waitingConnections_ = waitingConnections;
    maxConnections_ = maxConnections;
    
    // Track if we've reached max connections
    if (activeConnections >= maxConnections) {
        maxConnectionsReached_++;
    }
}

void DatabaseMetrics::recordQueryMetric(const std::string& queryType, double executionTimeMs, bool success) {
    std::lock_guard<std::mutex> lock(queryMetricsMutex_);
    
    // Normalize query type
    std::string normalizedType = queryType;
    if (queryMetrics_.find(normalizedType) == queryMetrics_.end()) {
        normalizedType = "OTHER";
    }
    
    auto& metrics = queryMetrics_[normalizedType];
    
    // Update metrics
    metrics.count++;
    metrics.totalTimeMs += executionTimeMs;
    metrics.minTimeMs = std::min(metrics.minTimeMs.load(), executionTimeMs);
    metrics.maxTimeMs = std::max(metrics.maxTimeMs.load(), executionTimeMs);
    
    if (!success) {
        metrics.errorCount++;
    }
}

void DatabaseMetrics::recordTransactionMetric(bool committed, double durationMs) {
    if (committed) {
        commitCount_++;
    } else {
        rollbackCount_++;
    }
    
    totalTransactionTimeMs_ += durationMs;
    maxTransactionTimeMs_ = std::max(maxTransactionTimeMs_.load(), durationMs);
}

void DatabaseMetrics::recordAuthenticationMetric(bool success) {
    if (success) {
        authSuccessCount_++;
    } else {
        authFailureCount_++;
    }
}

nlohmann::json DatabaseMetrics::getConnectionPoolMetrics() const {
    nlohmann::json metrics;
    
    metrics["active_connections"] = activeConnections_.load();
    metrics["idle_connections"] = idleConnections_.load();
    metrics["waiting_connections"] = waitingConnections_.load();
    metrics["max_connections"] = maxConnections_.load();
    metrics["connection_utilization"] = maxConnections_.load() > 0 
        ? static_cast<double>(activeConnections_.load()) / maxConnections_.load() 
        : 0.0;
    metrics["max_connections_reached_count"] = maxConnectionsReached_.load();
    
    return metrics;
}

nlohmann::json DatabaseMetrics::getQueryPerformanceMetrics() const {
    nlohmann::json metrics = nlohmann::json::object();
    
    std::lock_guard<std::mutex> lock(queryMetricsMutex_);
    
    for (const auto& [queryType, queryMetrics] : queryMetrics_) {
        nlohmann::json queryTypeMetrics;
        
        int count = queryMetrics.count.load();
        double totalTimeMs = queryMetrics.totalTimeMs.load();
        
        queryTypeMetrics["count"] = count;
        queryTypeMetrics["error_count"] = queryMetrics.errorCount.load();
        queryTypeMetrics["error_rate"] = count > 0 
            ? static_cast<double>(queryMetrics.errorCount.load()) / count 
            : 0.0;
        
        queryTypeMetrics["min_time_ms"] = count > 0 
            ? queryMetrics.minTimeMs.load() 
            : 0.0;
        
        queryTypeMetrics["max_time_ms"] = queryMetrics.maxTimeMs.load();
        
        queryTypeMetrics["avg_time_ms"] = count > 0 
            ? totalTimeMs / count 
            : 0.0;
        
        metrics[queryType] = queryTypeMetrics;
    }
    
    return metrics;
}

nlohmann::json DatabaseMetrics::getTransactionMetrics() const {
    nlohmann::json metrics;
    
    int commitCount = commitCount_.load();
    int rollbackCount = rollbackCount_.load();
    int totalCount = commitCount + rollbackCount;
    
    metrics["commit_count"] = commitCount;
    metrics["rollback_count"] = rollbackCount;
    metrics["total_count"] = totalCount;
    
    metrics["commit_rate"] = totalCount > 0 
        ? static_cast<double>(commitCount) / totalCount 
        : 0.0;
    
    metrics["avg_transaction_time_ms"] = totalCount > 0 
        ? totalTransactionTimeMs_.load() / totalCount 
        : 0.0;
    
    metrics["max_transaction_time_ms"] = maxTransactionTimeMs_.load();
    
    return metrics;
}

nlohmann::json DatabaseMetrics::getAuthenticationMetrics() const {
    nlohmann::json metrics;
    
    int successCount = authSuccessCount_.load();
    int failureCount = authFailureCount_.load();
    int totalCount = successCount + failureCount;
    
    metrics["success_count"] = successCount;
    metrics["failure_count"] = failureCount;
    metrics["total_count"] = totalCount;
    
    metrics["success_rate"] = totalCount > 0 
        ? static_cast<double>(successCount) / totalCount 
        : 0.0;
    
    return metrics;
}

nlohmann::json DatabaseMetrics::getAllMetrics() const {
    nlohmann::json metrics;
    
    metrics["connection_pool"] = getConnectionPoolMetrics();
    metrics["query_performance"] = getQueryPerformanceMetrics();
    metrics["transactions"] = getTransactionMetrics();
    metrics["authentication"] = getAuthenticationMetrics();
    metrics["timestamp"] = std::chrono::system_clock::to_time_t(std::chrono::system_clock::now());
    
    return metrics;
}

} // namespace dbservice::metrics
