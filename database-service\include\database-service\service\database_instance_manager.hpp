#pragma once

#include <string>
#include <vector>
#include <memory>
#include <expected>
#include <chrono>
#include <nlohmann/json.hpp>

namespace dbservice::core {
    class ConnectionManager;
    class Connection;
}

namespace dbservice::security {
    class CredentialStore;
}

namespace dbservice::service {

// Error types for database instance management
enum class DatabaseError {
    INVALID_HOST,
    INVALID_PORT,
    INVALID_DATABASE_NAME,
    INVALID_CREDENTIALS,
    INSTANCE_NOT_FOUND,
    INSTANCE_ALREADY_EXISTS,
    CONNECTION_FAILED,
    DATABASE_ERROR,
    PERMISSION_DENIED,
    INVALID_PARAMETERS,
    CREDENTIAL_STORAGE_FAILED
};

// Database instance configuration
struct DatabaseConfig {
    std::string name;
    std::string host;
    int port;
    std::string databaseName;
    std::string username;
    std::string password;
    bool useSSL;
    int maxConnections;
    nlohmann::json metadata;
};

// Database instance data structure
struct DatabaseInstance {
    int id;
    std::string name;
    std::string host;
    int port;
    std::string databaseName;
    std::string username;
    bool useSSL;
    int maxConnections;
    std::chrono::system_clock::time_point createdAt;
    std::chrono::system_clock::time_point updatedAt;
    bool active;
    nlohmann::json metadata;
};

/**
 * @brief Manages database instances and their connections
 * 
 * The DatabaseInstanceManager is responsible for:
 * - Registering database instances
 * - Managing database credentials securely
 * - Providing connections to registered databases
 * - Routing queries to appropriate database instances
 * - Managing connection pools per database
 */
class DatabaseInstanceManager {
public:
    /**
     * @brief Constructor
     * @param connectionManager Database connection manager for metadata
     * @param credentialStore Secure credential storage
     */
    DatabaseInstanceManager(
        std::shared_ptr<dbservice::core::ConnectionManager> connectionManager,
        std::shared_ptr<dbservice::security::CredentialStore> credentialStore
    );

    /**
     * @brief Destructor
     */
    ~DatabaseInstanceManager() = default;

    // Disable copy constructor and assignment operator
    DatabaseInstanceManager(const DatabaseInstanceManager&) = delete;
    DatabaseInstanceManager& operator=(const DatabaseInstanceManager&) = delete;

    /**
     * @brief Initialize the database instance manager
     * @return true if initialization successful, false otherwise
     */
    bool initialize();

    /**
     * @brief Register a new database instance
     * @param config Database configuration
     * @return Database instance ID on success, error on failure
     */
    std::expected<int, DatabaseError> registerInstance(const DatabaseConfig& config);

    /**
     * @brief Get database instance by ID
     * @param instanceId Database instance ID
     * @return Database instance data on success, error on failure
     */
    std::expected<DatabaseInstance, DatabaseError> getInstance(int instanceId);

    /**
     * @brief Get database instance by name
     * @param name Database instance name
     * @return Database instance data on success, error on failure
     */
    std::expected<DatabaseInstance, DatabaseError> getInstanceByName(const std::string& name);

    /**
     * @brief List all database instances
     * @return Vector of database instances on success, error on failure
     */
    std::expected<std::vector<DatabaseInstance>, DatabaseError> listInstances();

    /**
     * @brief Get connection for application to specific database instance
     * @param applicationId Application ID
     * @param instanceId Database instance ID
     * @return Database connection on success, error on failure
     */
    std::expected<std::shared_ptr<dbservice::core::Connection>, DatabaseError> getConnection(
        int applicationId, 
        int instanceId
    );

    /**
     * @brief Get connection for application with schema routing
     * @param applicationId Application ID
     * @param schemaName Schema name to route to
     * @return Database connection on success, error on failure
     */
    std::expected<std::shared_ptr<dbservice::core::Connection>, DatabaseError> getConnectionBySchema(
        int applicationId, 
        const std::string& schemaName
    );

    /**
     * @brief Update database instance credentials
     * @param instanceId Database instance ID
     * @param username New username
     * @param password New password
     * @return void on success, error on failure
     */
    std::expected<void, DatabaseError> updateCredentials(
        int instanceId, 
        const std::string& username, 
        const std::string& password
    );

    /**
     * @brief Update database instance configuration
     * @param instanceId Database instance ID
     * @param config New configuration
     * @return void on success, error on failure
     */
    std::expected<void, DatabaseError> updateInstanceConfig(int instanceId, const DatabaseConfig& config);

    /**
     * @brief Test connection to database instance
     * @param instanceId Database instance ID
     * @return true if connection successful, false otherwise
     */
    std::expected<bool, DatabaseError> testConnection(int instanceId);

    /**
     * @brief Deactivate database instance
     * @param instanceId Database instance ID
     * @return void on success, error on failure
     */
    std::expected<void, DatabaseError> deactivateInstance(int instanceId);

    /**
     * @brief Activate database instance
     * @param instanceId Database instance ID
     * @return void on success, error on failure
     */
    std::expected<void, DatabaseError> activateInstance(int instanceId);

    /**
     * @brief Get database instances for an application
     * @param applicationId Application ID
     * @return Vector of database instances on success, error on failure
     */
    std::expected<std::vector<DatabaseInstance>, DatabaseError> getInstancesForApplication(int applicationId);

private:
    std::shared_ptr<dbservice::core::ConnectionManager> connectionManager_;
    std::shared_ptr<dbservice::security::CredentialStore> credentialStore_;
    bool initialized_;

    /**
     * @brief Validate database configuration
     * @param config Database configuration
     * @return true if valid, false otherwise
     */
    bool isValidDatabaseConfig(const DatabaseConfig& config);

    /**
     * @brief Check if database instance name already exists
     * @param name Database instance name
     * @return true if exists, false otherwise
     */
    bool instanceNameExists(const std::string& name);

    /**
     * @brief Store credentials securely
     * @param instanceId Database instance ID
     * @param username Username
     * @param password Password
     * @return true if successful, false otherwise
     */
    bool storeCredentials(int instanceId, const std::string& username, const std::string& password);

    /**
     * @brief Retrieve credentials securely
     * @param instanceId Database instance ID
     * @return Pair of username and password on success, empty on failure
     */
    std::pair<std::string, std::string> getCredentials(int instanceId);

    /**
     * @brief Convert database row to DatabaseInstance object
     * @param row Database row data
     * @return DatabaseInstance object
     */
    DatabaseInstance rowToDatabaseInstance(const std::vector<std::string>& row);

    /**
     * @brief Create connection string for database instance
     * @param instance Database instance
     * @param username Username
     * @param password Password
     * @return Connection string
     */
    std::string createConnectionString(const DatabaseInstance& instance, const std::string& username, const std::string& password);
};

} // namespace dbservice::service
