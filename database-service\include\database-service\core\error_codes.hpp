#pragma once
#include <system_error>
#include <string>

namespace dbservice::core {

/**
 * @enum DatabaseErrc
 * @brief Database error codes
 */
enum class DatabaseErrc {
    Success = 0,
    ConnectionError,
    QueryError,
    TransactionError,
    Timeout,
    InvalidParameter,
    PermissionDenied,
    DatabaseNotFound,
    TableNotFound,
    ConstraintViolation,
    DataCorruption,
    OutOfMemory,
    NetworkError,
    AuthenticationFailed,
    ConfigurationError,
    UnknownError
};

/**
 * @class DatabaseErrorCategory
 * @brief Error category for database errors
 */
class DatabaseErrorCategory : public std::error_category {
public:
    /**
     * @brief Get the name of the error category
     * @return Category name
     */
    const char* name() const noexcept override {
        return "database";
    }

    /**
     * @brief Get the error message for a given error code
     * @param ev Error value
     * @return Error message
     */
    std::string message(int ev) const override {
        switch (static_cast<DatabaseErrc>(ev)) {
            case DatabaseErrc::Success:
                return "Success";
            case DatabaseErrc::ConnectionError:
                return "Database connection error";
            case DatabaseErrc::QueryError:
                return "Database query error";
            case DatabaseErrc::TransactionError:
                return "Database transaction error";
            case DatabaseErrc::Timeout:
                return "Database operation timeout";
            case DatabaseErrc::InvalidParameter:
                return "Invalid parameter provided";
            case DatabaseErrc::PermissionDenied:
                return "Permission denied";
            case DatabaseErrc::DatabaseNotFound:
                return "Database not found";
            case DatabaseErrc::TableNotFound:
                return "Table not found";
            case DatabaseErrc::ConstraintViolation:
                return "Database constraint violation";
            case DatabaseErrc::DataCorruption:
                return "Data corruption detected";
            case DatabaseErrc::OutOfMemory:
                return "Out of memory";
            case DatabaseErrc::NetworkError:
                return "Network error";
            case DatabaseErrc::AuthenticationFailed:
                return "Authentication failed";
            case DatabaseErrc::ConfigurationError:
                return "Configuration error";
            case DatabaseErrc::UnknownError:
            default:
                return "Unknown database error";
        }
    }
};

/**
 * @brief Get the database error category instance
 * @return Database error category
 */
inline const DatabaseErrorCategory& database_error_category() {
    static DatabaseErrorCategory instance;
    return instance;
}

/**
 * @brief Create an error code from a DatabaseErrc
 * @param e Database error code
 * @return std::error_code
 */
inline std::error_code make_error_code(DatabaseErrc e) {
    return std::error_code(static_cast<int>(e), database_error_category());
}

} // namespace dbservice::core

// Enable std::error_code support for DatabaseErrc
namespace std {
template<>
struct is_error_code_enum<dbservice::core::DatabaseErrc> : true_type {};
}
