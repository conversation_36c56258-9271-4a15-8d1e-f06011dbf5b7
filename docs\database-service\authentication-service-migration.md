# Authentication Service Migration Plan

*Migration Documentation*  
*Created: December 24, 2024*  
*Status: Planning Phase*

## Table of Contents
1. [Overview](#overview)
2. [Current State Analysis](#current-state-analysis)
3. [Target Architecture](#target-architecture)
4. [Migration Strategy](#migration-strategy)
5. [Implementation Plan](#implementation-plan)
6. [Technical Specifications](#technical-specifications)
7. [Security Considerations](#security-considerations)
8. [Deployment Strategy](#deployment-strategy)
9. [Testing Strategy](#testing-strategy)
10. [Timeline and Milestones](#timeline-and-milestones)

## Overview

This document outlines the migration plan for creating a dedicated C++23 authentication service using Argon2id password hashing, separate from the existing database-service application. This migration addresses security best practices, scalability concerns, and separation of concerns in the microservices architecture.

### Migration Objectives

- **Security Enhancement**: Implement Argon2id password hashing (OWASP recommended)
- **Service Separation**: Isolate authentication logic from data access operations
- **Scalability**: Enable independent scaling of authentication vs database operations
- **Maintainability**: Simplify updates and security patches for authentication
- **Compliance**: Improve audit trails and security controls

### Key Benefits

- **Modern Security**: Argon2id provides superior protection against GPU/ASIC attacks
- **Microservices Architecture**: Better fault isolation and independent deployment
- **Performance**: Dedicated resources for authentication operations
- **Future-Proof**: Easier to integrate with enterprise SSO and advanced auth features

## Current State Analysis

### Existing Authentication Implementations

| Service | Technology | Password Hashing | Status | Issues |
|---------|------------|------------------|--------|--------|
| Database Service | C++23 | SHA-256 + salt | Active | Basic security, mixed concerns |
| Backend Service | Python | bcrypt | Active | Good but separate ecosystem |
| Git Server Web | C++ | SHA-256 + salt | Active | Outdated hashing method |
| Project Tracker | Python | bcrypt + JWT | Active | Comprehensive but isolated |

### Current Database Service Issues

1. **ConnectionManager Crashes**: Silent failures in database operations
2. **Mixed Responsibilities**: Authentication and data access in same service
3. **Security Limitations**: SHA-256 + salt is less secure than modern alternatives
4. **Maintenance Complexity**: Authentication changes affect database operations

### User Data Distribution

```sql
-- Current user tables across services
database_service.users (id, username, password_hash, salt, role, created_at, last_login)
git_server.users (similar schema)
project_tracker.users (different schema with bcrypt)
```

## Target Architecture

### Microservices Design

```
┌─────────────────────────────────────────────────────────────────┐
│                     Client Applications                        │
├─────────────────┬─────────────────┬─────────────────────────────┤
│   Dashboard UI  │  Git Server UI  │    Logging UI               │
└─────────────────┴─────────────────┴─────────────────────────────┘
         │                 │                     │
         ▼                 ▼                     ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Nginx Reverse Proxy                         │
│                   (SSL Termination)                            │
├─────────────────────────────────────────────────────────────────┤
│  /auth-api/*  →  Auth Service (8082)                          │
│  /database-api/* → Database Service (8081)                     │
└─────────────────────────────────────────────────────────────────┘
         │                                │
         ▼                                ▼
┌─────────────────┐              ┌─────────────────┐
│ Auth Service    │              │ Database Service│
│ (C++23)         │              │ (C++23)         │
│ Port: 8082      │              │ Port: 8081      │
│                 │              │                 │
│ • User Mgmt     │              │ • Data Access   │
│ • Authentication│              │ • Query Engine  │
│ • JWT Tokens    │              │ • Schema Mgmt   │
│ • Argon2id      │              │ • Multi-DB      │
│ • Rate Limiting │              │ • Transactions  │
└─────────────────┘              └─────────────────┘
         │                                │
         ▼                                ▼
┌─────────────────┐              ┌─────────────────┐
│ Auth Database   │              │ Application     │
│ (PostgreSQL)    │              │ Databases       │
│                 │              │ (PostgreSQL)    │
│ • users         │              │ • git_repo      │
│ • roles         │              │ • logging       │
│ • sessions      │              │ • dashboard     │
│ • audit_log     │              │ • metrics       │
└─────────────────┘              └─────────────────┘
```

### Service Responsibilities

**Authentication Service (New):**
- User registration and management
- Password authentication with Argon2id
- JWT token generation and validation
- Session management
- Rate limiting and brute force protection
- Audit logging for security events
- Role and permission management

**Database Service (Updated):**
- Data access and query operations
- Multi-database connection management
- Schema management and migrations
- Transaction handling
- Application-specific data operations
- JWT token validation (not generation)

## Migration Strategy

### Phase-Based Approach

#### Phase 1: Authentication Service Development (4-6 weeks)
1. **Project Setup**
   - Create auth-service directory structure
   - Configure C++23 build environment
   - Set up Argon2 library integration
   - Implement basic HTTP server

2. **Core Authentication Features**
   - User registration and login endpoints
   - Argon2id password hashing implementation
   - JWT token generation and validation
   - Basic rate limiting

3. **Database Integration**
   - Create auth database schema
   - Implement user data access layer
   - Add audit logging capabilities

#### Phase 2: Service Integration (2-3 weeks)
1. **Nginx Configuration**
   - Add auth-service routing
   - Configure SSL termination
   - Set up health checks

2. **Database Service Updates**
   - Remove authentication endpoints
   - Add JWT validation middleware
   - Update API documentation

3. **Testing and Validation**
   - Integration testing between services
   - Performance testing
   - Security testing

#### Phase 3: UI Migration (3-4 weeks)
1. **Update Client Applications**
   - Modify login/registration flows
   - Update API endpoints
   - Handle new authentication flow

2. **User Data Migration**
   - Migrate existing users to auth service
   - Convert password hashes to Argon2id
   - Preserve user roles and permissions

#### Phase 4: Production Deployment (1-2 weeks)
1. **Deployment Preparation**
   - Production configuration
   - Monitoring setup
   - Backup procedures

2. **Go-Live**
   - Blue-green deployment
   - User acceptance testing
   - Performance monitoring

## Technical Specifications

### Authentication Service Architecture

#### Directory Structure
```
auth-service/
├── CMakeLists.txt
├── config/
│   ├── development.json
│   ├── production.json
│   └── test.json
├── include/
│   └── auth-service/
│       ├── api/
│       │   ├── auth_controller.hpp
│       │   ├── user_controller.hpp
│       │   └── middleware/
│       ├── core/
│       │   ├── auth_manager.hpp
│       │   ├── password_manager.hpp
│       │   ├── token_manager.hpp
│       │   └── session_manager.hpp
│       ├── database/
│       │   ├── connection_manager.hpp
│       │   ├── user_repository.hpp
│       │   └── audit_repository.hpp
│       ├── security/
│       │   ├── argon2_hasher.hpp
│       │   ├── jwt_handler.hpp
│       │   ├── rate_limiter.hpp
│       │   └── crypto_utils.hpp
│       └── utils/
│           ├── config_manager.hpp
│           ├── logger.hpp
│           └── http_server.hpp
├── src/
│   ├── api/
│   ├── core/
│   ├── database/
│   ├── security/
│   ├── utils/
│   └── main.cpp
├── sql/
│   ├── schema/
│   │   ├── 001_create_users_table.sql
│   │   ├── 002_create_roles_table.sql
│   │   ├── 003_create_sessions_table.sql
│   │   └── 004_create_audit_log_table.sql
│   └── migrations/
├── tests/
│   ├── unit/
│   ├── integration/
│   └── performance/
└── deployment/
    ├── systemd/
    ├── nginx/
    └── scripts/
```

#### Core Dependencies
```cmake
# CMakeLists.txt dependencies
find_package(PkgConfig REQUIRED)
find_package(OpenSSL REQUIRED)
find_package(PostgreSQL REQUIRED)

# Argon2 library
pkg_check_modules(ARGON2 REQUIRED libargon2)

# HTTP server library (crow or similar)
find_package(Crow REQUIRED)

# JSON library
find_package(nlohmann_json REQUIRED)

# JWT library
find_package(jwt-cpp REQUIRED)
```

### Argon2id Implementation

#### Password Hashing Configuration
```cpp
// Argon2id parameters (production recommended)
struct Argon2Config {
    uint32_t time_cost = 3;      // Number of iterations
    uint32_t memory_cost = 65536; // Memory usage in KB (64MB)
    uint32_t parallelism = 4;     // Number of threads
    uint32_t hash_length = 32;    // Output hash length
    uint32_t salt_length = 16;    // Salt length
};
```

#### Security Features
- **Memory-hard**: Resistant to GPU/ASIC attacks
- **Configurable parameters**: Adjustable based on hardware capabilities
- **Salt generation**: Cryptographically secure random salts
- **Constant-time verification**: Prevents timing attacks

### Database Schema

#### Users Table
```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(255) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    salt BYTEA NOT NULL,
    argon2_params JSONB NOT NULL,
    role_id INTEGER REFERENCES roles(id),
    is_active BOOLEAN DEFAULT true,
    is_verified BOOLEAN DEFAULT false,
    failed_login_attempts INTEGER DEFAULT 0,
    locked_until TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL
);
```

#### Sessions Table
```sql
CREATE TABLE sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    access_token_hash TEXT NOT NULL,
    refresh_token_hash TEXT NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_used TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address INET,
    user_agent TEXT
);
```

#### Audit Log Table
```sql
CREATE TABLE audit_log (
    id BIGSERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    event_type VARCHAR(50) NOT NULL,
    action VARCHAR(100) NOT NULL,
    resource VARCHAR(100),
    ip_address INET,
    user_agent TEXT,
    success BOOLEAN NOT NULL,
    details JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Security Considerations

### Password Security
- **Argon2id Algorithm**: Memory-hard, GPU-resistant
- **Parameter Tuning**: Adjust based on server capabilities
- **Salt Management**: Unique salt per password
- **Migration Strategy**: Gradual conversion from existing hashes

### Token Security
- **JWT Implementation**: RS256 or HS256 with secure secrets
- **Token Rotation**: Refresh token mechanism
- **Expiration**: Short-lived access tokens (15-30 minutes)
- **Revocation**: Session invalidation capabilities

### Rate Limiting
- **Login Attempts**: Progressive delays and account lockout
- **API Endpoints**: Request rate limiting per IP/user
- **Brute Force Protection**: Automatic IP blocking
- **Monitoring**: Real-time attack detection

### Audit and Compliance
- **Comprehensive Logging**: All authentication events
- **GDPR Compliance**: Data minimization and user rights
- **SOX Compliance**: Immutable audit trails
- **Security Monitoring**: Real-time alerting

## Deployment Strategy

### Infrastructure Requirements
- **Server Resources**: Dedicated CPU/memory for auth service
- **Database**: Separate PostgreSQL schema or instance
- **Monitoring**: Health checks and performance metrics
- **Backup**: Regular database backups and recovery procedures

### Configuration Management
- **Environment-specific configs**: Development, staging, production
- **Secret management**: Secure storage of JWT secrets and DB credentials
- **Feature flags**: Gradual rollout capabilities
- **Monitoring integration**: Prometheus/Grafana metrics

### Rollback Plan
- **Service versioning**: Ability to rollback to previous version
- **Database migrations**: Reversible schema changes
- **Configuration rollback**: Quick reversion to previous settings
- **User data preservation**: No data loss during rollback

## Testing Strategy

### Unit Testing
- **Password hashing**: Argon2id implementation
- **JWT handling**: Token generation and validation
- **Rate limiting**: Algorithm correctness
- **Database operations**: Repository layer testing

### Integration Testing
- **Service communication**: Auth ↔ Database service
- **Database connectivity**: PostgreSQL integration
- **API endpoints**: Full request/response cycle
- **Security features**: Authentication flow testing

### Performance Testing
- **Load testing**: Concurrent authentication requests
- **Stress testing**: High-volume user registration
- **Memory usage**: Argon2id parameter optimization
- **Response times**: Authentication latency measurement

### Security Testing
- **Penetration testing**: Authentication bypass attempts
- **Brute force testing**: Rate limiting effectiveness
- **Token security**: JWT manipulation attempts
- **SQL injection**: Database query security

## Timeline and Milestones

### Phase 1: Development (4-6 weeks)
- **Week 1-2**: Project setup and core authentication
- **Week 3-4**: Database integration and JWT implementation
- **Week 5-6**: Security features and testing

### Phase 2: Integration (2-3 weeks)
- **Week 7-8**: Service integration and nginx configuration
- **Week 9**: Testing and validation

### Phase 3: Migration (3-4 weeks)
- **Week 10-11**: UI updates and user data migration
- **Week 12-13**: End-to-end testing and documentation

### Phase 4: Deployment (1-2 weeks)
- **Week 14**: Production deployment and monitoring
- **Week 15**: Go-live and post-deployment support

### Success Criteria
- ✅ All authentication operations use Argon2id
- ✅ Zero downtime during migration
- ✅ Performance meets or exceeds current system
- ✅ Security audit passes with no critical issues
- ✅ All UI applications successfully integrated

## Risk Management

### Technical Risks

| Risk | Impact | Probability | Mitigation Strategy |
|------|--------|-------------|-------------------|
| Argon2 library compatibility | High | Low | Test on target Ubuntu 24.04, fallback to bcrypt |
| Performance degradation | Medium | Medium | Load testing, parameter tuning |
| Database migration issues | High | Low | Comprehensive backup, rollback procedures |
| Service integration failures | Medium | Medium | Extensive integration testing |
| User data corruption | Critical | Very Low | Multiple backups, validation checks |

### Operational Risks

| Risk | Impact | Probability | Mitigation Strategy |
|------|--------|-------------|-------------------|
| Extended downtime | High | Low | Blue-green deployment, rollback plan |
| User lockout | Medium | Medium | Emergency admin access, unlock procedures |
| Performance issues | Medium | Medium | Monitoring, auto-scaling |
| Security vulnerabilities | Critical | Low | Security audit, penetration testing |

### Business Risks

| Risk | Impact | Probability | Mitigation Strategy |
|------|--------|-------------|-------------------|
| User experience degradation | Medium | Low | UX testing, gradual rollout |
| Compliance violations | High | Very Low | Legal review, audit trail preservation |
| Development delays | Medium | Medium | Buffer time, parallel development |

## Implementation Guidelines

### Development Environment Setup

#### Prerequisites
```bash
# Ubuntu 24.04 dependencies
sudo apt update
sudo apt install -y build-essential cmake pkg-config
sudo apt install -y libssl-dev libpq-dev libargon2-dev
sudo apt install -y libnlohmann-json3-dev

# Install Crow HTTP framework
git clone https://github.com/CrowCpp/Crow.git
cd Crow && mkdir build && cd build
cmake .. -DCROW_BUILD_EXAMPLES=OFF
make -j4 && sudo make install
```

#### Project Initialization
```bash
# Create auth-service project
mkdir -p /opt/auth-service
cd /opt/auth-service

# Initialize CMake project
cmake -B build -S . -DCMAKE_BUILD_TYPE=Release
cmake --build build --parallel 4
```

### Code Implementation Examples

#### Argon2id Password Hasher
```cpp
// include/auth-service/security/argon2_hasher.hpp
#pragma once
#include <string>
#include <vector>
#include <argon2.h>

class Argon2Hasher {
public:
    struct Config {
        uint32_t time_cost = 3;
        uint32_t memory_cost = 65536; // 64MB
        uint32_t parallelism = 4;
        uint32_t hash_length = 32;
        uint32_t salt_length = 16;
    };

    explicit Argon2Hasher(const Config& config = {});

    std::string hashPassword(const std::string& password);
    bool verifyPassword(const std::string& password, const std::string& hash);

private:
    Config config_;
    std::vector<uint8_t> generateSalt();
    std::string encodeHash(const std::vector<uint8_t>& salt,
                          const std::vector<uint8_t>& hash);
    bool decodeHash(const std::string& encoded,
                   std::vector<uint8_t>& salt,
                   std::vector<uint8_t>& hash);
};
```

#### JWT Token Manager
```cpp
// include/auth-service/core/token_manager.hpp
#pragma once
#include <string>
#include <chrono>
#include <jwt-cpp/jwt.h>

class TokenManager {
public:
    struct TokenPair {
        std::string access_token;
        std::string refresh_token;
        std::chrono::system_clock::time_point expires_at;
    };

    explicit TokenManager(const std::string& secret);

    TokenPair generateTokens(int user_id, const std::string& role);
    bool validateAccessToken(const std::string& token);
    TokenPair refreshTokens(const std::string& refresh_token);
    void revokeToken(const std::string& token);

private:
    std::string secret_;
    std::chrono::minutes access_token_lifetime_{30};
    std::chrono::hours refresh_token_lifetime_{24 * 7}; // 7 days
};
```

### Database Migration Scripts

#### User Data Migration
```sql
-- Migration script: migrate_users_to_argon2.sql
-- Migrate existing users from SHA-256 to Argon2id

BEGIN;

-- Create temporary table for migration
CREATE TEMP TABLE user_migration_log (
    user_id INTEGER,
    old_hash TEXT,
    new_hash TEXT,
    migrated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Function to migrate user passwords
CREATE OR REPLACE FUNCTION migrate_user_password(
    p_user_id INTEGER,
    p_username TEXT,
    p_old_hash TEXT,
    p_salt TEXT
) RETURNS BOOLEAN AS $$
DECLARE
    v_temp_password TEXT;
    v_new_hash TEXT;
BEGIN
    -- For migration, we'll need users to reset passwords
    -- or use a temporary migration strategy

    -- Option 1: Force password reset
    UPDATE auth_service.users
    SET password_hash = 'MIGRATION_REQUIRED',
        requires_password_reset = TRUE
    WHERE id = p_user_id;

    -- Log migration
    INSERT INTO user_migration_log (user_id, old_hash, new_hash)
    VALUES (p_user_id, p_old_hash, 'MIGRATION_REQUIRED');

    RETURN TRUE;
EXCEPTION
    WHEN OTHERS THEN
        RETURN FALSE;
END;
$$ LANGUAGE plpgsql;

-- Migrate all users
DO $$
DECLARE
    user_record RECORD;
BEGIN
    FOR user_record IN
        SELECT id, username, password_hash, salt
        FROM database_service.users
    LOOP
        PERFORM migrate_user_password(
            user_record.id,
            user_record.username,
            user_record.password_hash,
            user_record.salt
        );
    END LOOP;
END $$;

COMMIT;
```

### Monitoring and Alerting

#### Prometheus Metrics
```cpp
// Metrics collection for auth service
class AuthMetrics {
public:
    static void recordLogin(bool success, const std::string& method);
    static void recordPasswordHash(std::chrono::milliseconds duration);
    static void recordTokenGeneration(std::chrono::milliseconds duration);
    static void recordRateLimitHit(const std::string& endpoint);

private:
    // Prometheus metric definitions
    static prometheus::Counter& login_attempts_total_;
    static prometheus::Histogram& password_hash_duration_;
    static prometheus::Histogram& token_generation_duration_;
    static prometheus::Counter& rate_limit_hits_total_;
};
```

#### Health Check Endpoint
```cpp
// Health check implementation
crow::response healthCheck() {
    nlohmann::json health;
    health["status"] = "healthy";
    health["timestamp"] = std::chrono::system_clock::now();
    health["version"] = AUTH_SERVICE_VERSION;

    // Check database connectivity
    try {
        auto conn = db_pool_->getConnection();
        conn->execute("SELECT 1");
        health["database"] = "connected";
    } catch (const std::exception& e) {
        health["database"] = "disconnected";
        health["status"] = "unhealthy";
    }

    // Check Argon2 functionality
    try {
        Argon2Hasher hasher;
        hasher.hashPassword("test");
        health["argon2"] = "functional";
    } catch (const std::exception& e) {
        health["argon2"] = "error";
        health["status"] = "unhealthy";
    }

    int status_code = (health["status"] == "healthy") ? 200 : 503;
    return crow::response(status_code, health.dump());
}
```

## Deployment Automation

### PowerShell Deployment Script Integration
```powershell
# Add to existing deploy-database-service-modular.ps1

function Deploy-AuthService {
    param(
        [string]$ServerHost = "git.chcit.org",
        [string]$Username = "btaylor-admin",
        [string]$KeyPath = "C:\Users\<USER>\.ssh\id_rsa"
    )

    Write-Host "Deploying Authentication Service..." -ForegroundColor Green

    # Copy source files
    scp -i $KeyPath -r "auth-service/*" "${Username}@${ServerHost}:/home/<USER>/auth-service-build/"

    # Build and deploy
    ssh -i $KeyPath "${Username}@${ServerHost}" @"
        cd /home/<USER>/auth-service-build
        mkdir -p build
        cd build
        cmake .. -DCMAKE_BUILD_TYPE=Release
        make -j4

        # Stop existing service
        sudo systemctl stop auth-service 2>/dev/null || true

        # Install binary
        sudo mkdir -p /opt/auth-service/bin
        sudo cp bin/auth-service /opt/auth-service/bin/

        # Install configuration
        sudo cp ../config/production.json /opt/auth-service/config.json

        # Install systemd service
        sudo cp ../deployment/systemd/auth-service.service /etc/systemd/system/
        sudo systemctl daemon-reload
        sudo systemctl enable auth-service
        sudo systemctl start auth-service

        # Verify deployment
        sleep 3
        curl -f http://localhost:8082/api/health || echo "Health check failed"
"@

    Write-Host "Authentication Service deployed successfully!" -ForegroundColor Green
}

# Add menu option
function Show-AuthServiceMenu {
    Write-Host "`n=== Authentication Service Management ===" -ForegroundColor Cyan
    Write-Host "15. Deploy Authentication Service"
    Write-Host "16. Check Authentication Service Status"
    Write-Host "17. View Authentication Service Logs"
    Write-Host "18. Restart Authentication Service"
}
```

### Nginx Configuration Update
```nginx
# Add to existing nginx configuration
upstream auth_service {
    server 127.0.0.1:8082;
    keepalive 32;
}

# Authentication service location
location /auth-api/ {
    # Rate limiting for auth endpoints
    limit_req zone=auth burst=10 nodelay;

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";

    # Proxy configuration
    proxy_pass http://auth_service/api/;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection 'upgrade';
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_cache_bypass $http_upgrade;

    # Timeouts
    proxy_connect_timeout 5s;
    proxy_send_timeout 30s;
    proxy_read_timeout 30s;
}

# Rate limiting zones for auth
limit_req_zone $binary_remote_addr zone=auth:10m rate=5r/s;
limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;

# Specific rate limiting for login endpoint
location /auth-api/auth/login {
    limit_req zone=login burst=3 nodelay;
    proxy_pass http://auth_service/api/auth/login;
    # ... other proxy settings
}
```

## Post-Migration Validation

### Validation Checklist
- [ ] All users can authenticate with existing credentials
- [ ] New user registration works correctly
- [ ] Password reset functionality operational
- [ ] JWT tokens generated and validated properly
- [ ] Rate limiting prevents brute force attacks
- [ ] Audit logging captures all events
- [ ] Performance meets baseline requirements
- [ ] Security scan passes without critical issues
- [ ] Backup and recovery procedures tested
- [ ] Monitoring and alerting functional

### Performance Benchmarks
```bash
# Authentication performance testing
ab -n 1000 -c 10 -H "Content-Type: application/json" \
   -p login_payload.json https://db.chcit.org/auth-api/auth/login

# Expected results:
# - 95th percentile < 500ms
# - Throughput > 100 req/sec
# - Zero failed requests
```

This comprehensive migration plan provides a detailed roadmap for implementing a secure, scalable authentication service using C++23 and Argon2id, ensuring a smooth transition from the current mixed authentication implementations to a unified, modern solution.
