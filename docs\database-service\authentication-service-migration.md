# Authentication Service Migration Plan

*Migration Documentation*  
*Created: December 24, 2024*  
*Status: Planning Phase*

## Table of Contents
1. [Overview](#overview)
2. [Current State Analysis](#current-state-analysis)
3. [Target Architecture](#target-architecture)
4. [Migration Strategy](#migration-strategy)
5. [Implementation Plan](#implementation-plan)
6. [Technical Specifications](#technical-specifications)
7. [Security Considerations](#security-considerations)
8. [Deployment Strategy](#deployment-strategy)
9. [Testing Strategy](#testing-strategy)
10. [Timeline and Milestones](#timeline-and-milestones)

## Overview

This document outlines the migration plan for creating a dedicated C++23 authentication service using Argon2id password hashing, separate from the existing database-service application. This migration addresses security best practices, scalability concerns, and separation of concerns in the microservices architecture.

### Migration Objectives

- **Security Enhancement**: Implement Argon2id password hashing (OWASP recommended)
- **Service Separation**: Isolate authentication logic from data access operations
- **Scalability**: Enable independent scaling of authentication vs database operations
- **Maintainability**: Simplify updates and security patches for authentication
- **Compliance**: Improve audit trails and security controls
- **Multi-Project Reusability**: Design for deployment across different projects and environments
- **Multi-Database Support**: Support multiple database instances and schemas per deployment
- **Multi-Server Architecture**: Enable distributed deployment across different servers and networks

### Key Benefits

- **Modern Security**: Argon2id provides superior protection against GPU/ASIC attacks
- **Microservices Architecture**: Better fault isolation and independent deployment
- **Performance**: Dedicated resources for authentication operations
- **Future-Proof**: Easier to integrate with enterprise SSO and advanced auth features
- **Project Portability**: Easy deployment to new projects with minimal configuration changes
- **Database Flexibility**: Support for multiple PostgreSQL instances, schemas, and connection pools
- **Network Agnostic**: Configurable for different IP addresses, domains, and network topologies
- **Environment Isolation**: Clean separation between development, staging, and production deployments

## Current State Analysis

### Existing Authentication Implementations

| Service | Technology | Password Hashing | Status | Issues |
|---------|------------|------------------|--------|--------|
| Database Service | C++23 | SHA-256 + salt | Active | Basic security, mixed concerns |
| Backend Service | Python | bcrypt | Active | Good but separate ecosystem |
| Git Server Web | C++ | SHA-256 + salt | Active | Outdated hashing method |
| Project Tracker | Python | bcrypt + JWT | Active | Comprehensive but isolated |

### Current Database Service Issues

1. **ConnectionManager Crashes**: Silent failures in database operations
2. **Mixed Responsibilities**: Authentication and data access in same service
3. **Security Limitations**: SHA-256 + salt is less secure than modern alternatives
4. **Maintenance Complexity**: Authentication changes affect database operations

### User Data Distribution

```sql
-- Current user tables across services
database_service.users (id, username, password_hash, salt, role, created_at, last_login)
git_server.users (similar schema)
project_tracker.users (different schema with bcrypt)
```

## Target Architecture

### Multi-Project Microservices Architecture

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                          Project A (git.chcit.org)                             │
├─────────────────┬─────────────────┬─────────────────────────────────────────────┤
│   Dashboard UI  │  Git Server UI  │    Logging UI                               │
└─────────────────┴─────────────────┴─────────────────────────────────────────────┘
         │                 │                     │
         ▼                 ▼                     ▼
┌─────────────────────────────────────────────────────────────────────────────────┐
│                    Nginx Reverse Proxy (git.chcit.org)                         │
│                         (SSL Termination)                                      │
├─────────────────────────────────────────────────────────────────────────────────┤
│  /auth-api/*     →  Auth Service (127.0.0.1:8082)                             │
│  /database-api/* →  Database Service (127.0.0.1:8081)                         │
└─────────────────────────────────────────────────────────────────────────────────┘
         │                                │
         ▼                                ▼
┌─────────────────┐              ┌─────────────────┐
│ Auth Service    │              │ Database Service│
│ (C++23)         │◄────────────►│ (C++23)         │
│ Port: 8082      │   JWT Auth   │ Port: 8081      │
│                 │              │                 │
│ • Multi-Project │              │ • Multi-DB Pool │
│ • User Mgmt     │              │ • Query Engine  │
│ • JWT Tokens    │              │ • Schema Mgmt   │
│ • Argon2id      │              │ • App Isolation │
│ • Rate Limiting │              │ • Transactions  │
│ • Project Roles │              │ • Connection Mgr│
└─────────────────┘              └─────────────────┘
         │                                │
         ▼                                ▼
┌─────────────────┐              ┌─────────────────┐
│ Auth Database   │              │ Application DBs │
│ (PostgreSQL)    │              │ (PostgreSQL)    │
│                 │              │                 │
│ • users         │              │ • project_a_git │
│ • projects      │              │ • project_a_log │
│ • roles         │              │ • project_a_dash│
│ • sessions      │              │ • project_a_meta│
│ • audit_log     │              │ • project_b_*   │
│ • permissions   │              │ • project_c_*   │
└─────────────────┘              └─────────────────┘

┌─────────────────────────────────────────────────────────────────────────────────┐
│                          Project B (project-b.example.com)                     │
├─────────────────┬─────────────────┬─────────────────────────────────────────────┤
│   Custom UI     │  Admin Panel    │    Analytics UI                             │
└─────────────────┴─────────────────┴─────────────────────────────────────────────┘
         │                 │                     │
         ▼                 ▼                     ▼
┌─────────────────────────────────────────────────────────────────────────────────┐
│                Nginx Reverse Proxy (project-b.example.com)                     │
├─────────────────────────────────────────────────────────────────────────────────┤
│  /auth-api/*     →  Auth Service (10.0.1.100:8082)                            │
│  /database-api/* →  Database Service (10.0.1.101:8081)                        │
└─────────────────────────────────────────────────────────────────────────────────┘
         │                                │
         ▼                                ▼
┌─────────────────┐              ┌─────────────────┐
│ Auth Service    │              │ Database Service│
│ (Same Binary)   │◄────────────►│ (Same Binary)   │
│ Different Config│   JWT Auth   │ Different Config│
│                 │              │                 │
│ • Project B     │              │ • Project B DBs │
│ • Custom Roles  │              │ • Custom Schema │
│ • Local Users   │              │ • Local Data    │
└─────────────────┘              └─────────────────┘
```

### Multi-Server Deployment Scenarios

**Scenario 1: Single Server (Current)**
- Auth Service: 127.0.0.1:8082
- Database Service: 127.0.0.1:8081
- PostgreSQL: localhost:5432

**Scenario 2: Distributed Services**
- Auth Service: auth.company.com:8082
- Database Service: db.company.com:8081
- PostgreSQL Cluster: db1.company.com:5432, db2.company.com:5432

**Scenario 3: Multi-Project Hosting**
- Project A: git.chcit.org (Auth: 8082, DB: 8081)
- Project B: project-b.example.com (Auth: 8082, DB: 8081)
- Project C: internal.company.com (Auth: 8082, DB: 8081)

### Service Responsibilities

**Authentication Service (New - Multi-Project):**
- **User Management**: Registration, profiles, and account lifecycle across projects
- **Project-Aware Authentication**: Users can belong to multiple projects with different roles
- **Password Security**: Argon2id hashing with configurable parameters per deployment
- **JWT Token Management**: Project-scoped tokens with role-based claims
- **Session Management**: Multi-project session handling and cross-project SSO
- **Rate Limiting**: Configurable limits per project and deployment environment
- **Audit Logging**: Security events with project context and compliance tracking
- **Role & Permission Management**: Hierarchical roles across projects and organizations
- **Multi-Tenant Support**: Isolated user spaces for different projects/organizations

**Database Service (Updated - Multi-Application):**
- **Multi-Database Access**: Connection pools for multiple PostgreSQL instances
- **Application Isolation**: Separate schemas and connection contexts per application
- **Query Engine**: Secure, parameterized query execution across multiple databases
- **Schema Management**: Database migrations and schema versioning per application
- **Transaction Coordination**: Cross-database transaction support where needed
- **Connection Management**: Efficient pooling and load balancing across database instances
- **JWT Validation**: Token verification with project and application context
- **Data Access Control**: Application-level permissions and data isolation
- **Performance Monitoring**: Per-application and per-database performance metrics

## Migration Strategy

### Phase-Based Approach

#### Phase 1: Authentication Service Development (4-6 weeks)
1. **Project Setup**
   - Create auth-service directory structure
   - Configure C++23 build environment
   - Set up Argon2 library integration
   - Implement basic HTTP server

2. **Core Authentication Features**
   - User registration and login endpoints
   - Argon2id password hashing implementation
   - JWT token generation and validation
   - Basic rate limiting

3. **Database Integration**
   - Create auth database schema
   - Implement user data access layer
   - Add audit logging capabilities

#### Phase 2: Service Integration (2-3 weeks)
1. **Nginx Configuration**
   - Add auth-service routing
   - Configure SSL termination
   - Set up health checks

2. **Database Service Updates**
   - Remove authentication endpoints
   - Add JWT validation middleware
   - Update API documentation

3. **Testing and Validation**
   - Integration testing between services
   - Performance testing
   - Security testing

#### Phase 3: UI Migration (3-4 weeks)
1. **Update Client Applications**
   - Modify login/registration flows
   - Update API endpoints
   - Handle new authentication flow

2. **User Data Migration**
   - Migrate existing users to auth service
   - Convert password hashes to Argon2id
   - Preserve user roles and permissions

#### Phase 4: Production Deployment (1-2 weeks)
1. **Deployment Preparation**
   - Production configuration
   - Monitoring setup
   - Backup procedures

2. **Go-Live**
   - Blue-green deployment
   - User acceptance testing
   - Performance monitoring

## Technical Specifications

### Authentication Service Architecture

#### Directory Structure
```
auth-service/
├── CMakeLists.txt
├── config/
│   ├── templates/
│   │   ├── auth-service-template.json
│   │   └── database-service-template.json
│   ├── environments/
│   │   ├── development.json
│   │   ├── staging.json
│   │   ├── production.json
│   │   └── test.json
│   └── projects/
│       ├── project-tracker.json
│       ├── git-dashboard.json
│       └── custom-project.json
├── include/
│   └── auth-service/
│       ├── api/
│       │   ├── auth_controller.hpp
│       │   ├── user_controller.hpp
│       │   └── middleware/
│       ├── core/
│       │   ├── auth_manager.hpp
│       │   ├── password_manager.hpp
│       │   ├── token_manager.hpp
│       │   └── session_manager.hpp
│       ├── database/
│       │   ├── connection_manager.hpp
│       │   ├── user_repository.hpp
│       │   └── audit_repository.hpp
│       ├── security/
│       │   ├── argon2_hasher.hpp
│       │   ├── jwt_handler.hpp
│       │   ├── rate_limiter.hpp
│       │   └── crypto_utils.hpp
│       └── utils/
│           ├── config_manager.hpp
│           ├── logger.hpp
│           └── http_server.hpp
├── src/
│   ├── api/
│   ├── core/
│   ├── database/
│   ├── security/
│   ├── utils/
│   └── main.cpp
├── sql/
│   ├── schema/
│   │   ├── 001_create_projects_table.sql
│   │   ├── 002_create_users_table.sql
│   │   ├── 003_create_roles_table.sql
│   │   ├── 004_create_project_users_table.sql
│   │   ├── 005_create_sessions_table.sql
│   │   └── 006_create_audit_log_table.sql
│   ├── migrations/
│   │   ├── auth-service/
│   │   └── database-service/
│   └── templates/
│       ├── project-setup.sql
│       └── user-migration.sql
├── tests/
│   ├── unit/
│   ├── integration/
│   └── performance/
└── deployment/
    ├── systemd/
    │   ├── auth-service.service
    │   └── auth-service@.service (multi-instance)
    ├── nginx/
    │   ├── auth-service.conf
    │   └── multi-project-template.conf
    ├── scripts/
    │   ├── deploy-single-server.sh
    │   ├── deploy-distributed.sh
    │   ├── setup-new-project.sh
    │   └── migrate-project.sh
    └── docker/
        ├── Dockerfile.auth-service
        ├── Dockerfile.database-service
        └── docker-compose.multi-project.yml
```

#### Core Dependencies
```cmake
# CMakeLists.txt dependencies
find_package(PkgConfig REQUIRED)
find_package(OpenSSL REQUIRED)
find_package(PostgreSQL REQUIRED)

# Argon2 library
pkg_check_modules(ARGON2 REQUIRED libargon2)

# HTTP server library (crow or similar)
find_package(Crow REQUIRED)

# JSON library
find_package(nlohmann_json REQUIRED)

# JWT library
find_package(jwt-cpp REQUIRED)
```

### Argon2id Implementation

#### Password Hashing Configuration
```cpp
// Argon2id parameters (production recommended)
struct Argon2Config {
    uint32_t time_cost = 3;      // Number of iterations
    uint32_t memory_cost = 65536; // Memory usage in KB (64MB)
    uint32_t parallelism = 4;     // Number of threads
    uint32_t hash_length = 32;    // Output hash length
    uint32_t salt_length = 16;    // Salt length
};
```

#### Security Features
- **Memory-hard**: Resistant to GPU/ASIC attacks
- **Configurable parameters**: Adjustable based on hardware capabilities
- **Salt generation**: Cryptographically secure random salts
- **Constant-time verification**: Prevents timing attacks

## Multi-Project Configuration Architecture

### Configuration Templates

#### Auth Service Configuration Template
```json
{
  "project": {
    "id": "{{PROJECT_ID}}",
    "name": "{{PROJECT_NAME}}",
    "domain": "{{PROJECT_DOMAIN}}",
    "environment": "{{ENVIRONMENT}}"
  },
  "server": {
    "host": "{{AUTH_HOST}}",
    "port": {{AUTH_PORT}},
    "ssl": {
      "enabled": {{SSL_ENABLED}},
      "cert_path": "{{SSL_CERT_PATH}}",
      "key_path": "{{SSL_KEY_PATH}}"
    }
  },
  "database": {
    "auth_db": {
      "host": "{{AUTH_DB_HOST}}",
      "port": {{AUTH_DB_PORT}},
      "database": "{{AUTH_DB_NAME}}",
      "username": "{{AUTH_DB_USER}}",
      "password": "{{AUTH_DB_PASSWORD}}",
      "schema": "auth_{{PROJECT_ID}}"
    }
  },
  "security": {
    "jwt": {
      "secret": "{{JWT_SECRET}}",
      "access_token_lifetime_minutes": {{ACCESS_TOKEN_LIFETIME}},
      "refresh_token_lifetime_hours": {{REFRESH_TOKEN_LIFETIME}}
    },
    "argon2": {
      "time_cost": {{ARGON2_TIME_COST}},
      "memory_cost": {{ARGON2_MEMORY_COST}},
      "parallelism": {{ARGON2_PARALLELISM}}
    },
    "rate_limiting": {
      "login_attempts_per_minute": {{LOGIN_RATE_LIMIT}},
      "api_requests_per_minute": {{API_RATE_LIMIT}}
    }
  },
  "features": {
    "multi_project_support": true,
    "cross_project_sso": {{CROSS_PROJECT_SSO}},
    "external_auth_providers": {{EXTERNAL_AUTH_ENABLED}}
  }
}
```

#### Database Service Configuration Template
```json
{
  "project": {
    "id": "{{PROJECT_ID}}",
    "name": "{{PROJECT_NAME}}",
    "auth_service_url": "{{AUTH_SERVICE_URL}}"
  },
  "server": {
    "host": "{{DB_SERVICE_HOST}}",
    "port": {{DB_SERVICE_PORT}}
  },
  "databases": {
    "{{PROJECT_ID}}_git": {
      "host": "{{GIT_DB_HOST}}",
      "port": {{GIT_DB_PORT}},
      "database": "{{GIT_DB_NAME}}",
      "username": "{{GIT_DB_USER}}",
      "password": "{{GIT_DB_PASSWORD}}",
      "schema": "git_{{PROJECT_ID}}",
      "pool_size": {{GIT_DB_POOL_SIZE}}
    },
    "{{PROJECT_ID}}_logging": {
      "host": "{{LOG_DB_HOST}}",
      "port": {{LOG_DB_PORT}},
      "database": "{{LOG_DB_NAME}}",
      "username": "{{LOG_DB_USER}}",
      "password": "{{LOG_DB_PASSWORD}}",
      "schema": "logging_{{PROJECT_ID}}",
      "pool_size": {{LOG_DB_POOL_SIZE}}
    },
    "{{PROJECT_ID}}_dashboard": {
      "host": "{{DASH_DB_HOST}}",
      "port": {{DASH_DB_PORT}},
      "database": "{{DASH_DB_NAME}}",
      "username": "{{DASH_DB_USER}}",
      "password": "{{DASH_DB_PASSWORD}}",
      "schema": "dashboard_{{PROJECT_ID}}",
      "pool_size": {{DASH_DB_POOL_SIZE}}
    }
  },
  "applications": {
    "git-server": {
      "database": "{{PROJECT_ID}}_git",
      "permissions": ["read", "write", "admin"]
    },
    "logging-service": {
      "database": "{{PROJECT_ID}}_logging",
      "permissions": ["read", "write"]
    },
    "dashboard": {
      "database": "{{PROJECT_ID}}_dashboard",
      "permissions": ["read", "write"]
    }
  }
}
```

### Deployment Patterns

#### Pattern 1: Single Server Multi-Project
```bash
# Server: git.chcit.org
# Projects: project-tracker, git-dashboard, logging-system

# Auth Service (shared)
/opt/auth-service/
├── bin/auth-service
├── config/
│   ├── project-tracker.json
│   ├── git-dashboard.json
│   └── logging-system.json
└── logs/

# Database Service (shared)
/opt/database-service/
├── bin/database-service
├── config/
│   ├── project-tracker.json
│   ├── git-dashboard.json
│   └── logging-system.json
└── logs/

# Nginx routing
/auth-api/project-tracker/* → auth-service:8082 (project-tracker config)
/auth-api/git-dashboard/*   → auth-service:8083 (git-dashboard config)
/database-api/project-tracker/* → database-service:8081 (project-tracker config)
/database-api/git-dashboard/*   → database-service:8084 (git-dashboard config)
```

#### Pattern 2: Distributed Multi-Server
```bash
# Auth Server: auth.company.com
/opt/auth-service/ (handles all projects)

# Database Server 1: db1.company.com
/opt/database-service/ (project-tracker, git-dashboard)

# Database Server 2: db2.company.com
/opt/database-service/ (logging-system, analytics)

# Web Server: web.company.com
nginx → auth.company.com:8082
nginx → db1.company.com:8081
nginx → db2.company.com:8081
```

#### Pattern 3: Project-Isolated Deployment
```bash
# Project A Server: project-a.company.com
/opt/auth-service/ (project-a only)
/opt/database-service/ (project-a only)

# Project B Server: project-b.company.com
/opt/auth-service/ (project-b only)
/opt/database-service/ (project-b only)

# Shared Auth Server: auth.company.com (optional)
/opt/auth-service/ (cross-project SSO)
```

### Database Schema (Multi-Project)

#### Projects Table
```sql
CREATE TABLE projects (
    id SERIAL PRIMARY KEY,
    project_id VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    domain VARCHAR(255),
    description TEXT,
    settings JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Example data
INSERT INTO projects (project_id, name, domain, description) VALUES
('project-tracker', 'Project Tracker', 'git.chcit.org', 'Main project tracking system'),
('git-dashboard', 'Git Dashboard', 'git.chcit.org', 'Git repository dashboard'),
('logging-system', 'Logging System', 'logs.chcit.org', 'Centralized logging');
```

#### Users Table (Multi-Project)
```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(255) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    salt BYTEA NOT NULL,
    argon2_params JSONB NOT NULL,
    global_role VARCHAR(50) DEFAULT 'user', -- global, admin, super_admin
    is_active BOOLEAN DEFAULT true,
    is_verified BOOLEAN DEFAULT false,
    failed_login_attempts INTEGER DEFAULT 0,
    locked_until TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    metadata JSONB DEFAULT '{}'
);

-- Project-specific user roles and permissions
CREATE TABLE project_users (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    project_id INTEGER REFERENCES projects(id) ON DELETE CASCADE,
    role VARCHAR(50) NOT NULL, -- admin, manager, developer, viewer
    permissions JSONB DEFAULT '[]',
    is_active BOOLEAN DEFAULT true,
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, project_id)
);

-- Roles definition table
CREATE TABLE roles (
    id SERIAL PRIMARY KEY,
    project_id INTEGER REFERENCES projects(id) ON DELETE CASCADE,
    name VARCHAR(50) NOT NULL,
    description TEXT,
    permissions JSONB DEFAULT '[]',
    is_system_role BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(project_id, name)
);

-- Default roles for each project
INSERT INTO roles (project_id, name, description, permissions, is_system_role)
SELECT p.id, 'admin', 'Project Administrator',
       '["read", "write", "delete", "manage_users", "manage_settings"]'::jsonb, true
FROM projects p;

INSERT INTO roles (project_id, name, description, permissions, is_system_role)
SELECT p.id, 'developer', 'Developer',
       '["read", "write", "create_branches", "manage_repos"]'::jsonb, true
FROM projects p;

INSERT INTO roles (project_id, name, description, permissions, is_system_role)
SELECT p.id, 'viewer', 'Read-only Access',
       '["read"]'::jsonb, true
FROM projects p;
```

#### Sessions Table (Multi-Project)
```sql
CREATE TABLE sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    project_id INTEGER REFERENCES projects(id) ON DELETE CASCADE,
    access_token_hash TEXT NOT NULL,
    refresh_token_hash TEXT NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_used TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address INET,
    user_agent TEXT,
    session_data JSONB DEFAULT '{}',
    is_cross_project BOOLEAN DEFAULT false
);

-- Index for efficient session lookups
CREATE INDEX idx_sessions_user_project ON sessions(user_id, project_id);
CREATE INDEX idx_sessions_token_hash ON sessions(access_token_hash);
CREATE INDEX idx_sessions_expires_at ON sessions(expires_at);
```

#### Audit Log Table (Multi-Project)
```sql
CREATE TABLE audit_log (
    id BIGSERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    project_id INTEGER REFERENCES projects(id),
    event_type VARCHAR(50) NOT NULL,
    action VARCHAR(100) NOT NULL,
    resource VARCHAR(100),
    resource_id VARCHAR(100),
    ip_address INET,
    user_agent TEXT,
    success BOOLEAN NOT NULL,
    details JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for efficient audit queries
CREATE INDEX idx_audit_log_user_project ON audit_log(user_id, project_id);
CREATE INDEX idx_audit_log_event_type ON audit_log(event_type);
CREATE INDEX idx_audit_log_created_at ON audit_log(created_at);
CREATE INDEX idx_audit_log_success ON audit_log(success);

-- Partitioning for large audit logs (optional)
-- CREATE TABLE audit_log_y2024m12 PARTITION OF audit_log
-- FOR VALUES FROM ('2024-12-01') TO ('2025-01-01');
```

### Multi-Project Deployment Scripts

#### New Project Setup Script
```bash
#!/bin/bash
# setup-new-project.sh

PROJECT_ID="$1"
PROJECT_NAME="$2"
PROJECT_DOMAIN="$3"
DB_HOST="$4"
AUTH_HOST="$5"

if [ -z "$PROJECT_ID" ] || [ -z "$PROJECT_NAME" ]; then
    echo "Usage: $0 <project_id> <project_name> [domain] [db_host] [auth_host]"
    exit 1
fi

echo "Setting up new project: $PROJECT_NAME ($PROJECT_ID)"

# 1. Create project configuration
envsubst < config/templates/auth-service-template.json > config/projects/${PROJECT_ID}-auth.json
envsubst < config/templates/database-service-template.json > config/projects/${PROJECT_ID}-db.json

# 2. Create database schemas
psql -h ${DB_HOST:-localhost} -U postgres -c "
CREATE SCHEMA IF NOT EXISTS auth_${PROJECT_ID};
CREATE SCHEMA IF NOT EXISTS git_${PROJECT_ID};
CREATE SCHEMA IF NOT EXISTS logging_${PROJECT_ID};
CREATE SCHEMA IF NOT EXISTS dashboard_${PROJECT_ID};
"

# 3. Insert project record
psql -h ${DB_HOST:-localhost} -U postgres -d auth_service -c "
INSERT INTO projects (project_id, name, domain, description)
VALUES ('${PROJECT_ID}', '${PROJECT_NAME}', '${PROJECT_DOMAIN}', 'Auto-created project')
ON CONFLICT (project_id) DO UPDATE SET
    name = EXCLUDED.name,
    domain = EXCLUDED.domain,
    updated_at = CURRENT_TIMESTAMP;
"

# 4. Create default roles for project
psql -h ${DB_HOST:-localhost} -U postgres -d auth_service -c "
INSERT INTO roles (project_id, name, description, permissions, is_system_role)
SELECT p.id, 'admin', 'Project Administrator',
       '[\"read\", \"write\", \"delete\", \"manage_users\", \"manage_settings\"]'::jsonb, true
FROM projects p WHERE p.project_id = '${PROJECT_ID}'
ON CONFLICT (project_id, name) DO NOTHING;
"

# 5. Update nginx configuration
cat >> /etc/nginx/sites-available/multi-project.conf << EOF

# ${PROJECT_NAME} routes
location /auth-api/${PROJECT_ID}/ {
    proxy_pass http://127.0.0.1:8082/api/;
    proxy_set_header X-Project-ID ${PROJECT_ID};
    # ... other proxy settings
}

location /database-api/${PROJECT_ID}/ {
    proxy_pass http://127.0.0.1:8081/api/;
    proxy_set_header X-Project-ID ${PROJECT_ID};
    # ... other proxy settings
}
EOF

# 6. Reload nginx
nginx -t && systemctl reload nginx

echo "Project ${PROJECT_NAME} setup complete!"
echo "Auth API: https://${PROJECT_DOMAIN}/auth-api/${PROJECT_ID}/"
echo "Database API: https://${PROJECT_DOMAIN}/database-api/${PROJECT_ID}/"
```

## Security Considerations

### Password Security
- **Argon2id Algorithm**: Memory-hard, GPU-resistant
- **Parameter Tuning**: Adjust based on server capabilities
- **Salt Management**: Unique salt per password
- **Migration Strategy**: Gradual conversion from existing hashes

### Token Security
- **JWT Implementation**: RS256 or HS256 with secure secrets
- **Token Rotation**: Refresh token mechanism
- **Expiration**: Short-lived access tokens (15-30 minutes)
- **Revocation**: Session invalidation capabilities

### Rate Limiting
- **Login Attempts**: Progressive delays and account lockout
- **API Endpoints**: Request rate limiting per IP/user
- **Brute Force Protection**: Automatic IP blocking
- **Monitoring**: Real-time attack detection

### Audit and Compliance
- **Comprehensive Logging**: All authentication events
- **GDPR Compliance**: Data minimization and user rights
- **SOX Compliance**: Immutable audit trails
- **Security Monitoring**: Real-time alerting

## Deployment Strategy

### Infrastructure Requirements (Ubuntu 24.04)
- **Server Resources**: Minimum 2 CPU cores, 4GB RAM for auth service
- **Operating System**: Ubuntu 24.04 LTS with latest security updates
- **Database**: PostgreSQL 17.5 (latest version, already installed on git.chcit.org)
- **Monitoring**: systemd journal logging, optional Prometheus integration
- **Backup**: Regular database backups using pg_dump and automated scripts
- **Security**: UFW firewall, fail2ban for SSH protection, automatic security updates

### Configuration Management
- **Environment-specific configs**: Development, staging, production
- **Secret management**: Secure storage of JWT secrets and DB credentials
- **Feature flags**: Gradual rollout capabilities
- **Monitoring integration**: Prometheus/Grafana metrics

### Rollback Plan
- **Service versioning**: Ability to rollback to previous version
- **Database migrations**: Reversible schema changes
- **Configuration rollback**: Quick reversion to previous settings
- **User data preservation**: No data loss during rollback

## Testing Strategy

### Unit Testing
- **Password hashing**: Argon2id implementation
- **JWT handling**: Token generation and validation
- **Rate limiting**: Algorithm correctness
- **Database operations**: Repository layer testing

### Integration Testing
- **Service communication**: Auth ↔ Database service
- **Database connectivity**: PostgreSQL integration
- **API endpoints**: Full request/response cycle
- **Security features**: Authentication flow testing

### Performance Testing
- **Load testing**: Concurrent authentication requests
- **Stress testing**: High-volume user registration
- **Memory usage**: Argon2id parameter optimization
- **Response times**: Authentication latency measurement

### Security Testing
- **Penetration testing**: Authentication bypass attempts
- **Brute force testing**: Rate limiting effectiveness
- **Token security**: JWT manipulation attempts
- **SQL injection**: Database query security

## Timeline and Milestones

### Phase 1: Development (4-6 weeks)
- **Week 1-2**: Project setup and core authentication
- **Week 3-4**: Database integration and JWT implementation
- **Week 5-6**: Security features and testing

### Phase 2: Integration (2-3 weeks)
- **Week 7-8**: Service integration and nginx configuration
- **Week 9**: Testing and validation

### Phase 3: Migration (3-4 weeks)
- **Week 10-11**: UI updates and user data migration
- **Week 12-13**: End-to-end testing and documentation

### Phase 4: Deployment (1-2 weeks)
- **Week 14**: Production deployment and monitoring
- **Week 15**: Go-live and post-deployment support

### Success Criteria
- ✅ All authentication operations use Argon2id
- ✅ Zero downtime during migration
- ✅ Performance meets or exceeds current system
- ✅ Security audit passes with no critical issues
- ✅ All UI applications successfully integrated

## Risk Management

### Technical Risks

| Risk | Impact | Probability | Mitigation Strategy |
|------|--------|-------------|-------------------|
| Argon2 library compatibility | High | Low | Test on target Ubuntu 24.04, fallback to bcrypt |
| Performance degradation | Medium | Medium | Load testing, parameter tuning |
| Database migration issues | High | Low | Comprehensive backup, rollback procedures |
| Service integration failures | Medium | Medium | Extensive integration testing |
| User data corruption | Critical | Very Low | Multiple backups, validation checks |

### Operational Risks

| Risk | Impact | Probability | Mitigation Strategy |
|------|--------|-------------|-------------------|
| Extended downtime | High | Low | Blue-green deployment, rollback plan |
| User lockout | Medium | Medium | Emergency admin access, unlock procedures |
| Performance issues | Medium | Medium | Monitoring, auto-scaling |
| Security vulnerabilities | Critical | Low | Security audit, penetration testing |

### Business Risks

| Risk | Impact | Probability | Mitigation Strategy |
|------|--------|-------------|-------------------|
| User experience degradation | Medium | Low | UX testing, gradual rollout |
| Compliance violations | High | Very Low | Legal review, audit trail preservation |
| Development delays | Medium | Medium | Buffer time, parallel development |

## Implementation Guidelines

### Development Environment Setup

#### Prerequisites (Ubuntu 24.04)
```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install build dependencies
sudo apt install -y build-essential cmake pkg-config git
sudo apt install -y libssl-dev libpq-dev libargon2-dev
sudo apt install -y libnlohmann-json3-dev libcurl4-openssl-dev
sudo apt install -y postgresql-client-16 nginx

# Install Crow HTTP framework
git clone https://github.com/CrowCpp/Crow.git
cd Crow && mkdir build && cd build
cmake .. -DCROW_BUILD_EXAMPLES=OFF -DCMAKE_BUILD_TYPE=Release
make -j$(nproc) && sudo make install

# Update library cache
sudo ldconfig

# Install additional monitoring tools
sudo apt install -y htop iotop nethogs curl jq
```

#### Project Initialization (Ubuntu 24.04)
```bash
# Create auth-service project structure
sudo mkdir -p /opt/auth-service/{bin,config,logs,data}
sudo mkdir -p /opt/database-service/{bin,config,logs,data}

# Set up service user
sudo useradd -r -s /bin/false -d /opt/auth-service auth-service
sudo useradd -r -s /bin/false -d /opt/database-service database-service

# Set permissions
sudo chown -R auth-service:auth-service /opt/auth-service
sudo chown -R database-service:database-service /opt/database-service

# Create development workspace
mkdir -p ~/auth-service-build
cd ~/auth-service-build

# Initialize CMake project
cmake -B build -S . -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/opt/auth-service
cmake --build build --parallel $(nproc)

# Install to production directory
sudo cmake --install build
```

### Code Implementation Examples

#### Argon2id Password Hasher
```cpp
// include/auth-service/security/argon2_hasher.hpp
#pragma once
#include <string>
#include <vector>
#include <argon2.h>

class Argon2Hasher {
public:
    struct Config {
        uint32_t time_cost = 3;
        uint32_t memory_cost = 65536; // 64MB
        uint32_t parallelism = 4;
        uint32_t hash_length = 32;
        uint32_t salt_length = 16;
    };

    explicit Argon2Hasher(const Config& config = {});

    std::string hashPassword(const std::string& password);
    bool verifyPassword(const std::string& password, const std::string& hash);

private:
    Config config_;
    std::vector<uint8_t> generateSalt();
    std::string encodeHash(const std::vector<uint8_t>& salt,
                          const std::vector<uint8_t>& hash);
    bool decodeHash(const std::string& encoded,
                   std::vector<uint8_t>& salt,
                   std::vector<uint8_t>& hash);
};
```

#### JWT Token Manager
```cpp
// include/auth-service/core/token_manager.hpp
#pragma once
#include <string>
#include <chrono>
#include <jwt-cpp/jwt.h>

class TokenManager {
public:
    struct TokenPair {
        std::string access_token;
        std::string refresh_token;
        std::chrono::system_clock::time_point expires_at;
    };

    explicit TokenManager(const std::string& secret);

    TokenPair generateTokens(int user_id, const std::string& role);
    bool validateAccessToken(const std::string& token);
    TokenPair refreshTokens(const std::string& refresh_token);
    void revokeToken(const std::string& token);

private:
    std::string secret_;
    std::chrono::minutes access_token_lifetime_{30};
    std::chrono::hours refresh_token_lifetime_{24 * 7}; // 7 days
};
```

### Database Migration Scripts

#### User Data Migration
```sql
-- Migration script: migrate_users_to_argon2.sql
-- Migrate existing users from SHA-256 to Argon2id

BEGIN;

-- Create temporary table for migration
CREATE TEMP TABLE user_migration_log (
    user_id INTEGER,
    old_hash TEXT,
    new_hash TEXT,
    migrated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Function to migrate user passwords
CREATE OR REPLACE FUNCTION migrate_user_password(
    p_user_id INTEGER,
    p_username TEXT,
    p_old_hash TEXT,
    p_salt TEXT
) RETURNS BOOLEAN AS $$
DECLARE
    v_temp_password TEXT;
    v_new_hash TEXT;
BEGIN
    -- For migration, we'll need users to reset passwords
    -- or use a temporary migration strategy

    -- Option 1: Force password reset
    UPDATE auth_service.users
    SET password_hash = 'MIGRATION_REQUIRED',
        requires_password_reset = TRUE
    WHERE id = p_user_id;

    -- Log migration
    INSERT INTO user_migration_log (user_id, old_hash, new_hash)
    VALUES (p_user_id, p_old_hash, 'MIGRATION_REQUIRED');

    RETURN TRUE;
EXCEPTION
    WHEN OTHERS THEN
        RETURN FALSE;
END;
$$ LANGUAGE plpgsql;

-- Migrate all users
DO $$
DECLARE
    user_record RECORD;
BEGIN
    FOR user_record IN
        SELECT id, username, password_hash, salt
        FROM database_service.users
    LOOP
        PERFORM migrate_user_password(
            user_record.id,
            user_record.username,
            user_record.password_hash,
            user_record.salt
        );
    END LOOP;
END $$;

COMMIT;
```

### Monitoring and Alerting

#### Prometheus Metrics
```cpp
// Metrics collection for auth service
class AuthMetrics {
public:
    static void recordLogin(bool success, const std::string& method);
    static void recordPasswordHash(std::chrono::milliseconds duration);
    static void recordTokenGeneration(std::chrono::milliseconds duration);
    static void recordRateLimitHit(const std::string& endpoint);

private:
    // Prometheus metric definitions
    static prometheus::Counter& login_attempts_total_;
    static prometheus::Histogram& password_hash_duration_;
    static prometheus::Histogram& token_generation_duration_;
    static prometheus::Counter& rate_limit_hits_total_;
};
```

#### Health Check Endpoint
```cpp
// Health check implementation
crow::response healthCheck() {
    nlohmann::json health;
    health["status"] = "healthy";
    health["timestamp"] = std::chrono::system_clock::now();
    health["version"] = AUTH_SERVICE_VERSION;

    // Check database connectivity
    try {
        auto conn = db_pool_->getConnection();
        conn->execute("SELECT 1");
        health["database"] = "connected";
    } catch (const std::exception& e) {
        health["database"] = "disconnected";
        health["status"] = "unhealthy";
    }

    // Check Argon2 functionality
    try {
        Argon2Hasher hasher;
        hasher.hashPassword("test");
        health["argon2"] = "functional";
    } catch (const std::exception& e) {
        health["argon2"] = "error";
        health["status"] = "unhealthy";
    }

    int status_code = (health["status"] == "healthy") ? 200 : 503;
    return crow::response(status_code, health.dump());
}
```

## Deployment Automation

### Linux Deployment Scripts (Ubuntu 24.04)
```bash
#!/bin/bash
# deploy-auth-service.sh - Ubuntu 24.04 deployment script

set -e

SERVER_HOST="${1:-git.chcit.org}"
USERNAME="${2:-btaylor-admin}"
SSH_KEY="${3:-~/.ssh/id_rsa}"
BUILD_DIR="/home/<USER>/auth-service-build"
INSTALL_DIR="/opt/auth-service"

echo "Deploying Authentication Service to Ubuntu 24.04 server: ${SERVER_HOST}..."

# Function to execute commands on remote server
remote_exec() {
    ssh -i "${SSH_KEY}" "${USERNAME}@${SERVER_HOST}" "$1"
}

# Copy source files to server
echo "Copying source files..."
rsync -avz --delete -e "ssh -i ${SSH_KEY}" \
    ./auth-service/ "${USERNAME}@${SERVER_HOST}:${BUILD_DIR}/"

# Build and deploy on Ubuntu server
echo "Building and deploying on Ubuntu 24.04..."
remote_exec "
    cd ${BUILD_DIR}

    # Clean previous build
    rm -rf build
    mkdir -p build
    cd build

    # Configure and build with Ubuntu 24.04 optimizations
    cmake .. -DCMAKE_BUILD_TYPE=Release \
             -DCMAKE_INSTALL_PREFIX=${INSTALL_DIR} \
             -DCMAKE_CXX_STANDARD=23 \
             -DCMAKE_CXX_FLAGS='-march=native -O3'
    make -j\$(nproc)

    # Stop existing service
    sudo systemctl stop auth-service 2>/dev/null || true

    # Install binary and configuration
    sudo make install
    sudo cp ../config/production.json ${INSTALL_DIR}/config/

    # Install systemd service for Ubuntu 24.04
    sudo cp ../deployment/systemd/auth-service.service /etc/systemd/system/
    sudo systemctl daemon-reload
    sudo systemctl enable auth-service

    # Set proper permissions for Ubuntu
    sudo chown -R auth-service:auth-service ${INSTALL_DIR}
    sudo chmod +x ${INSTALL_DIR}/bin/auth-service

    # Configure firewall (UFW) if enabled
    sudo ufw allow 8082/tcp comment 'Auth Service' 2>/dev/null || true

    # Start service
    sudo systemctl start auth-service

    # Wait for service to start
    sleep 5

    # Verify deployment
    if curl -f http://localhost:8082/api/health >/dev/null 2>&1; then
        echo 'Authentication Service deployed successfully on Ubuntu 24.04!'
        sudo systemctl status auth-service --no-pager -l
    else
        echo 'Health check failed - checking logs...'
        sudo journalctl -u auth-service -n 20 --no-pager
        exit 1
    fi
"

echo "Ubuntu 24.04 deployment completed successfully!"
```

### PowerShell Integration (Windows Development → Ubuntu Deployment)
```powershell
# Enhanced deploy-database-service-modular.ps1 - Deploy to Ubuntu 24.04 from Windows

function Deploy-AuthServiceToUbuntu {
    param(
        [string]$ServerHost = "git.chcit.org",
        [string]$Username = "btaylor-admin",
        [string]$KeyPath = "C:\Users\<USER>\.ssh\id_rsa"
    )

    Write-Host "Deploying Authentication Service to Ubuntu 24.04..." -ForegroundColor Green

    # Ubuntu-specific deployment commands
    $ubuntuDeployScript = @"
#!/bin/bash
set -e

cd /home/<USER>/auth-service-build
mkdir -p build && cd build

# Configure for Ubuntu 24.04 with C++23
cmake .. -DCMAKE_BUILD_TYPE=Release \
         -DCMAKE_INSTALL_PREFIX=/opt/auth-service \
         -DCMAKE_CXX_STANDARD=23

# Build with all available cores
make -j`$(nproc)

# Stop and update service
sudo systemctl stop auth-service 2>/dev/null || true
sudo make install

# Set Ubuntu-specific permissions
sudo chown -R auth-service:auth-service /opt/auth-service
sudo chmod +x /opt/auth-service/bin/auth-service

# Configure Ubuntu firewall
sudo ufw allow 8082/tcp comment 'Auth Service' 2>/dev/null || true

# Start service
sudo systemctl start auth-service

# Verify on Ubuntu
sleep 3
if curl -f http://localhost:8082/api/health >/dev/null 2>&1; then
    echo "Auth service deployed successfully on Ubuntu 24.04"
    systemctl status auth-service --no-pager
else
    echo "Deployment failed - checking Ubuntu logs"
    sudo journalctl -u auth-service -n 10 --no-pager
    exit 1
fi
"@

    # Copy files and execute Ubuntu deployment
    scp -i $KeyPath -r "auth-service\*" "${Username}@${ServerHost}:/home/<USER>/auth-service-build/"
    $ubuntuDeployScript | ssh -i $KeyPath "${Username}@${ServerHost}" "bash -s"

    Write-Host "Authentication Service deployed to Ubuntu 24.04!" -ForegroundColor Green
}

function Show-UbuntuServiceMenu {
    Write-Host "`n=== Ubuntu 24.04 Service Management ===" -ForegroundColor Cyan
    Write-Host "15. Deploy Auth Service (Ubuntu 24.04)"
    Write-Host "16. Deploy Database Service (Ubuntu 24.04)"
    Write-Host "17. Check Ubuntu Service Status"
    Write-Host "18. View Ubuntu Service Logs"
    Write-Host "19. Restart Ubuntu Services"
    Write-Host "20. Update Ubuntu Dependencies"
}
```

### Nginx Configuration Update
```nginx
# Add to existing nginx configuration
upstream auth_service {
    server 127.0.0.1:8082;
    keepalive 32;
}

# Authentication service location
location /auth-api/ {
    # Rate limiting for auth endpoints
    limit_req zone=auth burst=10 nodelay;

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";

    # Proxy configuration
    proxy_pass http://auth_service/api/;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection 'upgrade';
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_cache_bypass $http_upgrade;

    # Timeouts
    proxy_connect_timeout 5s;
    proxy_send_timeout 30s;
    proxy_read_timeout 30s;
}

# Rate limiting zones for auth
limit_req_zone $binary_remote_addr zone=auth:10m rate=5r/s;
limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;

# Specific rate limiting for login endpoint
location /auth-api/auth/login {
    limit_req zone=login burst=3 nodelay;
    proxy_pass http://auth_service/api/auth/login;
    # ... other proxy settings
}
```

## Post-Migration Validation

### Validation Checklist
- [ ] All users can authenticate with existing credentials
- [ ] New user registration works correctly
- [ ] Password reset functionality operational
- [ ] JWT tokens generated and validated properly
- [ ] Rate limiting prevents brute force attacks
- [ ] Audit logging captures all events
- [ ] Performance meets baseline requirements
- [ ] Security scan passes without critical issues
- [ ] Backup and recovery procedures tested
- [ ] Monitoring and alerting functional

### Performance Benchmarks
```bash
# Authentication performance testing
ab -n 1000 -c 10 -H "Content-Type: application/json" \
   -p login_payload.json https://db.chcit.org/auth-api/auth/login

# Expected results:
# - 95th percentile < 500ms
# - Throughput > 100 req/sec
# - Zero failed requests
```

#### Project Migration Script
```bash
#!/bin/bash
# migrate-project.sh - Migrate existing project to new auth system

SOURCE_PROJECT="$1"
TARGET_PROJECT="$2"
SOURCE_DB="$3"

echo "Migrating project $SOURCE_PROJECT to new auth system..."

# 1. Export existing users
psql -h localhost -U postgres -d $SOURCE_DB -c "
COPY (
    SELECT username, email, password_hash, salt, role, created_at, last_login
    FROM users
    WHERE is_active = true
) TO '/tmp/${SOURCE_PROJECT}_users.csv' WITH CSV HEADER;
"

# 2. Import users to new auth system
python3 << EOF
import csv
import psycopg2
import json
from datetime import datetime

# Connect to auth database
conn = psycopg2.connect(
    host='localhost',
    database='auth_service',
    user='database_service',
    password='password123'
)
cur = conn.cursor()

# Get project ID
cur.execute("SELECT id FROM projects WHERE project_id = %s", ('${TARGET_PROJECT}',))
project_id = cur.fetchone()[0]

# Import users
with open('/tmp/${SOURCE_PROJECT}_users.csv', 'r') as f:
    reader = csv.DictReader(f)
    for row in reader:
        # Insert user
        cur.execute("""
            INSERT INTO users (username, email, password_hash, salt, argon2_params, is_active, created_at, last_login)
            VALUES (%s, %s, %s, %s, %s, true, %s, %s)
            ON CONFLICT (username) DO NOTHING
            RETURNING id
        """, (
            row['username'],
            row['email'],
            'MIGRATION_REQUIRED',  # Force password reset
            b'',  # Empty salt for migration
            json.dumps({'migration': True}),
            row['created_at'],
            row['last_login']
        ))

        user_result = cur.fetchone()
        if user_result:
            user_id = user_result[0]

            # Add user to project
            cur.execute("""
                INSERT INTO project_users (user_id, project_id, role, is_active)
                VALUES (%s, %s, %s, true)
                ON CONFLICT (user_id, project_id) DO NOTHING
            """, (user_id, project_id, row['role']))

conn.commit()
conn.close()
print(f"Migration completed for project {TARGET_PROJECT}")
EOF

# 3. Clean up temporary files
rm -f /tmp/${SOURCE_PROJECT}_users.csv

echo "Project migration completed. Users will need to reset passwords on first login."
```

## Multi-Project Management

### Configuration Management System

#### Environment Variable Templates
```bash
# .env.template for new projects
PROJECT_ID={{PROJECT_ID}}
PROJECT_NAME={{PROJECT_NAME}}
PROJECT_DOMAIN={{PROJECT_DOMAIN}}

# Auth Service Configuration
AUTH_HOST={{AUTH_HOST:-127.0.0.1}}
AUTH_PORT={{AUTH_PORT:-8082}}
AUTH_DB_HOST={{AUTH_DB_HOST:-localhost}}
AUTH_DB_PORT={{AUTH_DB_PORT:-5432}}
AUTH_DB_NAME={{AUTH_DB_NAME:-auth_service}}
AUTH_DB_USER={{AUTH_DB_USER:-database_service}}
AUTH_DB_PASSWORD={{AUTH_DB_PASSWORD}}

# Database Service Configuration
DB_SERVICE_HOST={{DB_SERVICE_HOST:-127.0.0.1}}
DB_SERVICE_PORT={{DB_SERVICE_PORT:-8081}}

# Project-specific database connections
GIT_DB_HOST={{GIT_DB_HOST:-localhost}}
GIT_DB_NAME={{GIT_DB_NAME:-${PROJECT_ID}_git}}
LOG_DB_HOST={{LOG_DB_HOST:-localhost}}
LOG_DB_NAME={{LOG_DB_NAME:-${PROJECT_ID}_logging}}
DASH_DB_HOST={{DASH_DB_HOST:-localhost}}
DASH_DB_NAME={{DASH_DB_NAME:-${PROJECT_ID}_dashboard}}

# Security Configuration
JWT_SECRET={{JWT_SECRET}}
ARGON2_TIME_COST={{ARGON2_TIME_COST:-3}}
ARGON2_MEMORY_COST={{ARGON2_MEMORY_COST:-65536}}
ARGON2_PARALLELISM={{ARGON2_PARALLELISM:-4}}

# Feature Flags
CROSS_PROJECT_SSO={{CROSS_PROJECT_SSO:-false}}
EXTERNAL_AUTH_ENABLED={{EXTERNAL_AUTH_ENABLED:-false}}
```

#### Multi-Instance Systemd Service
```ini
# /etc/systemd/system/auth-service@.service
[Unit]
Description=Authentication Service for %i
After=network.target postgresql.service
Requires=postgresql.service

[Service]
Type=simple
User=auth-service
Group=auth-service
WorkingDirectory=/opt/auth-service
ExecStart=/opt/auth-service/bin/auth-service --config=/opt/auth-service/config/projects/%i-auth.json
Restart=always
RestartSec=5
Environment=PROJECT_ID=%i

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/auth-service/logs /opt/auth-service/data

[Install]
WantedBy=multi-user.target

# Usage:
# systemctl start auth-service@project-tracker
# systemctl start auth-service@git-dashboard
# systemctl enable auth-service@project-tracker
```

### Monitoring and Management

#### Multi-Project Health Check
```cpp
// Enhanced health check for multi-project deployment
crow::response multiProjectHealthCheck() {
    nlohmann::json health;
    health["service"] = "auth-service";
    health["timestamp"] = std::chrono::system_clock::now();
    health["version"] = AUTH_SERVICE_VERSION;
    health["projects"] = nlohmann::json::array();

    // Check each configured project
    for (const auto& [project_id, config] : project_configs_) {
        nlohmann::json project_health;
        project_health["project_id"] = project_id;
        project_health["name"] = config.name;

        try {
            // Check project database connectivity
            auto conn = getProjectConnection(project_id);
            conn->execute("SELECT 1");
            project_health["database"] = "connected";

            // Check active sessions
            auto result = conn->execute(
                "SELECT COUNT(*) FROM sessions WHERE project_id = $1 AND expires_at > NOW()",
                project_id
            );
            project_health["active_sessions"] = result[0][0].as<int>();

            // Check user count
            auto user_result = conn->execute(
                "SELECT COUNT(*) FROM project_users WHERE project_id = $1 AND is_active = true",
                project_id
            );
            project_health["active_users"] = user_result[0][0].as<int>();

            project_health["status"] = "healthy";

        } catch (const std::exception& e) {
            project_health["status"] = "unhealthy";
            project_health["error"] = e.what();
        }

        health["projects"].push_back(project_health);
    }

    // Overall service status
    bool all_healthy = std::all_of(
        health["projects"].begin(),
        health["projects"].end(),
        [](const auto& project) {
            return project["status"] == "healthy";
        }
    );

    health["overall_status"] = all_healthy ? "healthy" : "degraded";

    int status_code = all_healthy ? 200 : 503;
    return crow::response(status_code, health.dump());
}
```

### Deployment Automation Updates

#### Enhanced Ubuntu Multi-Project Deployment
```bash
#!/bin/bash
# deploy-multi-project-services.sh - Ubuntu 24.04 multi-project deployment

set -e

PROJECTS=("${@:-project-tracker git-dashboard}")
SERVER_HOST="${SERVER_HOST:-git.chcit.org}"
USERNAME="${USERNAME:-btaylor-admin}"
SSH_KEY="${SSH_KEY:-~/.ssh/id_rsa}"

echo "Deploying Multi-Project Services to Ubuntu 24.04..."

# Function to execute commands on Ubuntu server
remote_exec() {
    ssh -i "${SSH_KEY}" "${USERNAME}@${SERVER_HOST}" "$1"
}

# Deploy each project configuration
for project in "${PROJECTS[@]}"; do
    echo "Configuring project: $project"

    # Generate project-specific configuration from templates
    envsubst < config/templates/auth-service-template.json > "config/projects/$project-auth.json"
    envsubst < config/templates/database-service-template.json > "config/projects/$project-db.json"

    # Copy configurations to Ubuntu server
    scp -i "${SSH_KEY}" "config/projects/$project-auth.json" \
        "${USERNAME}@${SERVER_HOST}:/opt/auth-service/config/"
    scp -i "${SSH_KEY}" "config/projects/$project-db.json" \
        "${USERNAME}@${SERVER_HOST}:/opt/database-service/config/"
done

# Deploy and restart services on Ubuntu
echo "Deploying services on Ubuntu 24.04..."
remote_exec "
    # Update Ubuntu system packages
    sudo apt update && sudo apt upgrade -y

    # Restart auth service instances for each project
    $(printf 'sudo systemctl restart auth-service@%s\n' "${PROJECTS[@]}")

    # Restart database service
    sudo systemctl restart database-service

    # Wait for services to start
    sleep 10

    # Verify deployments on Ubuntu
    $(printf 'curl -f http://localhost:8082/api/health?project=%s || echo \"Health check failed for %s\"\n' "${PROJECTS[@]}" "${PROJECTS[@]}")

    # Show Ubuntu service status
    sudo systemctl status auth-service database-service --no-pager
"

echo "Multi-project deployment to Ubuntu 24.04 completed!"
```

#### PowerShell Integration for Ubuntu Deployment
```powershell
# Enhanced deploy-database-service-modular.ps1 - Ubuntu 24.04 deployment

function Deploy-MultiProjectToUbuntu {
    param(
        [string[]]$Projects = @("project-tracker", "git-dashboard"),
        [string]$ServerHost = "git.chcit.org",
        [string]$Username = "btaylor-admin",
        [string]$KeyPath = "C:\Users\<USER>\.ssh\id_rsa"
    )

    Write-Host "Deploying Multi-Project Services to Ubuntu 24.04..." -ForegroundColor Green

    # Generate Ubuntu-specific deployment script
    $ubuntuScript = @"
#!/bin/bash
set -e

echo "Updating Ubuntu 24.04 system..."
sudo apt update && sudo apt upgrade -y

echo "Configuring services for Ubuntu..."
# Restart auth service instances
$(($Projects | ForEach-Object { "sudo systemctl restart auth-service@$_" }) -join "`n")

# Restart database service
sudo systemctl restart database-service

# Wait for Ubuntu services to start
sleep 10

# Verify deployments
$(($Projects | ForEach-Object { "curl -f http://localhost:8082/api/health?project=$_ || echo 'Health check failed for $_'" }) -join "`n")

echo "Ubuntu 24.04 deployment verification complete"
sudo systemctl status auth-service database-service --no-pager
"@

    # Copy configurations and deploy to Ubuntu
    foreach ($project in $Projects) {
        Write-Host "Configuring Ubuntu project: $project" -ForegroundColor Yellow
        scp -i $KeyPath "config/projects/$project-auth.json" "${Username}@${ServerHost}:/opt/auth-service/config/"
        scp -i $KeyPath "config/projects/$project-db.json" "${Username}@${ServerHost}:/opt/database-service/config/"
    }

    # Execute Ubuntu deployment
    $ubuntuScript | ssh -i $KeyPath "${Username}@${ServerHost}" "bash -s"

    Write-Host "Multi-project deployment to Ubuntu 24.04 completed!" -ForegroundColor Green
}

function Show-UbuntuMultiProjectMenu {
    Write-Host "`n=== Ubuntu 24.04 Multi-Project Management ===" -ForegroundColor Cyan
    Write-Host "20. Deploy All Projects (Ubuntu)"
    Write-Host "21. Setup New Project (Ubuntu)"
    Write-Host "22. Migrate Existing Project (Ubuntu)"
    Write-Host "23. Check Ubuntu Service Status"
    Write-Host "24. View Ubuntu Project Configurations"
    Write-Host "25. Update Ubuntu Dependencies"
}
```

## Ubuntu 24.04 Specific Considerations

### System Requirements and Optimizations

#### Minimum Hardware Requirements
```bash
# Recommended Ubuntu 24.04 server specifications
CPU: 2+ cores (4+ recommended for production)
RAM: 4GB minimum (8GB+ recommended for production)
Storage: 20GB+ SSD (50GB+ for production with logs)
Network: 1Gbps connection recommended
```

#### Ubuntu 24.04 Package Dependencies
```bash
# Core system packages (automatically installed)
sudo apt install -y ubuntu-server-minimal
sudo apt install -y software-properties-common apt-transport-https

# Development and build tools
sudo apt install -y build-essential cmake ninja-build
sudo apt install -y gcc-13 g++-13 # Latest GCC with C++23 support
sudo apt install -y pkg-config git curl wget

# Database and networking
sudo apt install -y postgresql-16 postgresql-client-16 postgresql-contrib-16
sudo apt install -y libpq-dev libssl-dev libcurl4-openssl-dev

# Security and authentication libraries
sudo apt install -y libargon2-dev libsodium-dev
sudo apt install -y libjwt-dev # If available, otherwise build from source

# JSON and HTTP libraries
sudo apt install -y libnlohmann-json3-dev
sudo apt install -y libcrow-dev # If available in Ubuntu 24.04 repos

# System monitoring and management
sudo apt install -y htop iotop nethogs
sudo apt install -y systemd-journal-remote logrotate
sudo apt install -y ufw fail2ban
```

#### Ubuntu 24.04 Security Hardening
```bash
#!/bin/bash
# ubuntu-security-setup.sh - Harden Ubuntu 24.04 for production

set -e

echo "Hardening Ubuntu 24.04 for authentication service deployment..."

# Update system
sudo apt update && sudo apt upgrade -y

# Configure automatic security updates
sudo apt install -y unattended-upgrades
sudo dpkg-reconfigure -plow unattended-upgrades

# Configure UFW firewall
sudo ufw --force reset
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80/tcp   # HTTP (for Let's Encrypt)
sudo ufw allow 443/tcp  # HTTPS
sudo ufw allow 8081/tcp # Database Service (internal)
sudo ufw allow 8082/tcp # Auth Service (internal)
sudo ufw --force enable

# Configure fail2ban for SSH protection
sudo systemctl enable fail2ban
sudo systemctl start fail2ban

# Secure shared memory
echo "tmpfs /run/shm tmpfs defaults,noexec,nosuid 0 0" | sudo tee -a /etc/fstab

# Configure system limits for services
sudo tee /etc/security/limits.d/auth-service.conf << EOF
auth-service soft nofile 65536
auth-service hard nofile 65536
auth-service soft nproc 32768
auth-service hard nproc 32768
EOF

# Configure kernel parameters for network performance
sudo tee /etc/sysctl.d/99-auth-service.conf << EOF
# Network performance tuning
net.core.rmem_max = 16777216
net.core.wmem_max = 16777216
net.ipv4.tcp_rmem = 4096 87380 16777216
net.ipv4.tcp_wmem = 4096 65536 16777216
net.core.netdev_max_backlog = 5000

# Security hardening
net.ipv4.conf.all.send_redirects = 0
net.ipv4.conf.default.send_redirects = 0
net.ipv4.conf.all.accept_redirects = 0
net.ipv4.conf.default.accept_redirects = 0
net.ipv4.conf.all.accept_source_route = 0
net.ipv4.conf.default.accept_source_route = 0
EOF

sudo sysctl -p /etc/sysctl.d/99-auth-service.conf

echo "Ubuntu 24.04 security hardening completed!"
```

#### PostgreSQL 17 Configuration for Ubuntu 24.04
```bash
#!/bin/bash
# setup-postgresql-ubuntu.sh - Configure PostgreSQL 17 on Ubuntu 24.04

set -e

echo "Configuring PostgreSQL 17 for Ubuntu 24.04..."

# Install PostgreSQL 17 (latest version, already installed on git.chcit.org)
sudo apt install -y postgresql-17 postgresql-client-17 postgresql-contrib-17

# Configure PostgreSQL for authentication service
sudo -u postgres psql << EOF
-- Create database service user
CREATE USER database_service WITH PASSWORD 'password123';
ALTER USER database_service CREATEDB;
ALTER USER database_service WITH SUPERUSER;

-- Create auth service database
CREATE DATABASE auth_service OWNER database_service;

-- Configure connection limits
ALTER USER database_service CONNECTION LIMIT 100;

-- Grant necessary permissions
GRANT ALL PRIVILEGES ON DATABASE auth_service TO database_service;
EOF

# Configure PostgreSQL settings for Ubuntu 24.04
sudo tee -a /etc/postgresql/16/main/postgresql.conf << EOF

# Authentication service optimizations
max_connections = 200
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB

# Logging configuration
log_destination = 'stderr'
logging_collector = on
log_directory = '/var/log/postgresql'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_statement = 'mod'
log_min_duration_statement = 1000

# Security settings
ssl = on
ssl_cert_file = '/etc/ssl/certs/ssl-cert-snakeoil.pem'
ssl_key_file = '/etc/ssl/private/ssl-cert-snakeoil.key'
EOF

# Configure client authentication
sudo tee /etc/postgresql/16/main/pg_hba.conf << EOF
# TYPE  DATABASE        USER            ADDRESS                 METHOD
local   all             postgres                                peer
local   all             all                                     peer
host    all             all             127.0.0.1/32            md5
host    all             all             ::1/128                 md5
host    auth_service    database_service 127.0.0.1/32          md5
EOF

# Restart PostgreSQL
sudo systemctl restart postgresql
sudo systemctl enable postgresql

# Verify installation
sudo -u postgres psql -c "SELECT version();"

echo "PostgreSQL 17 configured successfully on Ubuntu 24.04!"
```

#### Systemd Service Configuration for Ubuntu 24.04
```ini
# /etc/systemd/system/auth-service.service - Ubuntu 24.04 optimized
[Unit]
Description=Authentication Service (C++23)
Documentation=file:///opt/auth-service/docs/
After=network-online.target postgresql.service
Wants=network-online.target
Requires=postgresql.service

[Service]
Type=simple
User=auth-service
Group=auth-service
WorkingDirectory=/opt/auth-service

# Ubuntu 24.04 specific paths
ExecStart=/opt/auth-service/bin/auth-service --config=/opt/auth-service/config/production.json
ExecReload=/bin/kill -HUP $MAINPID

# Restart configuration
Restart=always
RestartSec=5
StartLimitInterval=60
StartLimitBurst=3

# Resource limits for Ubuntu 24.04
LimitNOFILE=65536
LimitNPROC=32768
LimitMEMLOCK=infinity

# Security settings (Ubuntu 24.04 systemd features)
NoNewPrivileges=true
PrivateTmp=true
PrivateDevices=true
ProtectHome=true
ProtectSystem=strict
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectControlGroups=true
RestrictRealtime=true
RestrictNamespaces=true

# Writable directories
ReadWritePaths=/opt/auth-service/logs
ReadWritePaths=/opt/auth-service/data
ReadWritePaths=/var/log/auth-service

# Environment
Environment=LANG=C.UTF-8
Environment=LC_ALL=C.UTF-8

[Install]
WantedBy=multi-user.target
```

## Summary: Multi-Project Architecture Benefits

### 🎯 **Reusability Advantages**
- **Single Codebase**: Same binaries work across different projects and servers
- **Configuration-Driven**: Project-specific behavior through configuration files
- **Scalable Deployment**: From single-server to distributed multi-server architectures
- **Environment Agnostic**: Works with different IP addresses, domains, and network setups

### 🔧 **Technical Benefits**
- **Database Isolation**: Each project gets its own schemas and connection pools
- **Security Isolation**: Project-scoped authentication and authorization
- **Performance Isolation**: Independent resource allocation per project
- **Monitoring Granularity**: Per-project metrics and health monitoring

### 🚀 **Operational Benefits**
- **Easy Project Onboarding**: Automated setup scripts for new projects
- **Simplified Maintenance**: Single service update affects all projects
- **Consistent Security**: Uniform security policies across all deployments
- **Centralized Management**: Single point of control for multiple projects

This comprehensive migration plan provides a detailed roadmap for implementing secure, scalable, and reusable authentication and database services using C++23 and Argon2id, designed for multi-project, multi-database, and multi-server deployments across different environments and organizations.
