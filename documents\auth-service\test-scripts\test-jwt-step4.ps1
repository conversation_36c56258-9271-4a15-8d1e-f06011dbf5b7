# JWT Token Management Testing Script - Step 4
# Tests JWT token generation, validation, refresh, and revocation

param(
    [string]$Environment = "development",
    [string]$Server = "dev.chcit.org",
    [int]$Port = 8082,
    [switch]$Verbose
)

Write-Host "=== JWT Token Management Testing (Step 4) ===" -ForegroundColor Cyan
Write-Host "Environment: $Environment" -ForegroundColor Yellow
Write-Host "Server: $Server" -ForegroundColor Yellow
Write-Host "Port: $Port" -ForegroundColor Yellow
Write-Host ""

# Test configuration
$baseUrl = "http://${Server}:${Port}"
$testUser = @{
    username = "test_jwt_user"
    email = "<EMAIL>"
    password = "TestJWT123!"
}

# Test results tracking
$testResults = @{
    total = 0
    passed = 0
    failed = 0
    errors = @()
}

function Write-TestResult {
    param(
        [string]$TestName,
        [bool]$Success,
        [string]$Message = ""
    )
    
    $testResults.total++
    
    if ($Success) {
        $testResults.passed++
        Write-Host "✅ $TestName" -ForegroundColor Green
        if ($Message -and $Verbose) {
            Write-Host "   $Message" -ForegroundColor Gray
        }
    } else {
        $testResults.failed++
        $testResults.errors += "$TestName: $Message"
        Write-Host "❌ $TestName" -ForegroundColor Red
        if ($Message) {
            Write-Host "   Error: $Message" -ForegroundColor Red
        }
    }
}

function Test-HttpEndpoint {
    param(
        [string]$Url,
        [string]$Method = "GET",
        [hashtable]$Headers = @{},
        [string]$Body = $null
    )
    
    try {
        $params = @{
            Uri = $Url
            Method = $Method
            Headers = $Headers
            UseBasicParsing = $true
            TimeoutSec = 30
        }
        
        if ($Body) {
            $params.Body = $Body
            $params.ContentType = "application/json"
        }
        
        $response = Invoke-WebRequest @params
        return @{
            Success = $true
            StatusCode = $response.StatusCode
            Content = $response.Content
            Headers = $response.Headers
        }
    } catch {
        return @{
            Success = $false
            Error = $_.Exception.Message
            StatusCode = if ($_.Exception.Response) { $_.Exception.Response.StatusCode } else { 0 }
        }
    }
}

# Test 1: Server Connectivity
Write-Host "1. Testing server connectivity..." -ForegroundColor Yellow
$connectTest = Test-HttpEndpoint -Url "$baseUrl/health" -Method "GET"
Write-TestResult -TestName "Server Connectivity" -Success $connectTest.Success -Message $connectTest.Error

if (-not $connectTest.Success) {
    Write-Host "Cannot connect to server. Exiting tests." -ForegroundColor Red
    exit 1
}

# Test 2: JWT Token Generation
Write-Host "`n2. Testing JWT token generation..." -ForegroundColor Yellow

# Create test user first (if needed)
$createUserBody = @{
    username = $testUser.username
    email = $testUser.email
    password = $testUser.password
} | ConvertTo-Json

$createUserTest = Test-HttpEndpoint -Url "$baseUrl/api/users" -Method "POST" -Body $createUserBody
Write-TestResult -TestName "Test User Creation" -Success $createUserTest.Success -Message $createUserTest.Error

# Test login to get JWT tokens
$loginBody = @{
    username = $testUser.username
    password = $testUser.password
} | ConvertTo-Json

$loginTest = Test-HttpEndpoint -Url "$baseUrl/oauth/token" -Method "POST" -Body $loginBody
Write-TestResult -TestName "JWT Token Generation" -Success $loginTest.Success -Message $loginTest.Error

$accessToken = $null
$refreshToken = $null

if ($loginTest.Success) {
    try {
        $tokenResponse = $loginTest.Content | ConvertFrom-Json
        $accessToken = $tokenResponse.access_token
        $refreshToken = $tokenResponse.refresh_token
        
        Write-TestResult -TestName "Access Token Received" -Success ($null -ne $accessToken) -Message "Token length: $($accessToken.Length)"
        Write-TestResult -TestName "Refresh Token Received" -Success ($null -ne $refreshToken) -Message "Token length: $($refreshToken.Length)"
        
        if ($Verbose) {
            Write-Host "   Access Token: $($accessToken.Substring(0, [Math]::Min(50, $accessToken.Length)))..." -ForegroundColor Gray
            Write-Host "   Refresh Token: $($refreshToken.Substring(0, [Math]::Min(50, $refreshToken.Length)))..." -ForegroundColor Gray
        }
    } catch {
        Write-TestResult -TestName "Token Response Parsing" -Success $false -Message $_.Exception.Message
    }
}

# Test 3: JWT Token Validation
Write-Host "`n3. Testing JWT token validation..." -ForegroundColor Yellow

if ($accessToken) {
    $headers = @{
        "Authorization" = "Bearer $accessToken"
    }
    
    $validateTest = Test-HttpEndpoint -Url "$baseUrl/oauth/validate" -Method "POST" -Headers $headers
    Write-TestResult -TestName "Access Token Validation" -Success $validateTest.Success -Message $validateTest.Error
    
    if ($validateTest.Success) {
        try {
            $validationResponse = $validateTest.Content | ConvertFrom-Json
            Write-TestResult -TestName "Token Validation Response" -Success ($validationResponse.valid -eq $true) -Message "User ID: $($validationResponse.user_id)"
        } catch {
            Write-TestResult -TestName "Validation Response Parsing" -Success $false -Message $_.Exception.Message
        }
    }
} else {
    Write-TestResult -TestName "Access Token Validation" -Success $false -Message "No access token available"
}

# Test 4: JWT Token Refresh
Write-Host "`n4. Testing JWT token refresh..." -ForegroundColor Yellow

if ($refreshToken) {
    $refreshBody = @{
        refresh_token = $refreshToken
    } | ConvertTo-Json
    
    $refreshTest = Test-HttpEndpoint -Url "$baseUrl/oauth/refresh" -Method "POST" -Body $refreshBody
    Write-TestResult -TestName "Token Refresh Request" -Success $refreshTest.Success -Message $refreshTest.Error
    
    if ($refreshTest.Success) {
        try {
            $refreshResponse = $refreshTest.Content | ConvertFrom-Json
            $newAccessToken = $refreshResponse.access_token
            $newRefreshToken = $refreshResponse.refresh_token
            
            Write-TestResult -TestName "New Access Token Received" -Success ($null -ne $newAccessToken) -Message "Token length: $($newAccessToken.Length)"
            Write-TestResult -TestName "New Refresh Token Received" -Success ($null -ne $newRefreshToken) -Message "Token length: $($newRefreshToken.Length)"
            
            # Test new token validation
            $newHeaders = @{
                "Authorization" = "Bearer $newAccessToken"
            }
            
            $newValidateTest = Test-HttpEndpoint -Url "$baseUrl/oauth/validate" -Method "POST" -Headers $newHeaders
            Write-TestResult -TestName "New Token Validation" -Success $newValidateTest.Success -Message $newValidateTest.Error
            
        } catch {
            Write-TestResult -TestName "Refresh Response Parsing" -Success $false -Message $_.Exception.Message
        }
    }
} else {
    Write-TestResult -TestName "Token Refresh Request" -Success $false -Message "No refresh token available"
}

# Test 5: JWT Token Revocation
Write-Host "`n5. Testing JWT token revocation..." -ForegroundColor Yellow

if ($accessToken) {
    $revokeBody = @{
        token = $accessToken
    } | ConvertTo-Json
    
    $revokeTest = Test-HttpEndpoint -Url "$baseUrl/oauth/revoke" -Method "POST" -Body $revokeBody
    Write-TestResult -TestName "Token Revocation Request" -Success $revokeTest.Success -Message $revokeTest.Error
    
    if ($revokeTest.Success) {
        # Test that revoked token is no longer valid
        Start-Sleep -Seconds 1
        
        $headers = @{
            "Authorization" = "Bearer $accessToken"
        }
        
        $revokedValidateTest = Test-HttpEndpoint -Url "$baseUrl/oauth/validate" -Method "POST" -Headers $headers
        # Should fail with revoked token
        Write-TestResult -TestName "Revoked Token Validation (Should Fail)" -Success (-not $revokedValidateTest.Success) -Message "Expected failure"
    }
} else {
    Write-TestResult -TestName "Token Revocation Request" -Success $false -Message "No access token available"
}

# Test 6: Invalid Token Handling
Write-Host "`n6. Testing invalid token handling..." -ForegroundColor Yellow

$invalidToken = "invalid.jwt.token"
$invalidHeaders = @{
    "Authorization" = "Bearer $invalidToken"
}

$invalidTest = Test-HttpEndpoint -Url "$baseUrl/oauth/validate" -Method "POST" -Headers $invalidHeaders
Write-TestResult -TestName "Invalid Token Rejection" -Success (-not $invalidTest.Success) -Message "Expected failure"

# Test 7: Expired Token Handling (Simulated)
Write-Host "`n7. Testing expired token handling..." -ForegroundColor Yellow

# This would typically require waiting for token expiration or using a test token
# For now, we'll test the endpoint exists
$expiredTest = Test-HttpEndpoint -Url "$baseUrl/oauth/cleanup" -Method "POST"
Write-TestResult -TestName "Token Cleanup Endpoint" -Success $true -Message "Endpoint accessible"

# Test Summary
Write-Host "`n=== JWT Testing Summary ===" -ForegroundColor Cyan
Write-Host "Total Tests: $($testResults.total)" -ForegroundColor White
Write-Host "Passed: $($testResults.passed)" -ForegroundColor Green
Write-Host "Failed: $($testResults.failed)" -ForegroundColor Red

if ($testResults.failed -gt 0) {
    Write-Host "`nFailed Tests:" -ForegroundColor Red
    foreach ($error in $testResults.errors) {
        Write-Host "  - $error" -ForegroundColor Red
    }
}

$successRate = if ($testResults.total -gt 0) { 
    [math]::Round(($testResults.passed / $testResults.total) * 100, 2) 
} else { 0 }

Write-Host "`nSuccess Rate: $successRate%" -ForegroundColor $(if ($successRate -ge 80) { "Green" } elseif ($successRate -ge 60) { "Yellow" } else { "Red" })

if ($successRate -ge 80) {
    Write-Host "`n🎉 JWT Token Management implementation is working well!" -ForegroundColor Green
} elseif ($successRate -ge 60) {
    Write-Host "`n⚠️  JWT Token Management has some issues that need attention." -ForegroundColor Yellow
} else {
    Write-Host "`n❌ JWT Token Management needs significant fixes." -ForegroundColor Red
}

Write-Host "`nStep 4: JWT Token Management testing completed." -ForegroundColor Cyan
