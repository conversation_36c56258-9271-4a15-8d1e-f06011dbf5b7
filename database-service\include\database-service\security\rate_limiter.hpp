#pragma once

#include <string>
#include <unordered_map>
#include <unordered_set>
#include <chrono>
#include <mutex>
#include <memory>
#include <expected>
#include <nlohmann/json.hpp>

namespace dbservice::security {

// Rate limiting configuration
struct RateLimitConfig {
    int requestsPerMinute;
    int requestsPerHour;
    int requestsPerDay;
    int burstLimit;
    std::chrono::seconds windowSize;
};

// Rate limiting result
struct RateLimitResult {
    bool allowed;
    int remainingRequests;
    std::chrono::system_clock::time_point resetTime;
    std::string reason;
};

// Client request tracking
struct ClientTracker {
    std::string clientId;
    int requestCount;
    std::chrono::system_clock::time_point windowStart;
    std::chrono::system_clock::time_point lastRequest;
    int burstCount;
    std::chrono::system_clock::time_point burstStart;
};

/**
 * @brief Rate limiter for API protection
 * 
 * The RateLimiter provides:
 * - Per-client rate limiting
 * - Per-endpoint rate limiting
 * - Burst protection
 * - Sliding window rate limiting
 * - Configurable limits per endpoint
 * - IP-based and API key-based limiting
 */
class RateLimiter {
public:
    /**
     * @brief Constructor
     */
    RateLimiter();

    /**
     * @brief Destructor
     */
    ~RateLimiter() = default;

    // Disable copy constructor and assignment operator
    RateLimiter(const RateLimiter&) = delete;
    RateLimiter& operator=(const RateLimiter&) = delete;

    /**
     * @brief Initialize the rate limiter
     * @return true if initialization successful, false otherwise
     */
    bool initialize();

    /**
     * @brief Check if request is allowed for client and endpoint
     * @param clientId Client identifier (IP address or API key)
     * @param endpoint API endpoint
     * @param userAgent Client user agent (optional)
     * @return Rate limit result
     */
    RateLimitResult checkLimit(
        const std::string& clientId,
        const std::string& endpoint,
        const std::string& userAgent = ""
    );

    /**
     * @brief Configure rate limit for specific endpoint
     * @param endpoint API endpoint
     * @param config Rate limit configuration
     * @return true if successful, false otherwise
     */
    bool configureEndpoint(const std::string& endpoint, const RateLimitConfig& config);

    /**
     * @brief Configure global rate limits
     * @param config Global rate limit configuration
     * @return true if successful, false otherwise
     */
    bool configureGlobal(const RateLimitConfig& config);

    /**
     * @brief Add client to whitelist (bypass rate limiting)
     * @param clientId Client identifier
     * @return true if successful, false otherwise
     */
    bool addToWhitelist(const std::string& clientId);

    /**
     * @brief Remove client from whitelist
     * @param clientId Client identifier
     * @return true if successful, false otherwise
     */
    bool removeFromWhitelist(const std::string& clientId);

    /**
     * @brief Add client to blacklist (block all requests)
     * @param clientId Client identifier
     * @param reason Reason for blacklisting
     * @param duration Duration of blacklist (0 for permanent)
     * @return true if successful, false otherwise
     */
    bool addToBlacklist(
        const std::string& clientId, 
        const std::string& reason,
        std::chrono::seconds duration = std::chrono::seconds(0)
    );

    /**
     * @brief Remove client from blacklist
     * @param clientId Client identifier
     * @return true if successful, false otherwise
     */
    bool removeFromBlacklist(const std::string& clientId);

    /**
     * @brief Get rate limiting statistics
     * @return Statistics as JSON
     */
    nlohmann::json getStatistics();

    /**
     * @brief Get client statistics
     * @param clientId Client identifier
     * @return Client statistics as JSON
     */
    nlohmann::json getClientStatistics(const std::string& clientId);

    /**
     * @brief Reset rate limits for client
     * @param clientId Client identifier
     * @return true if successful, false otherwise
     */
    bool resetClientLimits(const std::string& clientId);

    /**
     * @brief Clean up expired tracking data
     * @return Number of cleaned up entries
     */
    int cleanup();

    /**
     * @brief Check if client is whitelisted
     * @param clientId Client identifier
     * @return true if whitelisted, false otherwise
     */
    bool isWhitelisted(const std::string& clientId);

    /**
     * @brief Check if client is blacklisted
     * @param clientId Client identifier
     * @return true if blacklisted, false otherwise
     */
    bool isBlacklisted(const std::string& clientId);

private:
    mutable std::mutex mutex_;
    bool initialized_;

    // Rate limit configurations
    RateLimitConfig globalConfig_;
    std::unordered_map<std::string, RateLimitConfig> endpointConfigs_;

    // Client tracking
    std::unordered_map<std::string, ClientTracker> clientTrackers_;

    // Whitelist and blacklist
    std::unordered_set<std::string> whitelist_;
    std::unordered_map<std::string, std::pair<std::string, std::chrono::system_clock::time_point>> blacklist_;

    // Statistics
    struct Statistics {
        int totalRequests;
        int allowedRequests;
        int blockedRequests;
        int whitelistHits;
        int blacklistHits;
        std::chrono::system_clock::time_point startTime;
    } stats_;

    /**
     * @brief Get or create client tracker
     * @param clientId Client identifier
     * @return Reference to client tracker
     */
    ClientTracker& getClientTracker(const std::string& clientId);

    /**
     * @brief Get rate limit configuration for endpoint
     * @param endpoint API endpoint
     * @return Rate limit configuration
     */
    const RateLimitConfig& getEndpointConfig(const std::string& endpoint);

    /**
     * @brief Check sliding window rate limit
     * @param tracker Client tracker
     * @param config Rate limit configuration
     * @param now Current time
     * @return true if allowed, false otherwise
     */
    bool checkSlidingWindow(ClientTracker& tracker, const RateLimitConfig& config, const std::chrono::system_clock::time_point& now);

    /**
     * @brief Check burst limit
     * @param tracker Client tracker
     * @param config Rate limit configuration
     * @param now Current time
     * @return true if allowed, false otherwise
     */
    bool checkBurstLimit(ClientTracker& tracker, const RateLimitConfig& config, const std::chrono::system_clock::time_point& now);

    /**
     * @brief Update client tracker with new request
     * @param tracker Client tracker
     * @param now Current time
     */
    void updateTracker(ClientTracker& tracker, const std::chrono::system_clock::time_point& now);

    /**
     * @brief Calculate reset time for client
     * @param tracker Client tracker
     * @param config Rate limit configuration
     * @return Reset time
     */
    std::chrono::system_clock::time_point calculateResetTime(const ClientTracker& tracker, const RateLimitConfig& config);

    /**
     * @brief Check if blacklist entry is expired
     * @param entry Blacklist entry
     * @param now Current time
     * @return true if expired, false otherwise
     */
    bool isBlacklistExpired(const std::pair<std::string, std::chrono::system_clock::time_point>& entry, const std::chrono::system_clock::time_point& now);
};

} // namespace dbservice::security
