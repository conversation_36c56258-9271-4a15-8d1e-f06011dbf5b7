// Simple Database Connection Test
// Step 6: Database Operations Integration

#include <iostream>
#include <pqxx/pqxx>

int main() {
    try {
        std::cout << "Testing PostgreSQL database connection..." << std::endl;
        
        // Connection string for auth_service database
        std::string conn_str = "host=localhost port=5432 dbname=auth_service user=postgres connect_timeout=10";
        
        std::cout << "Connection string: " << conn_str << std::endl;
        
        // Create connection
        pqxx::connection conn(conn_str);
        
        if (conn.is_open()) {
            std::cout << "✅ Database connection successful!" << std::endl;
            std::cout << "Connected to: " << conn.dbname() << std::endl;
            
            // Test query
            pqxx::work txn(conn);
            pqxx::result result = txn.exec("SELECT version()");
            txn.commit();
            
            if (!result.empty()) {
                std::cout << "✅ Database query successful!" << std::endl;
                std::cout << "PostgreSQL version: " << result[0][0].c_str() << std::endl;
            }
            
            // Check tables
            pqxx::work txn2(conn);
            pqxx::result tables = txn2.exec("SELECT tablename FROM pg_tables WHERE schemaname = 'public'");
            txn2.commit();
            
            std::cout << "\n📋 Available tables:" << std::endl;
            for (const auto& row : tables) {
                std::cout << "  - " << row[0].c_str() << std::endl;
            }
            
            // Check auth_users structure
            pqxx::work txn3(conn);
            pqxx::result users = txn3.exec("SELECT COUNT(*) FROM auth_users");
            txn3.commit();
            
            std::cout << "\n👥 Users in auth_users table: " << users[0][0].c_str() << std::endl;
            
        } else {
            std::cout << "❌ Failed to establish database connection" << std::endl;
            return 1;
        }
        
    } catch (const std::exception& e) {
        std::cout << "❌ Database connection error: " << e.what() << std::endl;
        return 1;
    }
    
    std::cout << "\n🎉 Database connection test completed successfully!" << std::endl;
    return 0;
}
