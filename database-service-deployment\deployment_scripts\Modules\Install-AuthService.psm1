# Install-AuthService.psm1 - Module for installing the auth service

<#
.SYNOPSIS
    Provides functionality for installing the auth service as a systemd service.

.DESCRIPTION
    This module handles the installation process for the auth service,
    including copying binaries, creating service files, and setting up permissions.

.NOTES
    File Name      : Install-AuthService.psm1
    Author         : Auth Service Deployment Team
    Prerequisite   : PowerShell 5.1 or later
    Copyright      : (c) 2025 Augment
#>

# Import required modules
try {
    # Import common module
    if (-not (Get-Module -Name "Common")) {
        Import-Module -Name "$PSScriptRoot\..\Common.psm1" -Force -ErrorAction Stop
    }

    # Import Logger module
    if (-not (Get-Module -Name "Logger")) {
        Import-Module -Name "$PSScriptRoot\Logger\Logger.psm1" -Force -ErrorAction Stop
    }

    # Import SSHManager module
    if (-not (Get-Module -Name "SSHManager")) {
        Import-Module -Name "$PSScriptRoot\SSHManager.psm1" -Force -ErrorAction Stop
    }
} catch {
    Write-Host "Error loading required modules: $_" -ForegroundColor Red
}

function Install-AuthService {
    [CmdletBinding()]
    param()

    Write-Host "=== Installing Auth Service ===" -ForegroundColor Cyan
    Write-Host ""

    # Get configuration
    $installDir = $script:Config.project.remote_install_dir
    $buildDir = $script:Config.project.remote_build_dir
    $serviceName = $script:Config.service.name
    $serviceUser = $script:Config.service.user

    Write-Host "Installation Configuration:" -ForegroundColor Yellow
    Write-Host "  Install Directory: $installDir" -ForegroundColor White
    Write-Host "  Build Directory: $buildDir" -ForegroundColor White
    Write-Host "  Service Name: $serviceName" -ForegroundColor White
    Write-Host "  Service User: $serviceUser" -ForegroundColor White
    Write-Host ""

    # Create service user and group
    Write-Host "Creating service user and group..." -ForegroundColor Yellow
    
    $createUserCmd = "sudo useradd -r -s /bin/false $serviceUser 2>/dev/null || echo 'User already exists'"
    
    try {
        $sshCommand = "ssh -i `"$($script:Config.ssh.key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$($script:Config.ssh.host) `"$createUserCmd`""
        $result = Invoke-Expression $sshCommand
        Write-Host "✅ Service user created or already exists" -ForegroundColor Green
    } catch {
        Write-Host "❌ Error creating service user: $_" -ForegroundColor Red
        return $false
    }

    # Create installation directories
    Write-Host "Creating installation directories..." -ForegroundColor Yellow
    
    $createDirsCmd = "sudo mkdir -p $installDir/bin $installDir/config $installDir/logs && sudo chown -R $serviceUser`:$serviceUser $installDir"
    
    try {
        $sshCommand = "ssh -i `"$($script:Config.ssh.key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$($script:Config.ssh.host) `"$createDirsCmd`""
        $result = Invoke-Expression $sshCommand
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Installation directories created" -ForegroundColor Green
        } else {
            Write-Host "❌ Failed to create installation directories" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ Error creating directories: $_" -ForegroundColor Red
        return $false
    }

    # Copy executable from build directory
    Write-Host "Copying auth service executable..." -ForegroundColor Yellow
    
    $copyExeCmd = "sudo cp $buildDir/build/auth-service $installDir/bin/ && sudo chown $serviceUser`:$serviceUser $installDir/bin/auth-service && sudo chmod +x $installDir/bin/auth-service"
    
    try {
        $sshCommand = "ssh -i `"$($script:Config.ssh.key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$($script:Config.ssh.host) `"$copyExeCmd`""
        $result = Invoke-Expression $sshCommand
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Executable copied successfully" -ForegroundColor Green
        } else {
            Write-Host "❌ Failed to copy executable" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ Error copying executable: $_" -ForegroundColor Red
        return $false
    }

    # Copy configuration files
    Write-Host "Copying configuration files..." -ForegroundColor Yellow
    
    $copyConfigCmd = "sudo cp $buildDir/source/config/auth-service.conf $installDir/config/ && sudo chown $serviceUser`:$serviceUser $installDir/config/auth-service.conf"
    
    try {
        $sshCommand = "ssh -i `"$($script:Config.ssh.key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$($script:Config.ssh.host) `"$copyConfigCmd`""
        $result = Invoke-Expression $sshCommand
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Configuration files copied successfully" -ForegroundColor Green
        } else {
            Write-Host "❌ Failed to copy configuration files" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ Error copying configuration: $_" -ForegroundColor Red
        return $false
    }

    # Create systemd service file
    Write-Host "Creating systemd service file..." -ForegroundColor Yellow
    
    $serviceFileContent = @"
[Unit]
Description=Auth Service
After=network.target postgresql.service
Wants=postgresql.service

[Service]
Type=simple
User=$serviceUser
Group=$serviceUser
WorkingDirectory=$installDir
ExecStart=$installDir/bin/auth-service --config=$installDir/config/auth-service.conf
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
"@

    $createServiceCmd = "sudo tee /etc/systemd/system/$serviceName.service > /dev/null << 'EOF'`n$serviceFileContent`nEOF"
    
    try {
        $sshCommand = "ssh -i `"$($script:Config.ssh.key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$($script:Config.ssh.host) `"$createServiceCmd`""
        $result = Invoke-Expression $sshCommand
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Systemd service file created" -ForegroundColor Green
        } else {
            Write-Host "❌ Failed to create systemd service file" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ Error creating service file: $_" -ForegroundColor Red
        return $false
    }

    # Reload systemd and enable service
    Write-Host "Enabling auth service..." -ForegroundColor Yellow
    
    $enableServiceCmd = "sudo systemctl daemon-reload && sudo systemctl enable $serviceName"
    
    try {
        $sshCommand = "ssh -i `"$($script:Config.ssh.key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$($script:Config.ssh.host) `"$enableServiceCmd`""
        $result = Invoke-Expression $sshCommand
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Auth service enabled successfully" -ForegroundColor Green
        } else {
            Write-Host "❌ Failed to enable auth service" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ Error enabling service: $_" -ForegroundColor Red
        return $false
    }

    Write-Host ""
    Write-Host "✅ Auth service installation completed successfully!" -ForegroundColor Green
    Write-Host "Use 'sudo systemctl start $serviceName' to start the service" -ForegroundColor Cyan
    return $true
}

# Export the function
Export-ModuleMember -Function Install-AuthService
