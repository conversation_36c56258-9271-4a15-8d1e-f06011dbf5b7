#!/usr/bin/env powershell
# Auth Service Deployment Script
# Version: 1.0.0
# Last Updated: 2025-06-24
# Author: CHCIT DevOps Team
#
# This script automates the deployment of the Auth Service from Windows build servers
# to Ubuntu targets using SSH and SCP for file transfer.

param(
    [Parameter(Mandatory=$false)]
    [string]$Environment = "development",
    
    [Parameter(Mandatory=$false)]
    [string]$ConfigFile,
    
    [Parameter(Mandatory=$false)]
    [switch]$SkipDependencies,
    
    [Parameter(Mandatory=$false)]
    [switch]$SkipBuild,
    
    [Parameter(Mandatory=$false)]
    [switch]$SkipTests,
    
    [Parameter(Mandatory=$false)]
    [switch]$VerboseOutput,

    [Parameter(Mandatory=$false)]
    [switch]$DebugMode
)

# Set error action preference
$ErrorActionPreference = "Continue"

# Set script root and working directory
$PSScriptRoot = Split-Path -Parent -Path $MyInvocation.MyCommand.Definition
Set-Location $PSScriptRoot

# Initialize logging first
$logDir = Join-Path $PSScriptRoot "logs"
if (-not (Test-Path $logDir)) {
    New-Item -ItemType Directory -Path $logDir -Force | Out-Null
}

$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$logFile = Join-Path $logDir "auth-service_$timestamp.log"

# Import Logger module first
try {
    Import-Module "$PSScriptRoot\Modules\Logger\Logger.psm1" -Force -ErrorAction Stop
    Initialize-Logger -LogFilePath $logFile -Level $(if ($DebugMode) { "Debug" } else { "Info" })

    # Make Write-Log function globally available
    $writeLogFunction = Get-Command Write-Log -ErrorAction SilentlyContinue
    if ($writeLogFunction) {
        Set-Item -Path "function:global:Write-Log" -Value $writeLogFunction.ScriptBlock
    }

    Write-Log -Message "Logger initialized with level: $(if ($DebugMode) { 'Debug' } else { 'Info' })" -Level "Debug" -Component "Initialization"
    Write-Log -Message "Log file: $logFile" -Level "Debug" -Component "Initialization"
    Write-Host "True"
} catch {
    Write-Host "Failed to initialize logger: $_" -ForegroundColor Red
    Write-Host "False"
    exit 1
}

Write-Host "Logger initialized. Log file path: $logFile"

# Diagnostic information
Write-Host "[DIAG] PSScriptRoot: $PSScriptRoot" -ForegroundColor Magenta
$expectedLogsDir = Join-Path $PSScriptRoot "logs"
Write-Host "[DIAG] Expected logs directory: $expectedLogsDir" -ForegroundColor Magenta

# Import Common module
try {
    Import-Module "$PSScriptRoot\Common.psm1" -Force -ErrorAction Stop
    Write-Host "Configuration module loaded"
} catch {
    Write-Host "Failed to load Common module: $_" -ForegroundColor Red
    exit 1
}

# Load configuration
try {
    $configPath = if ($ConfigFile) { 
        $ConfigFile 
    } else { 
        Join-Path $PSScriptRoot "config\auth-service-$Environment.json" 
    }
    
    if (-not (Test-Path $configPath)) {
        throw "Configuration file not found: $configPath"
    }
    
    $script:Config = Get-Content $configPath | ConvertFrom-Json
    Write-Host "Configuration loaded successfully"
} catch {
    Write-Host "Failed to load configuration: $_" -ForegroundColor Red
    exit 1
}

Write-Host "Common module loaded"
Write-Host ""

# Module loading section
Write-Host "Loading functional modules..." -ForegroundColor Cyan
Write-Host ""

# Must clear existing modules to avoid circular dependencies
$coreMods = @('Configuration', 'Common', 'Edit-ProjectSettings', 'Manage-DeploymentConfigurations', 'SSHManager', 'Test-SSHConnection', 'Build-AuthService')
foreach ($mod in $coreMods) {
    if (Get-Module -Name $mod) {
        Write-Host "Removing module: $mod" -ForegroundColor Yellow
        Remove-Module -Name $mod -Force -ErrorAction SilentlyContinue
    }
}

# Function to safely import a module
function Import-ModuleSafely {
    param(
        [string]$ModulePath,
        [string]$ModuleName
    )
    
    try {
        if (Test-Path $ModulePath) {
            Import-Module $ModulePath -Force -Global -ErrorAction Stop

            # Special handling for Logger module to make Write-Log available globally
            if ($ModuleName -eq "Logger\Logger") {
                # Import Write-Log function into global scope
                $writeLogFunction = Get-Command Write-Log -ErrorAction SilentlyContinue
                if ($writeLogFunction) {
                    Set-Item -Path "function:global:Write-Log" -Value $writeLogFunction.ScriptBlock
                    Write-Host "  ✅ Write-Log function made globally available" -ForegroundColor Green
                }
            }

            return $true
        } else {
            Write-Host "  Module file not found: $ModulePath" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "  Failed to load $ModuleName`: $_" -ForegroundColor Red
        return $false
    }
}

# Define core modules that should be loaded first (in this specific order)
$coreModuleNames = @(
    "Logger\Logger",
    "SSHManager",
    "Test-SSHConnection"
)

# Define all other modules
$moduleNames = @(
    "Build-AuthService",
    "Edit-ProjectSettings",
    "Get-ServiceStatus", 
    "Initialize-AuthDatabase",
    "Install-Dependencies",
    "Install-Database-Dependencies",
    "Install-AuthService",
    "Invoke-CustomCommand",
    "Manage-ConfigurationBackups",
    "Manage-DatabaseSchemas",
    "Manage-DeploymentConfigurations",
    "Set-Environment",
    "Set-SSHKeys",
    "Setup-CertificateAccess",
    "Standardize-Configurations",
    "Start-AuthService",
    "Test-ServerReadiness",
    "Update-ServerConfig",
    "Build-AuthUI",
    "Deploy-AuthUI"
)

Write-Host "Loading core modules in specific order..." -ForegroundColor Yellow

$loadedCoreCount = 0
foreach ($moduleName in $coreModuleNames) {
    Write-Host "  Loading core module: $moduleName" -ForegroundColor White
    $modulePath = Join-Path $PSScriptRoot "Modules\$moduleName.psm1"
    
    if (Import-ModuleSafely -ModulePath $modulePath -ModuleName $moduleName) {
        Write-Host "  Successfully loaded core module: $moduleName" -ForegroundColor Green
        $loadedCoreCount++
    }
}

Write-Host "Successfully loaded $loadedCoreCount of $($coreModuleNames.Count) core modules" -ForegroundColor $(if ($loadedCoreCount -eq $coreModuleNames.Count) { "Green" } else { "Yellow" })
Write-Host ""

Write-Host "Loading remaining modules..." -ForegroundColor Yellow

$loadedCount = 0
$totalModules = $moduleNames.Count

foreach ($moduleName in $moduleNames) {
    Write-Host "  Loading: $moduleName" -ForegroundColor White
    $modulePath = Join-Path $PSScriptRoot "Modules\$moduleName.psm1"
    
    if (Import-ModuleSafely -ModulePath $modulePath -ModuleName $moduleName) {
        $loadedCount++
    }
}

Write-Host "Successfully loaded $loadedCount of $totalModules modules" -ForegroundColor $(if ($loadedCount -eq $totalModules) { "Green" } else { "Yellow" })

$script:CurrentBuildOutputPath = $null

# Validate function availability
$requiredFunctions = @(
    'Build-AuthService',
    'Edit-ProjectSettings',
    'Get-ServiceStatus',
    'Initialize-AuthDatabase', 
    'Install-Dependencies',
    'Install-Database-Dependencies',
    'Install-AuthService',
    'Invoke-CustomCommand',
    'Manage-DeploymentConfigurations',
    'Set-Environment',
    'Set-SSHKeys',
    'Setup-CertificateAccess',
    'Start-AuthService',
    'Test-SSHConnection',
    'Test-ServerReadiness',
    'Update-ServerConfig'
)

Write-Host "Authentication Service deployment script ready"

Write-Host "True"
Write-Host ""
Write-Host "Press Enter to continue to the main menu..." -ForegroundColor Yellow
Read-Host | Out-Null

# Main menu loop
do {
    Clear-Host
    Write-Host ""
    Write-Host "=== Authentication Service Deployment Main Menu ===" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "[1] Set Environment" -ForegroundColor White
    Write-Host "[2] Manage Deployment Configurations" -ForegroundColor White
    Write-Host "[3] Edit Project Settings" -ForegroundColor White
    Write-Host "[4] Set SSH Keys" -ForegroundColor White
    Write-Host "[5] Setup Certificate Access" -ForegroundColor White
    Write-Host "[6] Test SSH Connection" -ForegroundColor White
    Write-Host "[7] Test Server Readiness" -ForegroundColor White
    Write-Host "[8] Install Dependencies" -ForegroundColor White
    Write-Host ""
    Write-Host "[9] Build Project" -ForegroundColor Yellow
    Write-Host "[10] Install Systemd Service" -ForegroundColor Yellow
    Write-Host "[11] Install Database Dependencies" -ForegroundColor Yellow
    Write-Host "[12] Initialize Database" -ForegroundColor Yellow
    Write-Host "[13] Start Authentication Service" -ForegroundColor Yellow
    Write-Host "[14] Get Service Status" -ForegroundColor White
    Write-Host "[15] Update Server Config" -ForegroundColor White
    Write-Host "[16] Manage Configuration Backups" -ForegroundColor White
    Write-Host "[17] Invoke Custom Command" -ForegroundColor White
    Write-Host "[18] Build UI" -ForegroundColor Green
    Write-Host "[19] Deploy UI" -ForegroundColor Green
    Write-Host "[20] Manage Database Schemas" -ForegroundColor White
    Write-Host "[21] Initialize All Databases" -ForegroundColor White
    Write-Host "[L] View Log File" -ForegroundColor Gray
    Write-Host "[Q] Quit" -ForegroundColor Red
    Write-Host "Select an option:" -ForegroundColor Cyan -NoNewline

    $choice = Read-Host

    switch ($choice.ToUpper()) {
        '1' {
            Write-Host "[Menu] Set-Environment selected." -ForegroundColor Cyan
            Set-Environment
            Write-Host "[Menu] Set-Environment completed." -ForegroundColor Green
        }
        '2' {
            Write-Host "[Menu] Manage-DeploymentConfigurations selected." -ForegroundColor Cyan
            Manage-DeploymentConfigurations
            Write-Host "[Menu] Manage-DeploymentConfigurations completed." -ForegroundColor Green
        }
        '3' {
            Write-Host "[Menu] Edit-ProjectSettings selected." -ForegroundColor Cyan
            Edit-ProjectSettings
            Write-Host "[Menu] Edit-ProjectSettings completed." -ForegroundColor Green
        }
        '4' {
            Write-Host "[Menu] Set-SSHKeys selected." -ForegroundColor Cyan
            Set-SSHKeys
            Write-Host "[Menu] Set-SSHKeys completed." -ForegroundColor Green
        }
        '5' {
            Write-Host "[Menu] Setup-CertificateAccess selected." -ForegroundColor Cyan
            Setup-CertificateAccess
            Write-Host "[Menu] Setup-CertificateAccess completed." -ForegroundColor Green
        }
        '6' {
            Write-Host "[Menu] Test-SSHConnection selected." -ForegroundColor Cyan
            Test-SSHConnection
            Write-Host "[Menu] Test-SSHConnection completed." -ForegroundColor Green
        }
        '7' {
            Write-Host "[Menu] Test-ServerReadiness selected." -ForegroundColor Cyan
            Test-ServerReadiness
            Write-Host "[Menu] Test-ServerReadiness completed." -ForegroundColor Green
        }
        '8' {
            Write-Host "[Menu] Install-Dependencies selected." -ForegroundColor Cyan
            Install-Dependencies
            Write-Host "[Menu] Install-Dependencies completed." -ForegroundColor Green
        }
        '9' {
            Write-Host "[Menu] Build-AuthService selected." -ForegroundColor Cyan
            try {
                # Force reload the Build-AuthService module to ensure fresh import
                Import-Module "$PSScriptRoot\Modules\Build-AuthService.psm1" -Force -ErrorAction Stop
                Build-AuthService
                Write-Host "[Menu] Build-AuthService completed." -ForegroundColor Green
            } catch {
                Write-Host "Error in Build-AuthService: $_" -ForegroundColor Red
                Write-Host "Error details: $($_.Exception.ToString())" -ForegroundColor Red
                Write-Host "Press Enter to continue..." -ForegroundColor Yellow
                Read-Host | Out-Null
            }
        }
        'L' {
            Write-Host "[Menu] View Log File selected." -ForegroundColor Cyan

            # Get all possible log file locations
            $logPaths = @()

            # Try the main log path from logger
            $mainLogPath = Get-LogFilePath
            if ($mainLogPath) { $logPaths += $mainLogPath }

            # Check deployment_scripts/logs directory
            $scriptsLogDir = Join-Path -Path $PSScriptRoot -ChildPath "logs"
            if (Test-Path $scriptsLogDir) {
                $latestLog = Get-ChildItem -Path $scriptsLogDir -Filter "auth-service_*.log" |
                             Sort-Object LastWriteTime -Descending |
                             Select-Object -First 1 -ExpandProperty FullName
                if ($latestLog) { $logPaths += $latestLog }
            }

            # Check auth-service project logs directory
            $authServiceLogDir = "D:\Coding_Projects\auth-service\auth-service-deployment\deployment_scripts\logs"
            if (Test-Path $authServiceLogDir) {
                $authLatestLog = Get-ChildItem -Path $authServiceLogDir -Filter "deployment_*.log" |
                                 Sort-Object LastWriteTime -Descending |
                                 Select-Object -First 1 -ExpandProperty FullName
                if ($authLatestLog) { $logPaths += $authLatestLog }
            }

            Write-Host "[DIAG] Found these potential log files:" -ForegroundColor Yellow
            $logPaths | ForEach-Object { Write-Host "  $_" -ForegroundColor Yellow }

            # Try each log path until we find one that exists
            $foundLog = $false
            foreach ($logPath in $logPaths) {
                if (Test-Path $logPath) {
                    Write-Host "`nDisplaying log file: $logPath" -ForegroundColor Green
                    Write-Host "`n--- Last 40 lines of log ---`n" -ForegroundColor Yellow
                    Get-Content $logPath -Tail 40 | ForEach-Object { Write-Host $_ }
                    Write-Host "`n--- End of log ---`n" -ForegroundColor Yellow
                    $foundLog = $true
                    break
                }
            }

            if (-not $foundLog) {
                Write-Host "No log files found in any of the expected locations." -ForegroundColor Red
            }

            Write-Host ""
            Write-Host "Press Enter to continue..." -ForegroundColor Yellow
            Read-Host | Out-Null
        }
        'Q' {
            Write-Host "[Menu] Quit selected." -ForegroundColor Cyan
            Write-Host "Exiting..." -ForegroundColor Yellow
            break
        }
        default {
            Write-Host "Invalid option. Please try again." -ForegroundColor Red
            Start-Sleep -Seconds 1
        }
    }

    if ($choice.ToUpper() -ne 'Q') {
        Write-Host ""
        Write-Host "Press Enter to continue..." -ForegroundColor Yellow
        Read-Host | Out-Null
    }
} while ($choice.ToUpper() -ne 'Q')

Write-Host "Exiting deployment script..." -ForegroundColor Yellow
