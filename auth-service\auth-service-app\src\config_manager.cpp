﻿#include "config_manager.hpp"
#include <fstream>
#include <iostream>

ConfigManager::ConfigManager(const std::string& config_file) {
    load_config(config_file);
}

void ConfigManager::load_config(const std::string& config_file) {
    std::ifstream file(config_file);
    if (!file.is_open()) {
        std::cerr << "Warning: Could not open config file: " << config_file << ". Using defaults." << std::endl;

        // Default configuration with OAuth 2.0 settings
        config_ = nlohmann::json{
            {"database", {
                {"host", "***********"},
                {"port", 5432},
                {"name", "auth_service"},
                {"user", "auth_service"},
                {"password", "password2311"}
            }},
            {"server", {
                {"port", 8082},
                {"log_level", "info"}
            }},
            {"oauth2", {
                {"jwt", {
                    {"secret", "default-jwt-secret-change-in-production-please"},
                    {"access_token_expiry", 3600},    // 1 hour
                    {"refresh_token_expiry", 604800}, // 7 days
                    {"algorithm", "HS256"}
                }},
                {"argon2", {
                    {"memory_cost", 65536},    // 64 MB
                    {"time_cost", 3},          // 3 iterations
                    {"parallelism", 4},        // 4 threads
                    {"salt_length", 32}        // 32 bytes
                }},
                {"session", {
                    {"timeout", 86400},        // 24 hours
                    {"cleanup_interval", 3600}, // 1 hour
                    {"max_sessions_per_user", 5}
                }}
            }}
        };
        return;
    }
    file >> config_;

    // Ensure OAuth 2.0 defaults exist if not in config file
    if (!config_.contains("oauth2")) {
        config_["oauth2"] = nlohmann::json{
            {"jwt", {
                {"secret", "default-jwt-secret-change-in-production-please"},
                {"access_token_expiry", 3600},
                {"refresh_token_expiry", 604800},
                {"algorithm", "HS256"}
            }},
            {"argon2", {
                {"memory_cost", 65536},
                {"time_cost", 3},
                {"parallelism", 4},
                {"salt_length", 32}
            }},
            {"session", {
                {"timeout", 86400},
                {"cleanup_interval", 3600},
                {"max_sessions_per_user", 5}
            }}
        };
    }
}

std::string ConfigManager::get_database_host() const {
    return config_["database"]["host"].get<std::string>();
}

int ConfigManager::get_database_port() const {
    return config_["database"]["port"].get<int>();
}

std::string ConfigManager::get_database_name() const {
    return config_["database"]["name"].get<std::string>();
}

std::string ConfigManager::get_database_user() const {
    return config_["database"]["user"].get<std::string>();
}

std::string ConfigManager::get_database_password() const {
    return config_["database"]["password"].get<std::string>();
}

int ConfigManager::get_server_port() const {
    return config_["server"]["port"].get<int>();
}

std::string ConfigManager::get_log_level() const {
    return config_["server"]["log_level"].get<std::string>();
}

// OAuth 2.0 JWT configuration methods
std::string ConfigManager::get_jwt_secret() const {
    return config_["oauth2"]["jwt"]["secret"].get<std::string>();
}

int ConfigManager::get_jwt_access_token_expiry() const {
    return config_["oauth2"]["jwt"]["access_token_expiry"].get<int>();
}

int ConfigManager::get_jwt_refresh_token_expiry() const {
    return config_["oauth2"]["jwt"]["refresh_token_expiry"].get<int>();
}

std::string ConfigManager::get_jwt_algorithm() const {
    return config_["oauth2"]["jwt"]["algorithm"].get<std::string>();
}

// OAuth 2.0 Argon2 configuration methods
int ConfigManager::get_argon2_memory_cost() const {
    return config_["oauth2"]["argon2"]["memory_cost"].get<int>();
}

int ConfigManager::get_argon2_time_cost() const {
    return config_["oauth2"]["argon2"]["time_cost"].get<int>();
}

int ConfigManager::get_argon2_parallelism() const {
    return config_["oauth2"]["argon2"]["parallelism"].get<int>();
}

int ConfigManager::get_argon2_salt_length() const {
    return config_["oauth2"]["argon2"]["salt_length"].get<int>();
}

// OAuth 2.0 Session configuration methods
int ConfigManager::get_session_timeout() const {
    return config_["oauth2"]["session"]["timeout"].get<int>();
}

int ConfigManager::get_session_cleanup_interval() const {
    return config_["oauth2"]["session"]["cleanup_interval"].get<int>();
}

int ConfigManager::get_max_sessions_per_user() const {
    return config_["oauth2"]["session"]["max_sessions_per_user"].get<int>();
}
