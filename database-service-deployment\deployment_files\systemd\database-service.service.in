[Unit]
Description=@SERVICE_DESCRIPTION@
Documentation=https://git.chcit.org/docs/database-service
After=network.target postgresql.service
Wants=postgresql.service
Requires=network.target

[Service]
Type=simple
User=@SERVICE_USER@
Group=@SERVICE_GROUP@
WorkingDirectory=@CMAKE_INSTALL_PREFIX@
ExecStart=@CMAKE_INSTALL_PREFIX@/bin/database-service --config @CMAKE_INSTALL_PREFIX@/config/config.json
ExecReload=/bin/kill -HUP $MAINPID
Restart=on-failure
RestartSec=5
TimeoutStartSec=30
TimeoutStopSec=30

# Environment variables
Environment="LD_LIBRARY_PATH=@CMAKE_INSTALL_PREFIX@/lib"
Environment="PATH=/usr/local/bin:/usr/bin:/bin"

# Security enhancements
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
NoNewPrivileges=true
ReadWritePaths=@CMAKE_INSTALL_PREFIX@ /var/log/database-service
ReadOnlyPaths=/etc
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectControlGroups=true
RestrictSUIDSGID=true
RestrictRealtime=true
RestrictNamespaces=true
LockPersonality=true
MemoryDenyWriteExecute=true
SystemCallFilter=@system-service
SystemCallErrorNumber=EPERM

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target

