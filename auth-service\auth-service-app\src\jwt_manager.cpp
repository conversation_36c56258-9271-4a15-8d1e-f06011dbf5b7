#include "jwt_manager.hpp"
#include "config_manager.hpp"
#include "database_manager.hpp"
#include <openssl/hmac.h>
#include <openssl/sha.h>
#include <openssl/bio.h>
#include <openssl/evp.h>
#include <openssl/buffer.h>
#include <nlohmann/json.hpp>
#include <iostream>
#include <sstream>
#include <iomanip>

using json = nlohmann::json;

JWTManager::JWTManager(std::shared_ptr<ConfigManager> config, 
                       std::shared_ptr<DatabaseManager> database)
    : config_manager_(config), database_manager_(database), initialized_(false) {
    std::cout << "J<PERSON>TManager initialized for OAuth 2.0 token management" << std::endl;
}

JWTManager::~JWTManager() = default;

bool JWTManager::initialize() {
    if (initialized_) {
        return true;
    }

    try {
        // Load JWT configuration using ConfigManager getter methods
        secret_key_ = config_manager_->get_jwt_secret();
        algorithm_ = config_manager_->get_jwt_algorithm();
        access_token_expiry_ = config_manager_->get_jwt_access_token_expiry();
        refresh_token_expiry_ = config_manager_->get_jwt_refresh_token_expiry();

        // Set default values for issuer (not in ConfigManager yet)
        issuer_ = "auth.chcit.org";

        std::cout << "JWT Configuration loaded:" << std::endl;
        std::cout << "  Algorithm: " << algorithm_ << std::endl;
        std::cout << "  Issuer: " << issuer_ << std::endl;
        std::cout << "  Access Token Expiry: " << access_token_expiry_ << " seconds" << std::endl;
        std::cout << "  Refresh Token Expiry: " << refresh_token_expiry_ << " seconds" << std::endl;

        initialized_ = true;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error initializing JWT Manager: " << e.what() << std::endl;
        return false;
    }
}

JWTManager::TokenPair JWTManager::generateTokenPair(int user_id, const std::vector<std::string>& scopes) {
    TokenPair result;
    result.success = false;

    try {
        // Generate access token
        result.access_token = generateToken(user_id, TokenType::ACCESS, scopes);
        
        // Generate refresh token
        result.refresh_token = generateToken(user_id, TokenType::REFRESH, scopes);

        // Store tokens in database
        if (storeToken(result.access_token) && storeToken(result.refresh_token)) {
            result.success = true;
            std::cout << "Generated JWT token pair for user " << user_id << std::endl;
        } else {
            result.error_message = "Failed to store tokens in database";
        }
    } catch (const std::exception& e) {
        result.error_message = "Error generating token pair: " + std::string(e.what());
        std::cerr << result.error_message << std::endl;
    }

    return result;
}

JWTManager::Token JWTManager::generateToken(int user_id, TokenType type, const std::vector<std::string>& scopes) {
    Token token;
    token.user_id = user_id;
    token.type = type;
    token.scopes = scopes;

    // Calculate expiration time
    int expiry_seconds = (type == TokenType::ACCESS) ? access_token_expiry_ : refresh_token_expiry_;
    token.expires_at = std::chrono::system_clock::now() + std::chrono::seconds(expiry_seconds);

    // Create JWT components
    std::string header = createHeader();
    std::string payload = createPayload(user_id, type, scopes, token.expires_at);
    std::string signature = signToken(header, payload);

    // Combine into JWT token
    token.token = header + "." + payload + "." + signature;
    token.token_hash = calculateTokenHash(token.token);

    return token;
}

std::string JWTManager::createHeader() {
    json header;
    header["alg"] = algorithm_;
    header["typ"] = "JWT";
    
    return base64Encode(header.dump());
}

std::string JWTManager::createPayload(int user_id, TokenType type, 
                                     const std::vector<std::string>& scopes,
                                     std::chrono::system_clock::time_point expires_at) {
    json payload;
    payload["sub"] = std::to_string(user_id);
    payload["iss"] = issuer_;
    payload["aud"] = "chcit.org";
    payload["exp"] = std::chrono::duration_cast<std::chrono::seconds>(expires_at.time_since_epoch()).count();
    payload["iat"] = getCurrentTimestamp();
    payload["token_type"] = (type == TokenType::ACCESS) ? "access" : "refresh";
    
    // Add scopes
    if (!scopes.empty()) {
        payload["scope"] = scopes;
    }
    
    return base64Encode(payload.dump());
}

std::string JWTManager::signToken(const std::string& header, const std::string& payload) {
    std::string data = header + "." + payload;
    
    unsigned char* digest = nullptr;
    unsigned int digest_len = 0;
    
    digest = HMAC(EVP_sha256(), secret_key_.c_str(), secret_key_.length(),
                  reinterpret_cast<const unsigned char*>(data.c_str()), data.length(),
                  nullptr, &digest_len);
    
    if (!digest) {
        throw std::runtime_error("Failed to generate HMAC signature");
    }
    
    std::string signature(reinterpret_cast<char*>(digest), digest_len);
    return base64Encode(signature);
}

JWTManager::ValidationResult JWTManager::validateToken(const std::string& token_string) {
    ValidationResult result;
    result.valid = false;

    try {
        // Parse token components
        std::string header, payload, signature;
        if (!parseToken(token_string, header, payload, signature)) {
            result.error_message = "Invalid token format";
            return result;
        }

        // Verify signature
        if (!verifySignature(header, payload, signature)) {
            result.error_message = "Invalid token signature";
            return result;
        }

        // Decode payload
        int user_id;
        TokenType type;
        std::vector<std::string> scopes;
        std::chrono::system_clock::time_point expires_at;
        
        if (!decodePayload(payload, user_id, type, scopes, expires_at)) {
            result.error_message = "Invalid token payload";
            return result;
        }

        // Check expiration
        if (std::chrono::system_clock::now() >= expires_at) {
            result.error_message = "Token has expired";
            return result;
        }

        // Check if token is revoked
        std::string token_hash = calculateTokenHash(token_string);
        if (isTokenRevoked(token_hash)) {
            result.error_message = "Token has been revoked";
            return result;
        }

        // Token is valid
        result.valid = true;
        result.user_id = user_id;
        result.scopes = scopes;
        result.expires_at = expires_at;

    } catch (const std::exception& e) {
        result.error_message = "Error validating token: " + std::string(e.what());
    }

    return result;
}

bool JWTManager::parseToken(const std::string& token_string, std::string& header, 
                           std::string& payload, std::string& signature) {
    size_t first_dot = token_string.find('.');
    size_t second_dot = token_string.find('.', first_dot + 1);
    
    if (first_dot == std::string::npos || second_dot == std::string::npos) {
        return false;
    }
    
    header = token_string.substr(0, first_dot);
    payload = token_string.substr(first_dot + 1, second_dot - first_dot - 1);
    signature = token_string.substr(second_dot + 1);
    
    return true;
}

bool JWTManager::verifySignature(const std::string& header, const std::string& payload, 
                                const std::string& signature) {
    try {
        std::string expected_signature = signToken(header, payload);
        return expected_signature == signature;
    } catch (const std::exception& e) {
        std::cerr << "Error verifying signature: " << e.what() << std::endl;
        return false;
    }
}

bool JWTManager::decodePayload(const std::string& payload, int& user_id, TokenType& type,
                              std::vector<std::string>& scopes, 
                              std::chrono::system_clock::time_point& expires_at) {
    try {
        std::string decoded_payload = base64Decode(payload);
        json payload_json = json::parse(decoded_payload);
        
        // Extract user ID
        if (payload_json.contains("sub")) {
            user_id = std::stoi(payload_json["sub"].get<std::string>());
        } else {
            return false;
        }
        
        // Extract token type
        if (payload_json.contains("token_type")) {
            std::string type_str = payload_json["token_type"];
            type = (type_str == "access") ? TokenType::ACCESS : TokenType::REFRESH;
        } else {
            type = TokenType::ACCESS; // Default
        }
        
        // Extract scopes
        if (payload_json.contains("scope")) {
            scopes = payload_json["scope"].get<std::vector<std::string>>();
        }
        
        // Extract expiration
        if (payload_json.contains("exp")) {
            int64_t exp_timestamp = payload_json["exp"];
            expires_at = timestampToTimePoint(exp_timestamp);
        } else {
            return false;
        }
        
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error decoding payload: " << e.what() << std::endl;
        return false;
    }
}

std::string JWTManager::calculateTokenHash(const std::string& token) {
    unsigned char hash[SHA256_DIGEST_LENGTH];
    SHA256(reinterpret_cast<const unsigned char*>(token.c_str()), token.length(), hash);
    
    std::stringstream ss;
    for (int i = 0; i < SHA256_DIGEST_LENGTH; i++) {
        ss << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(hash[i]);
    }
    
    return ss.str();
}

int64_t JWTManager::getCurrentTimestamp() {
    return std::chrono::duration_cast<std::chrono::seconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
}

std::chrono::system_clock::time_point JWTManager::timestampToTimePoint(int64_t timestamp) {
    return std::chrono::system_clock::from_time_t(timestamp);
}

JWTManager::TokenPair JWTManager::refreshToken(const std::string& refresh_token) {
    TokenPair result;
    result.success = false;

    try {
        // Validate refresh token
        ValidationResult validation = validateToken(refresh_token);
        if (!validation.valid) {
            result.error_message = "Invalid refresh token: " + validation.error_message;
            return result;
        }

        // Generate new token pair
        result = generateTokenPair(validation.user_id, validation.scopes);

        if (result.success) {
            // Revoke old refresh token
            revokeToken(refresh_token);
            std::cout << "Refreshed tokens for user " << validation.user_id << std::endl;
        }
    } catch (const std::exception& e) {
        result.error_message = "Error refreshing token: " + std::string(e.what());
    }

    return result;
}

bool JWTManager::revokeToken(const std::string& token_string) {
    try {
        std::string token_hash = calculateTokenHash(token_string);

        // Update database to mark token as revoked
        // This would typically be implemented with a database query
        // For now, we'll simulate the operation
        std::cout << "Token revoked: " << token_hash.substr(0, 8) << "..." << std::endl;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error revoking token: " << e.what() << std::endl;
        return false;
    }
}

bool JWTManager::isTokenRevoked(const std::string& token_hash) {
    try {
        // Check database for revoked token
        // This would typically query the jwt_tokens table
        // For now, we'll return false (not revoked)
        return false;
    } catch (const std::exception& e) {
        std::cerr << "Error checking token revocation: " << e.what() << std::endl;
        return true; // Assume revoked on error for security
    }
}

bool JWTManager::storeToken(const Token& token) {
    try {
        // Store token in database
        // This would typically insert into jwt_tokens table
        std::cout << "Storing " << (token.type == TokenType::ACCESS ? "access" : "refresh")
                  << " token for user " << token.user_id << std::endl;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error storing token: " << e.what() << std::endl;
        return false;
    }
}

int JWTManager::cleanupExpiredTokens() {
    try {
        // Clean up expired tokens from database
        // This would typically run a DELETE query on jwt_tokens table
        std::cout << "Cleaning up expired tokens..." << std::endl;
        return 0; // Return number of cleaned tokens
    } catch (const std::exception& e) {
        std::cerr << "Error cleaning up expired tokens: " << e.what() << std::endl;
        return -1;
    }
}

std::string JWTManager::base64Encode(const std::string& input) {
    BIO *bio, *b64;
    BUF_MEM *bufferPtr;

    b64 = BIO_new(BIO_f_base64());
    bio = BIO_new(BIO_s_mem());
    bio = BIO_push(b64, bio);

    BIO_set_flags(bio, BIO_FLAGS_BASE64_NO_NL);
    BIO_write(bio, input.c_str(), input.length());
    BIO_flush(bio);
    BIO_get_mem_ptr(bio, &bufferPtr);

    std::string result(bufferPtr->data, bufferPtr->length);
    BIO_free_all(bio);

    return result;
}

std::string JWTManager::base64Decode(const std::string& input) {
    BIO *bio, *b64;
    int decodeLen = input.length();
    char* buffer = new char[decodeLen];

    bio = BIO_new_mem_buf(input.c_str(), -1);
    b64 = BIO_new(BIO_f_base64());
    bio = BIO_push(b64, bio);

    BIO_set_flags(bio, BIO_FLAGS_BASE64_NO_NL);
    int len = BIO_read(bio, buffer, input.length());
    BIO_free_all(bio);

    std::string result(buffer, len);
    delete[] buffer;

    return result;
}
