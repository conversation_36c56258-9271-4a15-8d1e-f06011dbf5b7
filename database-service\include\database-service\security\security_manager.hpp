#pragma once
#include <string>
#include <memory>
#include <vector>
#include <unordered_map>
#include <mutex>
#include <chrono>
#include <expected> // For std::expected

namespace dbservice {

namespace core {
    class ConnectionManager;
}

namespace security {

// Forward declaration for Connection within SecurityError context if needed
// class Connection; 

/**
 * @enum SecurityErrorType
 * @brief Defines the types of errors that can occur in SecurityManager.
 */
enum class SecurityErrorType {
    Success = 0, // Should not be used with std::unexpected
    InitializationFailed,
    DatabaseError,
    UserNotFound,
    InvalidCredentials,
    TokenGenerationFailed,
    TokenValidationFailed, // e.g., expired, malformed, invalid signature
    TokenStorageFailed,
    TokenRevoked,
    PermissionDenied,
    UserCreationFailure,
    UserDeletionFailure,
    HashingError,
    JwtError, // e.g., secret not set, library error
    ConfigurationError, // e.g. JWT secret not set
    OperationFailed, // Generic failure for operations like grant/revoke permission
    Unknown
};

/**
 * @struct SecurityError
 * @brief Represents an error from the SecurityManager.
 */
struct SecurityError {
    SecurityErrorType type;
    std::string message;
    int code = 0; // Optional: for DB error codes or other specific integer codes

    // Helper for easy string conversion (optional)
    std::string toString() const {
        return "SecurityError(type: " + std::to_string(static_cast<int>(type)) + 
               ", msg: '" + message + "', code: " + std::to_string(code) + ")";
    }
};


/**
 * @struct TokenPair
 * @brief Access and refresh token pair
 */
struct TokenPair {
    std::string accessToken;
    std::string refreshToken;
};

/**
 * @class SecurityManager
 * @brief Manages security for the database service
 */
class SecurityManager {
public:
    /**
     * @brief Constructor
     * @param connectionManager Connection manager
     */
    SecurityManager(std::shared_ptr<core::ConnectionManager> connectionManager);

    /**
     * @brief Destructor
     */
    ~SecurityManager();

    /**
     * @brief Initialize the security manager
     * @return True if initialization was successful
     */
    std::expected<void, SecurityError> initialize();

    /**
     * @brief Authenticate a user
     * @param username Username
     * @param password Password
     * @return Authentication token pair if successful, empty tokens otherwise
     */
    std::expected<TokenPair, SecurityError> authenticate(const std::string& username, const std::string& password);

    /**
     * @brief Validate an authentication token
     * @param token Authentication token
     * @return True if token is valid
     */
    std::expected<std::string, SecurityError> validateToken(const std::string& token);

    /**
     * @brief Validate a JWT token and extract payload
     * @param token JWT token to validate
     * @param payload Output parameter for token payload
     * @return True if token is valid and payload is extracted
     */
    bool validateJwtToken(const std::string& token, std::unordered_map<std::string, std::string>& payload);

    /**
     * @brief Refresh an access token using a refresh token
     * @param refreshToken Refresh token
     * @return New token pair if successful, empty tokens otherwise
     */
    std::expected<TokenPair, SecurityError> refreshAccessToken(const std::string& refreshToken);

    /**
     * @brief Invalidate all tokens for a user
     * @param username Username
     * @return True if tokens were invalidated successfully
     */
    std::expected<void, SecurityError> invalidateTokens(const std::string& username);

    /**
     * @brief Get user information from a token
     * @param token Authentication token
     * @return User information
     */
    std::expected<std::unordered_map<std::string, std::string>, SecurityError> getUserInfo(const std::string& token);

    /**
     * @brief Check if a user has a permission
     * @param username Username
     * @param permission Permission to check
     * @return True if user has permission
     */
    std::expected<bool, SecurityError> hasPermission(const std::string& username, const std::string& permission);

    /**
     * @brief Grant a permission to a user
     * @param username Username
     * @param permission Permission to grant
     * @return True if permission was granted successfully
     */
    std::expected<void, SecurityError> grantPermission(const std::string& username, const std::string& permission);

    /**
     * @brief Revoke a permission from a user
     * @param username Username
     * @param permission Permission to revoke
     * @return True if permission was revoked successfully
     */
    std::expected<void, SecurityError> revokePermission(const std::string& username, const std::string& permission);

    /**
     * @brief Create a user
     * @param username Username
     * @param password Password
     * @param isAdmin Whether the user is an admin
     * @return True if user was created successfully
     */
    std::expected<void, SecurityError> createUser(const std::string& username, const std::string& password, bool isAdmin = false);

    /**
     * @brief Delete a user
     * @param username Username
     * @return True if user was deleted successfully
     */
    std::expected<void, SecurityError> deleteUser(const std::string& username);

    /**
     * @brief Set the JWT secret
     * @param secret JWT secret
     */
    void setJwtSecret(const std::string& secret);

    /**
     * @brief Set token expiration times
     * @param accessTokenExpirationSeconds Access token expiration time in seconds
     * @param refreshTokenExpirationSeconds Refresh token expiration time in seconds
     */
    void setTokenExpirationTimes(int accessTokenExpirationSeconds, int refreshTokenExpirationSeconds);

private:
    /**
     * @brief Create the users table
     * @return Void or error
     */
    std::expected<void, SecurityError> createUsersTable();

    /**
     * @brief Create the permissions table
     * @return Void or error
     */
    std::expected<void, SecurityError> createPermissionsTable();

    /**
     * @brief Create the refresh tokens table
     * @return Void or error
     */
    std::expected<void, SecurityError> createRefreshTokensTable();

    /**
     * @brief Hash a password
     * @param password Password to hash
     * @return Hashed password or error
     */
    std::expected<std::string, SecurityError> hashPassword(const std::string& password);

    /**
     * @brief Verify a password
     * @param password Password to verify
     * @param hash Hash to verify against
     * @return Void or error if password does not match hash
     */
    std::expected<void, SecurityError> verifyPassword(const std::string& password, const std::string& hash);

    /**
     * @brief Generate a token pair
     * @param username Username
     * @return Token pair or error
     */
    std::expected<TokenPair, SecurityError> generateTokenPair(const std::string& username);

    /**
     * @brief Store a refresh token
     * @param username Username
     * @param refreshToken Refresh token
     * @param expiresAt Expiration time
     * @return Void or error
     */
    std::expected<void, SecurityError> storeRefreshToken(const std::string& username, const std::string& refreshToken,
                                                       const std::chrono::system_clock::time_point& expiresAt);

    /**
     * @brief Validate a refresh token
     * @param refreshToken The refresh token to validate
     * @return The username associated with the token on success, or an error
     */
    std::expected<std::string, SecurityError> validateRefreshToken(const std::string& refreshToken);

    /**
     * @brief Delete expired refresh tokens
     */
    void cleanupExpiredRefreshTokens();

    /**
     * @brief Generate a random salt for hashing
     * @param length The length of the salt to generate
     * @return A random salt string
     */
    std::string generateSalt(size_t length);

    std::shared_ptr<core::ConnectionManager> connectionManager_;
    bool initialized_;
    std::mutex mutex_;
    std::string jwtSecret_;
    int accessTokenExpirationSeconds_;
    int refreshTokenExpirationSeconds_;
};

} // namespace security
} // namespace dbservice
