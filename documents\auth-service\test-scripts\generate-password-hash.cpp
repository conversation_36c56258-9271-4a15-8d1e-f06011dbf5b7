// Generate Argon2id Password Hash
// Step 6: Database Operations Integration

#include <iostream>
#include <string>
#include <cstring>
#include <argon2.h>

int main() {
    const char* password = "testpassword";
    const char* salt = "testsalt1234567890"; // 16 bytes
    
    // Argon2id parameters
    uint32_t t_cost = 3;        // 3 iterations
    uint32_t m_cost = 65536;    // 64 MB memory
    uint32_t parallelism = 4;   // 4 threads
    uint32_t hashlen = 32;      // 32 bytes hash length
    
    char hash[128];
    
    int result = argon2id_hash_encoded(
        t_cost,
        m_cost,
        parallelism,
        password,
        strlen(password),
        salt,
        strlen(salt),
        hashlen,
        hash,
        sizeof(hash)
    );
    
    if (result == ARGON2_OK) {
        std::cout << "Argon2id hash for 'testpassword':" << std::endl;
        std::cout << hash << std::endl;
        
        // Test verification
        int verify_result = argon2id_verify(hash, password, strlen(password));
        if (verify_result == ARGON2_OK) {
            std::cout << "✅ Hash verification successful!" << std::endl;
        } else {
            std::cout << "❌ Hash verification failed!" << std::endl;
        }
    } else {
        std::cout << "❌ Hash generation failed: " << argon2_error_message(result) << std::endl;
        return 1;
    }
    
    return 0;
}
