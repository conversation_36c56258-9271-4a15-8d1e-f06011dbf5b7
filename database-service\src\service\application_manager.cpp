#include "database-service/service/application_manager.hpp"
#include "database-service/core/connection_manager.hpp"
#include "database-service/core/connection.hpp"
#include "database-service/utils/logger.hpp"
#include <random>
#include <sstream>
#include <iomanip>
#include <regex>
#include <format>
#include <span>

namespace dbservice::service {

ApplicationManager::ApplicationManager(std::shared_ptr<dbservice::core::ConnectionManager> connectionManager)
    : connectionManager_(connectionManager), initialized_(false) {
}

bool ApplicationManager::initialize() {
    if (initialized_) {
        return true;
    }

    if (!connectionManager_) {
        utils::Logger::error("ApplicationManager: Connection manager is null");
        return false;
    }

    // Verify that the applications table exists
    try {
        auto connectionResult = connectionManager_->getConnection();
        if (!connectionResult.has_value()) {
            utils::Logger::error("ApplicationManager: Failed to get database connection");
            return false;
        }
        auto connection = connectionResult.value();

        // Check if applications table exists
        std::string checkTableQuery = R"(
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_schema = 'public'
                AND table_name = 'applications'
            );
        )";

        auto result = connection->executeQuery(checkTableQuery);
        if (result.empty() || result[0].empty() || result[0][0] != "t") {
            utils::Logger::error("ApplicationManager: Applications table does not exist");
            return false;
        }

        // Check if application_databases table exists
        checkTableQuery = R"(
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_schema = 'public'
                AND table_name = 'application_databases'
            );
        )";

        result = connection->executeQuery(checkTableQuery);
        if (result.empty() || result[0].empty() || result[0][0] != "t") {
            utils::Logger::error("ApplicationManager: Application_databases table does not exist");
            return false;
        }

        initialized_ = true;
        utils::Logger::info("ApplicationManager initialized successfully");
        return true;

    } catch (const std::exception& e) {
        utils::Logger::error(std::format("ApplicationManager initialization failed: {}", e.what()));
        return false;
    }
}

std::expected<int, ApplicationError> ApplicationManager::registerApplication(const ApplicationRequest& request) {
    if (!initialized_) {
        utils::Logger::error("ApplicationManager: Not initialized");
        return std::unexpected(ApplicationError::DATABASE_ERROR);
    }

    // Validate input
    if (!isValidApplicationName(request.name)) {
        utils::Logger::warning(std::format("Invalid application name: {}", request.name));
        return std::unexpected(ApplicationError::INVALID_NAME);
    }

    if (request.description.empty()) {
        utils::Logger::warning("Application description cannot be empty");
        return std::unexpected(ApplicationError::INVALID_DESCRIPTION);
    }

    // Check if application name already exists
    if (applicationNameExists(request.name)) {
        utils::Logger::warning(std::format("Application name already exists: {}", request.name));
        return std::unexpected(ApplicationError::APPLICATION_ALREADY_EXISTS);
    }

    try {
        auto connectionResult = connectionManager_->getConnection();
        if (!connectionResult.has_value()) {
            utils::Logger::error("Failed to get database connection");
            return std::unexpected(ApplicationError::DATABASE_ERROR);
        }
        auto connection = connectionResult.value();

        // Generate API key
        std::string apiKey = generateSecureApiKey();

        // Insert application
        std::string insertQuery = R"(
            INSERT INTO applications (name, description, api_key, created_at, updated_at, active, metadata)
            VALUES ($1, $2, $3, NOW(), NOW(), true, $4)
            RETURNING id;
        )";

        std::vector<std::string> params = {
            request.name,
            request.description,
            apiKey,
            request.metadata.dump()
        };

        auto result = connection->executeQuery(insertQuery, std::span<const std::string>(params));
        if (result.empty() || result[0].empty()) {
            utils::Logger::error("Failed to insert application");
            return std::unexpected(ApplicationError::DATABASE_ERROR);
        }

        int applicationId = std::stoi(result[0][0]);
        utils::Logger::info(std::format("Application registered successfully: {} (ID: {})", request.name, applicationId));
        
        return applicationId;

    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Failed to register application: {}", e.what()));
        return std::unexpected(ApplicationError::DATABASE_ERROR);
    }
}

std::expected<std::string, ApplicationError> ApplicationManager::generateApiKey(int applicationId) {
    if (!initialized_) {
        return std::unexpected(ApplicationError::DATABASE_ERROR);
    }

    try {
        auto connectionResult = connectionManager_->getConnection();
        if (!connectionResult.has_value()) {
            return std::unexpected(ApplicationError::DATABASE_ERROR);
        }
        auto connection = connectionResult.value();

        // Check if application exists
        std::string checkQuery = "SELECT id FROM applications WHERE id = $1 AND active = true";
        std::vector<std::string> checkParams = {std::to_string(applicationId)};
        auto result = connection->executeQuery(checkQuery, std::span<const std::string>(checkParams));
        
        if (result.empty()) {
            return std::unexpected(ApplicationError::APPLICATION_NOT_FOUND);
        }

        // Generate new API key
        std::string newApiKey = generateSecureApiKey();

        // Update application with new API key
        std::string updateQuery = R"(
            UPDATE applications 
            SET api_key = $1, updated_at = NOW() 
            WHERE id = $2
        )";

        std::vector<std::string> updateParams = {newApiKey, std::to_string(applicationId)};
        connection->executeNonQuery(updateQuery, std::span<const std::string>(updateParams));

        utils::Logger::info(std::format("New API key generated for application ID: {}", applicationId));
        return newApiKey;

    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Failed to generate API key: {}", e.what()));
        return std::unexpected(ApplicationError::API_KEY_GENERATION_FAILED);
    }
}

std::expected<int, ApplicationError> ApplicationManager::validateApiKey(const std::string& apiKey) {
    if (!initialized_) {
        return std::unexpected(ApplicationError::DATABASE_ERROR);
    }

    if (apiKey.empty()) {
        return std::unexpected(ApplicationError::API_KEY_INVALID);
    }

    try {
        auto connectionResult = connectionManager_->getConnection();
        if (!connectionResult.has_value()) {
            return std::unexpected(ApplicationError::DATABASE_ERROR);
        }
        auto connection = connectionResult.value();

        std::string query = "SELECT id FROM applications WHERE api_key = $1 AND active = true";
        std::vector<std::string> queryParams = {apiKey};
        auto result = connection->executeQuery(query, std::span<const std::string>(queryParams));

        if (result.empty() || result[0].empty()) {
            return std::unexpected(ApplicationError::API_KEY_INVALID);
        }

        return std::stoi(result[0][0]);

    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Failed to validate API key: {}", e.what()));
        return std::unexpected(ApplicationError::DATABASE_ERROR);
    }
}

std::string ApplicationManager::generateSecureApiKey() {
    // Generate a secure random API key
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, 255);

    // Generate 32 random bytes
    std::vector<unsigned char> bytes(32);
    for (auto& byte : bytes) {
        byte = static_cast<unsigned char>(dis(gen));
    }

    // Convert to hex string with prefix
    std::ostringstream oss;
    oss << "dbsvc_";
    for (const auto& byte : bytes) {
        oss << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(byte);
    }

    return oss.str();
}

bool ApplicationManager::isValidApplicationName(const std::string& name) {
    // Application name must be 3-50 characters, alphanumeric with hyphens and underscores
    if (name.length() < 3 || name.length() > 50) {
        return false;
    }

    std::regex namePattern("^[a-zA-Z0-9][a-zA-Z0-9_-]*[a-zA-Z0-9]$");
    return std::regex_match(name, namePattern);
}

bool ApplicationManager::applicationNameExists(const std::string& name) {
    try {
        auto connectionResult = connectionManager_->getConnection();
        if (!connectionResult.has_value()) {
            return false;
        }
        auto connection = connectionResult.value();

        std::string query = "SELECT COUNT(*) as count FROM applications WHERE name = $1";
        std::vector<std::string> queryParams = {name};
        auto result = connection->executeQuery(query, std::span<const std::string>(queryParams));

        return !result.empty() && !result[0].empty() && std::stoi(result[0][0]) > 0;

    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Failed to check application name existence: {}", e.what()));
        return false;
    }
}

std::expected<Application, ApplicationError> ApplicationManager::getApplication(int applicationId) {
    if (!initialized_) {
        return std::unexpected(ApplicationError::DATABASE_ERROR);
    }

    try {
        auto connectionResult = connectionManager_->getConnection();
        if (!connectionResult.has_value()) {
            return std::unexpected(ApplicationError::DATABASE_ERROR);
        }
        auto connection = connectionResult.value();

        std::string query = R"(
            SELECT id, name, description, api_key, created_at, updated_at, active, metadata
            FROM applications
            WHERE id = $1
        )";

        std::vector<std::string> queryParams = {std::to_string(applicationId)};
        auto result = connection->executeQuery(query, std::span<const std::string>(queryParams));
        if (result.empty()) {
            return std::unexpected(ApplicationError::APPLICATION_NOT_FOUND);
        }

        return rowToApplication(result[0]);

    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Failed to get application: {}", e.what()));
        return std::unexpected(ApplicationError::DATABASE_ERROR);
    }
}

std::expected<Application, ApplicationError> ApplicationManager::getApplicationByName(const std::string& name) {
    if (!initialized_) {
        return std::unexpected(ApplicationError::DATABASE_ERROR);
    }

    try {
        auto connectionResult = connectionManager_->getConnection();
        if (!connectionResult.has_value()) {
            return std::unexpected(ApplicationError::DATABASE_ERROR);
        }
        auto connection = connectionResult.value();

        std::string query = R"(
            SELECT id, name, description, api_key, created_at, updated_at, active, metadata
            FROM applications
            WHERE name = $1
        )";

        std::vector<std::string> queryParams = {name};
        auto result = connection->executeQuery(query, std::span<const std::string>(queryParams));
        if (result.empty()) {
            return std::unexpected(ApplicationError::APPLICATION_NOT_FOUND);
        }

        return rowToApplication(result[0]);

    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Failed to get application by name: {}", e.what()));
        return std::unexpected(ApplicationError::DATABASE_ERROR);
    }
}

std::expected<std::vector<Application>, ApplicationError> ApplicationManager::listApplications() {
    if (!initialized_) {
        return std::unexpected(ApplicationError::DATABASE_ERROR);
    }

    try {
        auto connectionResult = connectionManager_->getConnection();
        if (!connectionResult.has_value()) {
            return std::unexpected(ApplicationError::DATABASE_ERROR);
        }
        auto connection = connectionResult.value();

        std::string query = R"(
            SELECT id, name, description, api_key, created_at, updated_at, active, metadata
            FROM applications
            ORDER BY created_at DESC
        )";

        auto result = connection->executeQuery(query);

        std::vector<Application> applications;
        applications.reserve(result.size());

        for (const auto& row : result) {
            applications.push_back(rowToApplication(row));
        }

        return applications;

    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Failed to list applications: {}", e.what()));
        return std::unexpected(ApplicationError::DATABASE_ERROR);
    }
}

std::expected<void, ApplicationError> ApplicationManager::updateApplicationMetadata(int applicationId, const nlohmann::json& metadata) {
    if (!initialized_) {
        return std::unexpected(ApplicationError::DATABASE_ERROR);
    }

    try {
        auto connectionResult = connectionManager_->getConnection();
        if (!connectionResult.has_value()) {
            return std::unexpected(ApplicationError::DATABASE_ERROR);
        }
        auto connection = connectionResult.value();

        // Check if application exists
        std::string checkQuery = "SELECT id FROM applications WHERE id = $1";
        std::vector<std::string> checkParams = {std::to_string(applicationId)};
        auto result = connection->executeQuery(checkQuery, std::span<const std::string>(checkParams));

        if (result.empty()) {
            return std::unexpected(ApplicationError::APPLICATION_NOT_FOUND);
        }

        // Update metadata
        std::string updateQuery = R"(
            UPDATE applications
            SET metadata = $1, updated_at = NOW()
            WHERE id = $2
        )";

        std::vector<std::string> updateParams = {metadata.dump(), std::to_string(applicationId)};
        connection->executeNonQuery(updateQuery, std::span<const std::string>(updateParams));

        utils::Logger::info(std::format("Application metadata updated for ID: {}", applicationId));
        return {};

    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Failed to update application metadata: {}", e.what()));
        return std::unexpected(ApplicationError::DATABASE_ERROR);
    }
}

Application ApplicationManager::rowToApplication(const std::vector<std::string>& row) {
    Application app;

    // Expecting columns: id, name, description, api_key, created_at, updated_at, active, metadata
    if (row.size() >= 8) {
        app.id = std::stoi(row[0]);
        app.name = row[1];
        app.description = row[2];
        app.apiKey = row[3];
        // Skip created_at (row[4]) and updated_at (row[5]) for now
        app.active = (row[6] == "t" || row[6] == "true");

        // Parse metadata JSON
        try {
            app.metadata = nlohmann::json::parse(row[7]);
        } catch (const std::exception&) {
            app.metadata = nlohmann::json::object();
        }
    }

    // Parse timestamps (simplified - in real implementation you'd parse ISO strings)
    app.createdAt = std::chrono::system_clock::now();
    app.updatedAt = std::chrono::system_clock::now();

    return app;
}

std::expected<void, ApplicationError> ApplicationManager::deactivateApplication(int applicationId) {
    if (!initialized_) {
        return std::unexpected(ApplicationError::DATABASE_ERROR);
    }

    try {
        auto connectionResult = connectionManager_->getConnection();
        if (!connectionResult.has_value()) {
            return std::unexpected(ApplicationError::DATABASE_ERROR);
        }
        auto connection = connectionResult.value();

        std::string updateQuery = R"(
            UPDATE applications
            SET active = false, updated_at = NOW()
            WHERE id = $1
        )";

        std::vector<std::string> updateParams = {std::to_string(applicationId)};
        auto rowsAffected = connection->executeNonQuery(updateQuery, std::span<const std::string>(updateParams));
        if (rowsAffected == 0) {
            return std::unexpected(ApplicationError::APPLICATION_NOT_FOUND);
        }

        utils::Logger::info(std::format("Application deactivated: {}", applicationId));
        return {};

    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Failed to deactivate application: {}", e.what()));
        return std::unexpected(ApplicationError::DATABASE_ERROR);
    }
}

std::expected<void, ApplicationError> ApplicationManager::activateApplication(int applicationId) {
    if (!initialized_) {
        return std::unexpected(ApplicationError::DATABASE_ERROR);
    }

    try {
        auto connectionResult = connectionManager_->getConnection();
        if (!connectionResult.has_value()) {
            return std::unexpected(ApplicationError::DATABASE_ERROR);
        }
        auto connection = connectionResult.value();

        std::string updateQuery = R"(
            UPDATE applications
            SET active = true, updated_at = NOW()
            WHERE id = $1
        )";

        std::vector<std::string> updateParams = {std::to_string(applicationId)};
        auto rowsAffected = connection->executeNonQuery(updateQuery, std::span<const std::string>(updateParams));
        if (rowsAffected == 0) {
            return std::unexpected(ApplicationError::APPLICATION_NOT_FOUND);
        }

        utils::Logger::info(std::format("Application activated: {}", applicationId));
        return {};

    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Failed to activate application: {}", e.what()));
        return std::unexpected(ApplicationError::DATABASE_ERROR);
    }
}

std::expected<void, ApplicationError> ApplicationManager::linkToDatabase(int applicationId, int databaseInstanceId, const std::string& schemaName) {
    if (!initialized_) {
        return std::unexpected(ApplicationError::DATABASE_ERROR);
    }

    if (schemaName.empty()) {
        return std::unexpected(ApplicationError::INVALID_PARAMETERS);
    }

    try {
        auto connectionResult = connectionManager_->getConnection();
        if (!connectionResult.has_value()) {
            return std::unexpected(ApplicationError::DATABASE_ERROR);
        }
        auto connection = connectionResult.value();

        // Check if application exists and is active
        std::string checkAppQuery = "SELECT id FROM applications WHERE id = $1 AND active = true";
        std::vector<std::string> checkParams = {std::to_string(applicationId)};
        auto result = connection->executeQuery(checkAppQuery, std::span<const std::string>(checkParams));

        if (result.empty()) {
            return std::unexpected(ApplicationError::APPLICATION_NOT_FOUND);
        }

        // Check if mapping already exists
        std::string checkMappingQuery = R"(
            SELECT id FROM application_databases
            WHERE application_id = $1 AND database_instance_id = $2 AND active = true
        )";

        std::vector<std::string> mappingParams = {
            std::to_string(applicationId),
            std::to_string(databaseInstanceId)
        };
        result = connection->executeQuery(checkMappingQuery, std::span<const std::string>(mappingParams));

        if (!result.empty()) {
            // Update existing mapping
            std::string updateQuery = R"(
                UPDATE application_databases
                SET schema_name = $1, updated_at = NOW()
                WHERE application_id = $2 AND database_instance_id = $3
            )";

            std::vector<std::string> updateParams = {
                schemaName,
                std::to_string(applicationId),
                std::to_string(databaseInstanceId)
            };
            connection->executeNonQuery(updateQuery, std::span<const std::string>(updateParams));
        } else {
            // Create new mapping
            std::string insertQuery = R"(
                INSERT INTO application_databases (application_id, database_instance_id, schema_name, created_at, active)
                VALUES ($1, $2, $3, NOW(), true)
            )";

            std::vector<std::string> insertParams = {
                std::to_string(applicationId),
                std::to_string(databaseInstanceId),
                schemaName
            };
            connection->executeNonQuery(insertQuery, std::span<const std::string>(insertParams));
        }

        utils::Logger::info(std::format("Application {} linked to database instance {} with schema {}",
                                       applicationId, databaseInstanceId, schemaName));
        return {};

    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Failed to link application to database: {}", e.what()));
        return std::unexpected(ApplicationError::DATABASE_ERROR);
    }
}

ApplicationDatabase ApplicationManager::rowToApplicationDatabase(const std::vector<std::string>& row) {
    ApplicationDatabase appDb;

    // Expecting columns: application_id, database_instance_id, schema_name, active, created_at
    if (row.size() >= 4) {
        appDb.applicationId = std::stoi(row[0]);
        appDb.databaseInstanceId = std::stoi(row[1]);
        appDb.schemaName = row[2];
        appDb.active = (row[3] == "t" || row[3] == "true");
    }

    // Parse timestamp (simplified)
    appDb.createdAt = std::chrono::system_clock::now();

    return appDb;
}

} // namespace dbservice::service
