# Authentication Service - Minimal Implementation Plan

*Last Updated: January 7, 2025*
*Based on: OAuth 2.0 + OIDC industry standard design with phased incremental development*

## 🎯 **Overview**

This document outlines the minimal implementation strategy for the Authentication Service using **OAuth 2.0 + OpenID Connect (OIDC)** industry standards, designed to build functionality incrementally while maintaining a working, deployable system at each stage.

### **Key Architecture Updates**
- **OAuth 2.0 Compliance**: RFC 6749 compliant authorization server
- **OpenID Connect**: OIDC Core 1.0 identity layer
- **Valkey Caching**: High-performance caching (Redis-compatible, fully open source)
- **Argon2id Security**: OWASP recommended password hashing
- **Enhanced Security**: PKCE, state parameters, token binding

## 🏗️ **Implementation Philosophy**

### **Core Principles**
- **Always Compilable**: Code must build successfully at every stage
- **Always Deployable**: Each phase produces a functional (though limited) service
- **Incremental Testing**: Test each component as it's implemented
- **Stub-to-Real**: Start with working stubs, replace with real implementations
- **Interface-First**: Define all interfaces before implementing functionality

### **Why This Approach?**
- **Risk Mitigation**: Avoid "big bang" integration problems
- **Early Feedback**: Identify issues early in development
- **Continuous Deployment**: Always have a working version to deploy
- **Parallel Development**: Multiple developers can work on different components
- **User Confidence**: Demonstrate progress with working software

---

## 📋 **Phase 1: Infrastructure Foundation** ✅ **COMPLETE**

### **Objectives**
- Establish complete project structure
- Create deployment automation
- Set up build system
- Prepare for incremental development

### **Completed Deliverables**
- [x] **Project Structure**: Complete directory hierarchy
- [x] **Build System**: CMakeLists.txt with C++23 and all dependencies
- [x] **Deployment Scripts**: 33 PowerShell modules for full automation
- [x] **Configuration System**: JSON-based environment management
- [x] **Database Framework**: Schema file structure and migration system

### **Key Achievement**
✅ **Infrastructure Ready**: Complete foundation for incremental development

---

## 📋 **Phase 2: Skeleton Implementation** 🔄 **IN PROGRESS**

### **Objectives**
- Create all C++ classes with working interfaces
- Ensure compilation and basic service startup
- Establish component relationships
- Prepare for real functionality implementation

### **Implementation Strategy**

#### **Step 2.1: Class Skeletons** ✅ **COMPLETE**
```cpp
// All classes created with:
// - Complete header files with proper interfaces
// - Stub implementations that compile and run
// - Proper dependency injection structure
// - Basic logging and error handling
```

**Classes Implemented**:
- `AuthService` - Main service coordinator
- `OAuthServer` - OAuth 2.0 authorization server (stub)
- `OIDCProvider` - OpenID Connect provider (stub)
- `DatabaseManager` - PostgreSQL operations (stub)
- `ValkeyClient` - Valkey cache client (stub)
- `Argon2Hasher` - Argon2id password hashing (stub)
- `TokenManager` - JWT token management (stub)
- `HttpServer` - OAuth 2.0 compliant API server (stub)
- `UserManager` - User operations (stub)
- `ConfigManager` - Configuration loading (functional)

#### **Step 2.2: Service Integration** ✅ **COMPLETE**
```cpp
// Enhanced service startup sequence:
// 1. Load configuration
// 2. Initialize database connection pool
// 3. Initialize Valkey cache client
// 4. Initialize Argon2 password hasher
// 5. Initialize OAuth 2.0 server
// 6. Initialize OIDC provider
// 7. Initialize token manager
// 8. Start HTTP server with OAuth endpoints
// 9. Begin OAuth 2.0 request processing
```

#### **Step 2.3: Deployment Testing** 🔄 **CURRENT**
- **Test Compilation**: Verify build on Ubuntu 24.04
- **Test Service Startup**: Confirm all components initialize
- **Test Configuration**: Validate JSON config loading
- **Test Deployment Scripts**: Verify all 21 menu options work

### **Current Status**
✅ **Skeleton Complete**: All components compile and integrate  
🔄 **Next**: Test deployment and complete database schema

---

## 📋 **Phase 3: OAuth 2.0 Core Functionality** ⏳ **PLANNED**

### **Implementation Strategy: OAuth 2.0 First Approach**

#### **Step 3.1: Database and Cache Integration** (Priority 1)
**Objective**: Replace database and cache stubs with real PostgreSQL and Valkey operations

**Current Database Stub**:
```cpp
void DatabaseManager::initialize() {
    std::cout << "Initializing database connection..." << std::endl;
    // NO ACTUAL CONNECTION
}
```

**Current Valkey Stub**:
```cpp
void ValkeyClient::connect() {
    std::cout << "Connecting to Valkey..." << std::endl;
    // NO ACTUAL CONNECTION
}
```

**Minimal Real Implementation**:
```cpp
void DatabaseManager::initialize() {
    try {
        connection_pool_ = std::make_unique<pqxx::connection_pool>(
            config_->get_database_connection_string(), 10);

        // Test connection
        auto conn = connection_pool_->acquire();
        pqxx::work txn(*conn);
        txn.exec("SELECT 1");
        txn.commit();

        logger_->info("PostgreSQL connection established");
    } catch (const std::exception& e) {
        logger_->error("Database initialization failed: {}", e.what());
        throw;
    }
}

bool ValkeyClient::connect() {
    try {
        context_ = valkeyConnect(host_.c_str(), port_);
        if (context_ == nullptr || context_->err) {
            throw std::runtime_error("Valkey connection failed");
        }

        // Authenticate if password provided
        if (!password_.empty()) {
            valkeyReply* reply = (valkeyReply*)valkeyCommand(context_, "AUTH %s", password_.c_str());
            if (reply->type == VALKEY_REPLY_ERROR) {
                valkeyFreeReplyObject(reply);
                throw std::runtime_error("Valkey authentication failed");
            }
            valkeyFreeReplyObject(reply);
        }

        logger_->info("Valkey connection established");
        return true;
    } catch (const std::exception& e) {
        logger_->error("Valkey connection failed: {}", e.what());
        return false;
    }
}
```

**Testing Strategy**:
- Unit tests for connection management
- Integration tests with real PostgreSQL
- Error handling verification
- Performance baseline establishment

#### **Step 3.2: Argon2id Security Implementation** (Priority 2)
**Objective**: Replace security stubs with real Argon2id password hashing

**Current Stub**:
```cpp
std::string Argon2Hasher::hash_password(const std::string& password) {
    return "argon2id_hashed_" + password;  // FAKE!
}
```

**Minimal Real Implementation**:
```cpp
Argon2Hasher::HashResult Argon2Hasher::hash_password(const std::string& password) {
    auto salt = generate_salt();
    std::vector<uint8_t> hash(HASH_LENGTH);

    int result = argon2id_hash_raw(
        TIME_COST,      // 3 iterations
        MEMORY_COST,    // 64 MB memory
        PARALLELISM,    // 4 threads
        password.c_str(), password.length(),
        salt.data(), SALT_LENGTH,
        hash.data(), HASH_LENGTH
    );

    if (result != ARGON2_OK) {
        throw std::runtime_error("Argon2id hashing failed: " +
                               std::string(argon2_error_message(result)));
    }

    return {
        .encoded_hash = encode_hash(salt, hash),
        .salt = salt,
        .hash = hash
    };
}

bool Argon2Hasher::verify_password(const std::string& password,
                                  const std::string& encoded_hash) {
    std::vector<uint8_t> salt, stored_hash;
    if (!decode_hash(encoded_hash, salt, stored_hash)) {
        return false;
    }

    std::vector<uint8_t> computed_hash(HASH_LENGTH);
    int result = argon2id_hash_raw(
        TIME_COST, MEMORY_COST, PARALLELISM,
        password.c_str(), password.length(),
        salt.data(), salt.size(),
        computed_hash.data(), HASH_LENGTH
    );

    if (result != ARGON2_OK) {
        return false;
    }

    // Constant-time comparison
    return std::equal(stored_hash.begin(), stored_hash.end(), computed_hash.begin());
}
```

**Testing Strategy**:
- Password hashing verification
- JWT token generation/validation
- Security parameter validation
- Performance benchmarking

#### **Step 3.3: OAuth 2.0 HTTP Server Implementation** (Priority 3)
**Objective**: Replace HTTP stub with real OAuth 2.0 compliant API

**Current Stub**:
```cpp
void HttpServer::start(int port) {
    std::cout << "Starting OAuth 2.0 server on port " << port << "..." << std::endl;
    // NO ACTUAL SERVER
}
```

**Minimal Real Implementation**:
```cpp
void HttpServer::start(int port) {
    httplib::SSLServer server(ssl_cert_path_.c_str(), ssl_key_path_.c_str());

    // OAuth 2.0 Authorization Endpoint (RFC 6749)
    server.Get("/oauth/authorize", [this](const httplib::Request& req, httplib::Response& res) {
        handle_authorize(req, res);
    });

    // OAuth 2.0 Token Endpoint (RFC 6749)
    server.Post("/oauth/token", [this](const httplib::Request& req, httplib::Response& res) {
        handle_token(req, res);
    });

    // Token Revocation Endpoint (RFC 7009)
    server.Post("/oauth/revoke", [this](const httplib::Request& req, httplib::Response& res) {
        handle_revoke(req, res);
    });

    // Token Introspection Endpoint (RFC 7662)
    server.Post("/oauth/introspect", [this](const httplib::Request& req, httplib::Response& res) {
        handle_introspect(req, res);
    });

    // OpenID Connect UserInfo Endpoint
    server.Get("/oidc/userinfo", [this](const httplib::Request& req, httplib::Response& res) {
        handle_userinfo(req, res);
    });

    // OpenID Connect Discovery Endpoint
    server.Get("/.well-known/openid_configuration", [this](const httplib::Request& req, httplib::Response& res) {
        handle_discovery(req, res);
    });

    // JWKS Endpoint for token verification
    server.Get("/.well-known/jwks.json", [this](const httplib::Request& req, httplib::Response& res) {
        handle_jwks(req, res);
    });

    // Health check
    server.Get("/health", [](const httplib::Request&, httplib::Response& res) {
        res.set_content("{\"status\":\"healthy\"}", "application/json");
    });

    server.listen("0.0.0.0", port);
}
```

**Testing Strategy**:
- API endpoint testing
- JSON request/response validation
- Authentication flow testing
- Load testing with minimal load

#### **Step 3.4: OAuth 2.0 Flow Implementation** (Priority 4)
**Objective**: Implement complete OAuth 2.0 Authorization Code Flow

**Testing Strategy**:
- OAuth 2.0 authorization flow testing
- PKCE implementation verification
- Token generation and validation
- Client registration and validation
- State parameter CSRF protection

### **Phase 3 Success Criteria**
- [ ] PostgreSQL and Valkey connections functional
- [ ] Argon2id password hashing implemented
- [ ] OAuth 2.0 authorization endpoint working
- [ ] OAuth 2.0 token endpoint operational
- [ ] OpenID Connect UserInfo endpoint functional
- [ ] JWT ID tokens and access tokens working
- [ ] PKCE security implementation complete
- [ ] Complete OAuth 2.0 authorization flow working

---

## 📋 **Phase 4: Advanced OAuth 2.0 Features** ⏳ **FUTURE**

### **Enhanced OAuth 2.0 Implementation**
- **Multiple Grant Types**: Client credentials, device code, refresh token flows
- **Dynamic Client Registration**: RFC 7591 compliant client registration
- **Token Binding**: Cryptographic binding of tokens to clients
- **Rich Authorization Requests**: RFC 9396 for fine-grained permissions

### **Advanced Security Features**
- **Multi-factor Authentication**: TOTP, WebAuthn, SMS integration
- **Risk-based Authentication**: Adaptive authentication based on context
- **Rate Limiting**: OAuth-aware rate limiting and DDoS protection
- **Security Event Tokens**: RFC 8417 for security event notifications

### **Enterprise Integration**
- **SAML Bridge**: SAML to OAuth 2.0 protocol translation
- **LDAP/Active Directory**: Enterprise directory integration
- **Single Sign-On**: Enterprise SSO with multiple identity providers
- **Federation**: Cross-domain identity federation

### **Monitoring and Analytics**
- **OAuth Analytics**: Token usage, client metrics, security events
- **Real-time Monitoring**: WebSocket-based real-time dashboards
- **Compliance Reporting**: Automated compliance and audit reports
- **Performance Metrics**: OAuth flow performance and optimization

### **UI Implementation**
- **OAuth Client Management**: Client registration and configuration interface
- **User Consent Management**: User-friendly consent and permission management
- **Admin Dashboard**: OAuth server administration and monitoring
- **Developer Portal**: API documentation and client development tools

---

## 🧪 **Testing Strategy**

### **Continuous Testing Approach**
1. **Unit Tests**: Test each component in isolation
2. **Integration Tests**: Test component interactions
3. **System Tests**: Test complete workflows
4. **Deployment Tests**: Test in target environment

### **OAuth 2.0 Test-Driven Development**
```cpp
// Example: OAuth 2.0 Authorization Code Flow Test
TEST(OAuthServer, AuthorizationCodeFlow) {
    OAuthServer oauth_server;

    // Test authorization request
    AuthorizeRequest auth_req = {
        .client_id = "test_client",
        .redirect_uri = "https://client.example.com/callback",
        .response_type = "code",
        .scope = "openid profile email",
        .state = "random_state_value",
        .code_challenge = "E9Melhoa2OwvFrEMTJguCHaoeK1t8URWbuGJSstw-cM",
        .code_challenge_method = "S256"
    };

    auto auth_result = oauth_server.handle_authorize(auth_req, authenticated_user);
    EXPECT_TRUE(auth_result.success);
    EXPECT_FALSE(auth_result.authorization_code.empty());

    // Test token exchange
    TokenRequest token_req = {
        .grant_type = "authorization_code",
        .code = auth_result.authorization_code,
        .client_id = "test_client",
        .code_verifier = "dBjftJeZ4CVP-mB92K27uhbUJU1p1r_wW1gFWFOEjXk"
    };

    auto token_result = oauth_server.handle_token(token_req);
    EXPECT_TRUE(token_result.success);
    EXPECT_FALSE(token_result.access_token.empty());
    EXPECT_FALSE(token_result.id_token.empty());
    EXPECT_EQ(token_result.token_type, "Bearer");
}

// Test Argon2id password hashing
TEST(Argon2Hasher, PasswordHashing) {
    std::string password = "secure_password_123!";
    auto hash_result = Argon2Hasher::hash_password(password);

    EXPECT_NE(hash_result.encoded_hash, password);
    EXPECT_TRUE(Argon2Hasher::verify_password(password, hash_result.encoded_hash));
    EXPECT_FALSE(Argon2Hasher::verify_password("wrong_password", hash_result.encoded_hash));
}

// Test Valkey session storage
TEST(ValkeyClient, SessionManagement) {
    ValkeyClient valkey;
    ASSERT_TRUE(valkey.connect());

    SessionData session = {
        .user_id = "user123",
        .client_id = "client456",
        .scope = "openid profile",
        .created_at = std::chrono::system_clock::now()
    };

    std::string session_id = "session_" + generate_random_string(32);
    EXPECT_TRUE(valkey.store_session(session_id, session, std::chrono::hours(1)));

    auto retrieved_session = valkey.get_session(session_id);
    ASSERT_TRUE(retrieved_session.has_value());
    EXPECT_EQ(retrieved_session->user_id, session.user_id);
}
```

### **Testing Phases**
- **Phase 2**: Skeleton integration testing
- **Phase 3**: Component functionality testing
- **Phase 4**: End-to-end system testing

---

## 🔄 **Development Workflow**

### **Daily Development Cycle**
1. **Review Current Status**: Check `auth-service-next-steps.md`
2. **Select Component**: Choose highest priority item
3. **Write Tests**: Create tests for new functionality
4. **Implement**: Replace stub with real implementation
5. **Test**: Verify functionality works correctly
6. **Deploy**: Test in development environment
7. **Update Documentation**: Update next steps and progress

### **Quality Gates**
- **Compilation**: Must build without errors
- **Tests**: All tests must pass
- **Deployment**: Must deploy successfully
- **Functionality**: Must provide working features
- **Documentation**: Must update progress tracking

---

## 📊 **Progress Tracking**

### **Completion Metrics**
- **Phase 1**: ✅ 100% Complete
- **Phase 2**: 🔄 90% Complete (deployment testing remaining)
- **Phase 3**: ⏳ 0% Complete (ready to start)
- **Phase 4**: ⏳ 0% Complete (future)

### **Success Indicators**
- OAuth 2.0 service compiles and runs
- All components (PostgreSQL, Valkey, OAuth server) initialize correctly
- Configuration system functional with OAuth 2.0 settings
- Deployment scripts operational
- OAuth 2.0 authorization code flow working
- OpenID Connect identity layer functional
- Argon2id password security implemented
- Industry standard compliance achieved

---

*This OAuth 2.0 + OIDC minimal implementation plan ensures continuous progress with industry-standard compliant software at every stage, reducing risk and enabling early feedback while maintaining compatibility with existing OAuth 2.0 infrastructure.*
