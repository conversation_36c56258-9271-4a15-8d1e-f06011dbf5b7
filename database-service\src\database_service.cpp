// Standard Library Headers
#include <iostream>
#include <thread>
#include <chrono>
#include <condition_variable>
#include <expected>
#include <format>
#include <print>

// Project Headers
#include "database-service/database_service.hpp"
#include "database-service/api/api_server.hpp"
#include "database-service/core/connection_manager.hpp"
#include "database-service/schema/schema_manager.hpp"
#include "database-service/security/security_manager.hpp"
#include "database-service/security/credential_store.hpp"
#include "database-service/api/route_controller.hpp"
#include "database-service/utils/config_manager.hpp"
#include "database-service/utils/logger.hpp"

namespace dbservice {

/**
 * @brief Construct a new Database Service object
 * 
 * Initializes the service in a stopped state with default configuration.
 * Actual initialization of components is deferred to the initialize() method.
 */
DatabaseService::DatabaseService()
    : initialized_(false),
      running_(false) {
    std::cout << "=== DATABASE_SERVICE: Constructor entry ===" << std::endl;
    std::cout.flush();
    // All member initialization is handled in the initialization list
    std::cout << "=== DATABASE_SERVICE: Constructor exit ===" << std::endl;
    std::cout.flush();
}

/**
 * @brief Destroy the Database Service object
 * 
 * Ensures proper cleanup of resources by stopping the service
 * if it's still running.
 */
DatabaseService::~DatabaseService() {
    stop(); // Ensure clean shutdown on destruction
}

/**
 * @brief Initialize the database service and all its components
 * 
 * Initializes components in the following order:
 * 1. Configuration Manager
 * 2. Connection Manager
 * 3. Security Manager
 * 4. Schema Manager
 * 5. API Server
 * 
 * @return std::expected<void, std::string> 
 *         - Success: void
 *         - Error: Descriptive error message
 */
std::expected<void, std::string> DatabaseService::initialize() {
    // Check if already initialized (idempotent operation)
    if (initialized_.load(std::memory_order_relaxed)) {
        return {};
    }

    try {
        utils::Logger::info("DATABASE_SERVICE: Starting initialization...");
        std::cout << "DATABASE_SERVICE: Starting initialization..." << std::endl;

        // Access configuration manager singleton
        utils::Logger::info("DATABASE_SERVICE: Getting configuration manager instance...");
        std::cout << "DATABASE_SERVICE: Getting configuration manager instance..." << std::endl;
        auto& configManager = utils::ConfigManager::getInstance();
        utils::Logger::info("DATABASE_SERVICE: Configuration manager instance obtained");
        std::cout << "DATABASE_SERVICE: Configuration manager instance obtained" << std::endl;

        // Build database connection string from configuration
        utils::Logger::info("DATABASE_SERVICE: Building database connection string...");
        std::cout << "DATABASE_SERVICE: Building database connection string..." << std::endl;
        // Format: host=hostname port=5432 dbname=dbname user=username [password=secret]
        std::string connectionString =
            std::format("host={} port={} dbname={} user={} connect_timeout=10",
                configManager.getString("database.host", "localhost"),
                configManager.getInt("database.port", 5432),
                configManager.getString("database.name", "postgres"),
                configManager.getString("database.user", "postgres"));
        utils::Logger::info("DATABASE_SERVICE: Database connection string built");
        std::cout << "DATABASE_SERVICE: Database connection string built" << std::endl;

        // Add password to connection string if provided
        // Note: In production, consider using environment variables or secure storage for passwords
        std::string password = configManager.getString("database.password", "");
        if (!password.empty()) {
            connectionString += std::format(" password={}", password);
        }

        // Configure SSL for database connections
        core::SSLConfig sslConfig;
        sslConfig.enabled = configManager.getBool("database.ssl.enabled", false);

        if (sslConfig.enabled) {
            // SSL is enabled, parse SSL mode and certificate paths
            // Get SSL mode
            std::string sslModeStr = configManager.getString("database.ssl.mode", "verify-full");
            if (sslModeStr == "disable") {
                sslConfig.mode = core::SSLMode::Disable;
            } else if (sslModeStr == "allow") {
                sslConfig.mode = core::SSLMode::Allow;
            } else if (sslModeStr == "prefer") {
                sslConfig.mode = core::SSLMode::Prefer;
            } else if (sslModeStr == "require") {
                sslConfig.mode = core::SSLMode::Require;
            } else if (sslModeStr == "verify-ca") {
                sslConfig.mode = core::SSLMode::VerifyCa;
            } else if (sslModeStr == "verify-full") {
                sslConfig.mode = core::SSLMode::VerifyFull;
            } else {
                utils::Logger::warning(std::format("Unknown SSL mode: {}, using verify-full", sslModeStr));
                sslConfig.mode = core::SSLMode::VerifyFull;
            }

            // Get certificate paths
            sslConfig.certPath = configManager.getString("database.ssl.cert_path", "");
            sslConfig.keyPath = configManager.getString("database.ssl.key_path", "");
            sslConfig.caPath = configManager.getString("database.ssl.ca_path", "");
            sslConfig.crlPath = configManager.getString("database.ssl.crl_path", "");
            sslConfig.rejectExpired = configManager.getBool("database.ssl.reject_expired", true);

            utils::Logger::info(std::format("SSL configuration: mode={}, cert={}, key={}, ca={}",
                sslModeStr,
                sslConfig.certPath.empty() ? "not set" : sslConfig.certPath,
                sslConfig.keyPath.empty() ? "not set" : sslConfig.keyPath,
                sslConfig.caPath.empty() ? "not set" : sslConfig.caPath));
        }

        // Initialize connection manager with connection pool
        utils::Logger::info("DATABASE_SERVICE: Initializing connection manager...");
        std::cout << "DATABASE_SERVICE: Initializing connection manager..." << std::endl;
        int maxConnections = configManager.getInt("database.pool.max_connections", 10);
        connectionManager_ = std::make_shared<core::ConnectionManager>(
            connectionString,
            maxConnections,
            sslConfig
        );
        utils::Logger::info(std::format("DATABASE_SERVICE: Initialized connection pool with {} connections", maxConnections));
        std::cout << "DATABASE_SERVICE: Connection manager initialized" << std::endl;

        // Initialize and configure security manager
        utils::Logger::info("DATABASE_SERVICE: Creating security manager...");
        std::cout << "DATABASE_SERVICE: Creating security manager..." << std::endl;
        securityManager_ = std::make_shared<security::SecurityManager>(connectionManager_);
        utils::Logger::info("DATABASE_SERVICE: Security manager created");
        std::cout << "DATABASE_SERVICE: Security manager created" << std::endl;

        // Configure JWT settings from configuration
        // Note: In production, JWT secret should be provided via environment variable or secure vault
        std::string jwtSecret = configManager.getString(
            "security.jwt_secret", 
            "change-this-to-a-secure-secret-in-production"
        );
        
        // Set token expiration times (default: 1 hour for access token, 1 day for refresh token)
        int tokenExpirationSeconds = configManager.getInt(
            "security.token_expiration_seconds", 
            3600  // 1 hour
        );
        int refreshTokenExpirationSeconds = configManager.getInt(
            "security.refresh_token_expiration_seconds", 
            86400  // 24 hours
        );

        // Apply security settings
        securityManager_->setJwtSecret(jwtSecret);
        securityManager_->setTokenExpirationTimes(tokenExpirationSeconds, refreshTokenExpirationSeconds);

        // Explicitly initialize the SecurityManager
        utils::Logger::info("DATABASE_SERVICE: Initializing security manager...");
        std::cout << "DATABASE_SERVICE: Initializing security manager..." << std::endl;
        auto securityInitResult = securityManager_->initialize();
        if (!securityInitResult) {
            std::string errorMsg = std::format("Failed to initialize security manager: {}", securityInitResult.error().message);
            utils::Logger::error(errorMsg);
            return std::unexpected(errorMsg);
        }
        utils::Logger::info("DATABASE_SERVICE: Security manager initialized successfully");
        std::cout << "DATABASE_SERVICE: Security manager initialized successfully" << std::endl;

        // Initialize secure credential storage if enabled
        // This provides encrypted storage for sensitive credentials
        bool enableSecureCredentialStorage = configManager.getBool(
            "security.secure_credential_storage.enabled", 
            false
        );
        
        if (enableSecureCredentialStorage) {
            utils::Logger::info("Initializing secure credential storage");
            
            // Get encryption key from configuration
            // IMPORTANT: In production, this should come from a secure source like a secrets manager
            std::string encryptionKey = configManager.getString(
                "security.secure_credential_storage.encryption_key", 
                ""
            );

            if (encryptionKey.empty()) {
                utils::Logger::warning(
                    "Secure credential storage is enabled but encryption key is not set. "
                    "Falling back to in-memory storage."
                );
            } else {
                // Initialize the credential store with the provided encryption key
                auto& credentialStore = security::CredentialStore::getInstance();
                if (!credentialStore.initialize(encryptionKey)) {
                    return std::unexpected(
                        "Failed to initialize secure credential storage. "
                        "Check encryption key and storage permissions."
                    );
                }
                utils::Logger::info("Secure credential storage initialized successfully");
            }
        }

        // Initialize schema manager
        // The schema manager handles database migrations and schema validation
        std::string schemaDir = configManager.getString("schema.directory", "");
        schemaManager_ = std::make_shared<schema::SchemaManager>(
            connectionManager_,
            schemaDir
        );
        
        if (schemaDir.empty()) {
            utils::Logger::warning("No schema directory specified. Schema validation and migrations will be disabled.");
        } else {
            utils::Logger::info(std::format("Using schema directory: {}", schemaDir));
        }

        // Initialize and configure the API server
        // The API server handles all HTTP requests and routes them to appropriate handlers
        utils::Logger::info("DATABASE_SERVICE: Configuring API server...");
        std::cout << "DATABASE_SERVICE: Configuring API server..." << std::endl;
        unsigned short apiPort = configManager.getInt("api.port", 8080);
        std::string apiHost = configManager.getString("api.host", "0.0.0.0");

        utils::Logger::info(std::format("DATABASE_SERVICE: Initializing API server on {}:{}", apiHost, apiPort));
        std::cout << "DATABASE_SERVICE: Creating API server instance..." << std::endl;

        // Get authentication setting from configuration
        bool enableAuthentication = configManager.getBool("security.enable_authentication", true);
        utils::Logger::info(std::format("Authentication enabled: {}", enableAuthentication ? "true" : "false"));

        // Create API server with dependency injection
        apiServer_ = std::make_shared<api::ApiServer>(
            apiPort,
            connectionManager_,
            securityManager_,
            shared_from_this(),  // Pass shared_ptr to this service
            enableAuthentication
        );
        utils::Logger::info("DATABASE_SERVICE: API server instance created");
        std::cout << "DATABASE_SERVICE: API server instance created" << std::endl;
        
        // Set API server host if configured
        if (!apiHost.empty() && apiHost != "0.0.0.0") {
            // Note: setHost method not available in current ApiServer interface
        }

        // Configure API server with SSL settings if enabled
        api::SSLConfig apiSslConfig;
        apiSslConfig.enabled = configManager.getBool("api.ssl.enabled", false);

        if (apiSslConfig.enabled) {
            utils::Logger::info("Configuring SSL for API server");
            
            // Get certificate and key paths from configuration
            apiSslConfig.certPath = configManager.getString("api.ssl.cert_path", "");
            apiSslConfig.keyPath = configManager.getString("api.ssl.key_path", "");
            // Note: caPath, ciphers, and requireClientCerts not available in current SSLConfig struct

            // Validate SSL configuration
            if (apiSslConfig.certPath.empty() || apiSslConfig.keyPath.empty()) {
                utils::Logger::warning(
                    "SSL is enabled but certificate or key path is not set. "
                    "Disabling SSL for API server."
                );
                apiSslConfig.enabled = false;
            } else {
                // Apply SSL configuration to API server
                apiServer_->configureSSL(apiSslConfig);
                utils::Logger::info("SSL configured successfully for API server");
            }
        }

        // Configure CORS (Cross-Origin Resource Sharing) settings if enabled
        api::CorsConfig corsConfig;
        corsConfig.enabled = configManager.getBool("api.cors.enabled", false);

        if (corsConfig.enabled) {
            utils::Logger::info("Configuring CORS for API server");

            // Load allowed origins from configuration
            // If none specified, default to allowing all origins ("*")
            auto originsJson = configManager.getJsonArray("api.cors.allowed_origins");
            if (!originsJson.empty()) {
                for (const auto& origin : originsJson) {
                    if (origin.is_string()) {
                        corsConfig.allowedOrigins.push_back(origin);
                        utils::Logger::debug(std::format("Added CORS allowed origin: {}", 
                            origin.get<std::string>()));
                    }
                }
            } else {
                // Default to all origins (not recommended for production)
                corsConfig.allowedOrigins.push_back("*");
                utils::Logger::warning("No CORS allowed origins specified. Allowing all origins (not recommended for production).");
            }

            // Configure allowed HTTP methods
            auto methodsJson = configManager.getJsonArray("api.cors.allowed_methods");
            if (!methodsJson.empty()) {
                for (const auto& method : methodsJson) {
                    if (method.is_string()) {
                        corsConfig.allowedMethods.push_back(method);
                    }
                }
            } else {
                // Default to common REST methods
                corsConfig.allowedMethods = {"GET", "POST", "PUT", "DELETE", "OPTIONS"};
                utils::Logger::debug("Using default CORS allowed methods");
            }

            // Configure allowed headers
            auto headersJson = configManager.getJsonArray("api.cors.allowed_headers");
            if (!headersJson.empty()) {
                for (const auto& header : headersJson) {
                    if (header.is_string()) {
                        corsConfig.allowedHeaders.push_back(header);
                    }
                }
            } else {
                // Default to common headers
                corsConfig.allowedHeaders = {
                    "Content-Type", 
                    "Authorization",
                    "X-Requested-With",
                    "Accept",
                    "Origin",
                    "Access-Control-Request-Method",
                    "Access-Control-Request-Headers"
                };
                utils::Logger::debug("Using default CORS allowed headers");
            }
            
            // Configure additional CORS settings
            corsConfig.allowCredentials = configManager.getBool("api.cors.allow_credentials", false);
            corsConfig.maxAge = configManager.getInt("api.cors.max_age", 86400);  // 24 hours default

            apiServer_->configureCors(corsConfig);
        }

        // Register API routes
        utils::Logger::info("DATABASE_SERVICE: Registering API routes...");
        std::cout << "DATABASE_SERVICE: Creating RouteController..." << std::endl;
        api::RouteController routeController(connectionManager_, securityManager_);
        utils::Logger::info("DATABASE_SERVICE: RouteController created, registering routes...");
        std::cout << "DATABASE_SERVICE: RouteController created, registering routes..." << std::endl;
        routeController.registerRoutes(*apiServer_);
        utils::Logger::info("DATABASE_SERVICE: API routes registered successfully");
        std::cout << "DATABASE_SERVICE: API routes registered successfully" << std::endl;

        initialized_.store(true, std::memory_order_relaxed);
        utils::Logger::info("Database service initialized successfully");
        return {};
    } catch (const std::exception& e) {
        std::string errorMsg = std::format("Failed to initialize database service: {}", e.what());
        utils::Logger::error(errorMsg);
        return std::unexpected(errorMsg);
    }
}

std::expected<void, std::string> DatabaseService::loadConfig(const std::string& configFile) {
    try {
        utils::Logger::info(std::format("Loading configuration from {}", configFile));

        auto& configManager = utils::ConfigManager::getInstance();

        // Load configuration from file
        if (!configManager.loadFromFile(configFile)) {
            std::string errorMsg = std::format("Failed to load configuration from file: {}", configFile);
            utils::Logger::error(errorMsg);
            return std::unexpected(errorMsg);
        }

        return {};
    } catch (const std::exception& e) {
        std::string errorMsg = std::format("Failed to load configuration: {}", e.what());
        utils::Logger::error(errorMsg);
        return std::unexpected(errorMsg);
    }
}

/**
 * @brief Start the database service and all its components
 *
 * Starts components in the following order:
 * 1. Connection pool
 * 2. Background workers
 * 3. API server
 *
 * @return std::expected<void, std::string>
 *         - Success: void
 *         - Error: Descriptive error message if startup fails
 */
std::expected<void, std::string> DatabaseService::start() {
    // Atomically check and set running state
    bool expected = false;
    if (!running_.compare_exchange_strong(expected, true)) {
        return std::unexpected("Service is already running");
    }

    try {
        utils::Logger::info("Starting database service components...");
        
        // Initialize connection pool
        // Connection manager is initialized in constructor

        // Start API server
        utils::Logger::info("Starting API server...");
        if (!apiServer_->start()) {
            running_ = false;
            return std::unexpected("Failed to start API server");
        }

        // Notify any threads waiting for service to start
        {
            std::lock_guard<std::mutex> lock(mutex_);
            initialized_ = true;
            cv_.notify_all();
        }

        utils::Logger::info("Database service started successfully");
        return {};
    } catch (const std::exception& e) {
        // Ensure clean state on failure
        running_ = false;
        initialized_ = false;
        
        std::string error = std::format("Error starting service: {}", e.what());
        utils::Logger::error(error);
        return std::unexpected(error);
    }
}

/**
 * @brief Stop the database service and all its components
 * 
 * Stops components in reverse order of initialization:
 * 1. API server
 * 2. Background workers
 * 3. Connection pool
 */
void DatabaseService::stop() {
    // Atomically check and set running state
    bool expected = true;
    if (!running_.compare_exchange_strong(expected, false)) {
        return; // Already stopped
    }

    try {
        utils::Logger::info("Initiating service shutdown...");
        
        // Stop API server if it exists
        if (apiServer_) {
            utils::Logger::debug("Stopping API server...");
            apiServer_->stop();
        }

        // Stop connection pool and cleanup resources
        if (connectionManager_) {
            utils::Logger::debug("Shutting down connection pool...");
            connectionManager_->shutdown();
        }

        // Notify all threads waiting in wait()
        {
            std::lock_guard<std::mutex> lock(waitMutex_);
            waitCondition_.notify_all();
        }
        
        utils::Logger::info("Service shutdown completed");
    } catch (const std::exception& e) {
        std::string error = std::format("Error during service shutdown: {}", e.what());
        utils::Logger::error(error);
        // Continue with shutdown even if some components fail
    }
    
    // Ensure we don't mark as initialized if we're shutting down
    initialized_ = false;
}

/**
 * @brief Block until the service is stopped
 * 
 * This method blocks the calling thread until the stop() method is called
 * or a shutdown signal is received. Useful for keeping the main thread alive
 * while the service is running.
 */
void DatabaseService::wait() {
    std::unique_lock<std::mutex> lock(waitMutex_);
    
    // Wait until running_ becomes false
    waitCondition_.wait(lock, [this] { 
        return !running_.load(std::memory_order_acquire); 
    });
    
    utils::Logger::debug("Wait condition released, service is stopping");
}

/**
 * @brief Check if the service is currently running
 * 
 * @return true If the service is running
 * @return false If the service is stopped or stopping
 */
bool DatabaseService::isRunning() const {
    return running_.load(std::memory_order_acquire);
}

/**
 * @brief Get the API server instance
 * 
 * @return std::shared_ptr<api::ApiServer> Shared pointer to the API server instance
 * @throws std::runtime_error if the service is not initialized
 */
std::shared_ptr<api::ApiServer> DatabaseService::getApiServer() const {
    if (!initialized_) {
        throw std::runtime_error("Service not initialized");
    }
    return apiServer_;
}

/**
 * @brief Get the connection manager instance
 * 
 * The connection manager handles the database connection pool and connection lifecycle.
 * 
 * @return std::shared_ptr<core::ConnectionManager> Shared pointer to the connection manager
 * @throws std::runtime_error if the service is not initialized
 */
std::shared_ptr<core::ConnectionManager> DatabaseService::getConnectionManager() const {
    if (!initialized_) {
        throw std::runtime_error("Service not initialized");
    }
    return connectionManager_;
}

/**
 * @brief Get comprehensive database service metrics
 * 
 * Collects and returns various metrics about the database service, including:
 * - Service status and uptime
 * - Connection pool statistics
 * - Current timestamp
 * 
 * @return std::expected<nlohmann::json, std::string> 
 *         - Success: JSON object containing service metrics
 *         - Error: Descriptive error message if metrics collection fails
 */
std::expected<nlohmann::json, std::string> DatabaseService::getDatabaseMetrics() const {
    try {
        nlohmann::json metrics;

        // Basic service status
        metrics["status"] = isRunning() ? "running" : "stopped";
        metrics["initialized"] = initialized_.load(std::memory_order_relaxed);
        
        // Note: Service uptime tracking not implemented yet

        // Connection pool metrics
        if (connectionManager_) {
            auto poolMetrics = connectionManager_->getMetrics();
            if (poolMetrics) {
                metrics["connection_pool"] = *poolMetrics;
            } else {
                metrics["connection_pool"] = "unavailable";
            }
        } else {
            metrics["connection_pool"] = "not_initialized";
        }

        // Add timestamp in ISO 8601 format
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        metrics["timestamp"] = std::format("{:%Y-%m-%dT%H:%M:%SZ}", 
            std::chrono::system_clock::from_time_t(time_t));

        // Add version information
        metrics["version"] = nlohmann::json{
            {"major", 1},
            {"minor", 0},
            {"patch", 0},
            {"build", "dev"}
        };

        return metrics;
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Failed to collect database metrics: {}", e.what()));
        return std::unexpected(std::format("Failed to get database metrics: {}", e.what()));
    }
}

/**
 * @brief Get query performance metrics
 * 
 * Returns statistics about query performance, including:
 * - Total number of queries executed
 * - Average query execution time
 * - Queries per second rate
 * - Slow query count and details
 * 
 * @note This is a placeholder implementation that needs to be implemented
 *       with actual query performance tracking.
 * 
 * @return std::expected<nlohmann::json, std::string> 
 *         - Success: JSON object with query performance metrics
 *         - Error: Descriptive error message if metrics are unavailable
 */
std::expected<nlohmann::json, std::string> DatabaseService::getQueryPerformanceMetrics() const {
    try {
        if (!initialized_) {
            return std::unexpected("Service not initialized");
        }

        nlohmann::json metrics;
        
        // TODO: Implement actual query performance tracking
        // This is a placeholder implementation
        metrics["total_queries"] = 0;
        metrics["average_query_time_ms"] = 0.0;
        metrics["queries_per_second"] = 0.0;
        metrics["slow_queries"] = 0;
        metrics["failed_queries"] = 0;
        metrics["slow_query_threshold_ms"] = 1000;  // Default 1 second threshold
        
        // Add timestamp in ISO 8601 format
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        metrics["timestamp"] = std::format("{:%Y-%m-%dT%H:%M:%SZ}", 
            std::chrono::system_clock::from_time_t(time_t));
            
        // Indicate this is a placeholder implementation
        metrics["status"] = "not_implemented";
        metrics["message"] = "Query performance tracking is not yet implemented";

        return metrics;
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Failed to collect query performance metrics: {}", e.what()));
        return std::unexpected(std::format("Failed to get query performance metrics: {}", e.what()));
    }
}

/**
 * @brief Get the schema manager instance
 * 
 * The schema manager handles database schema validation and migrations.
 * 
 * @return std::shared_ptr<schema::SchemaManager> Shared pointer to the schema manager
 * @throws std::runtime_error if the service is not initialized
 */
std::shared_ptr<schema::SchemaManager> DatabaseService::getSchemaManager() const {
    if (!initialized_) {
        throw std::runtime_error("Service not initialized");
    }
    return schemaManager_;
}

/**
 * @brief Get the security manager instance
 * 
 * The security manager handles authentication, authorization, and JWT token management.
 * 
 * @return std::shared_ptr<security::SecurityManager> Shared pointer to the security manager
 * @throws std::runtime_error if the service is not initialized
 */
std::shared_ptr<security::SecurityManager> DatabaseService::getSecurityManager() const {
    if (!initialized_) {
        throw std::runtime_error("Service not initialized");
    }
    return securityManager_;
}

/**
 * @brief Get detailed connection pool metrics
 * 
 * Retrieves metrics specifically about the connection pool, including:
 * - Number of active connections
 * - Number of idle connections
 * - Connection wait times
 * - Connection acquisition statistics
 * 
 * @return std::expected<nlohmann::json, std::string> 
 *         - Success: JSON object with connection pool metrics
 *         - Error: Descriptive error message if metrics are unavailable
 */
std::expected<nlohmann::json, std::string> DatabaseService::getConnectionPoolMetrics() const {
    try {
        if (!connectionManager_) {
            return std::unexpected("Connection manager not initialized");
        }

        auto metrics = connectionManager_->getMetrics();
        if (!metrics) {
            return std::unexpected("Connection pool metrics not available");
        }

        // Add timestamp to metrics
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        (*metrics)["timestamp"] = std::format("{:%Y-%m-%dT%H:%M:%SZ}", 
            std::chrono::system_clock::from_time_t(time_t));
            
        return *metrics;
    } catch (const std::exception& e) {
        utils::Logger::error(std::format("Failed to collect connection pool metrics: {}", e.what()));
        return std::unexpected(std::format("Failed to get connection pool metrics: {}", e.what()));
    }
}

} // namespace dbservice
