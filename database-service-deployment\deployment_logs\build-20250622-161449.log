Running CMake configuration...
-- The CXX compiler identification is GNU 14.2.0
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /usr/bin/g++-14 - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found PostgreSQL: /usr/lib/x86_64-linux-gnu/libpq.so (found version "17.5")
-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.13")
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE
-- pqxx package not found, will attempt to use system libraries
-- Tests disabled. Use -DBUILD_TESTS=ON to enable.
-- 
-- === Database Service Configuration Summary ===
-- Version: 1.0.0
-- Build type: Release
-- C++ standard: 23
-- Compiler: GNU 14.2.0
-- Build tests: OFF
-- Code coverage: OFF
-- Install prefix: /usr/local
-- 
-- Dependencies:
--   Boost: 1.83.0
--   PostgreSQL: 
--   OpenSSL: 3.0.13
--   nlohmann/json: Found
--   pqxx: System library
-- ===============================================
-- 
-- Configuring done (0.4s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/database-service-build/build
Starting compilation...
[  9%] Building CXX object CMakeFiles/database-service.dir/src/api/route_controller.cpp.o
[  9%] Building CXX object CMakeFiles/database-service.dir/src/api/api_server.cpp.o
/home/<USER>/database-service-build/src/api/route_controller.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/route_controller.cpp:133:16: error: ‘handleListDatabases’ was not declared in this scope
  133 |         return handleListDatabases(request);
      |                ^~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/api/route_controller.cpp: In member function ‘void dbservice::api::RouteController::registerRoutes(dbservice::api::ApiServer&)’:
/home/<USER>/database-service-build/src/api/route_controller.cpp:132:46: error: cannot convert ‘dbservice::api::RouteController::registerRoutes(dbservice::api::ApiServer&)::<lambda(const dbservice::api::ParsedRequest&)>’ to ‘dbservice::api::RouteHandler’ {aka ‘std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>’}
  132 |     server.addRoute("GET", "/api/databases", [this](const ParsedRequest& request) {
      |                                              ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
      |                                              |
      |                                              dbservice::api::RouteController::registerRoutes(dbservice::api::ApiServer&)::<lambda(const dbservice::api::ParsedRequest&)>
  133 |         return handleListDatabases(request);
      |         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~  
  134 |     });
      |     ~                                         
In file included from /home/<USER>/database-service-build/include/database-service/api/route_controller.hpp:3,
                 from /home/<USER>/database-service-build/src/api/route_controller.cpp:1:
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:126:100: note:   initializing argument 3 of ‘void dbservice::api::ApiServer::addRoute(const std::string&, const std::string&, dbservice::api::RouteHandler)’
  126 |     void addRoute(const std::string& method, const std::string& path, dbservice::api::RouteHandler handler);
      |                                                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~
/home/<USER>/database-service-build/src/api/route_controller.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/route_controller.cpp:137:16: error: ‘handleCreateDatabase’ was not declared in this scope
  137 |         return handleCreateDatabase(request);
      |                ^~~~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/api/route_controller.cpp: In member function ‘void dbservice::api::RouteController::registerRoutes(dbservice::api::ApiServer&)’:
/home/<USER>/database-service-build/src/api/route_controller.cpp:136:47: error: cannot convert ‘dbservice::api::RouteController::registerRoutes(dbservice::api::ApiServer&)::<lambda(const dbservice::api::ParsedRequest&)>’ to ‘dbservice::api::RouteHandler’ {aka ‘std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>’}
  136 |     server.addRoute("POST", "/api/databases", [this](const ParsedRequest& request) {
      |                                               ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
      |                                               |
      |                                               dbservice::api::RouteController::registerRoutes(dbservice::api::ApiServer&)::<lambda(const dbservice::api::ParsedRequest&)>
  137 |         return handleCreateDatabase(request);
      |         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~  
  138 |     });
      |     ~                                          
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:126:100: note:   initializing argument 3 of ‘void dbservice::api::ApiServer::addRoute(const std::string&, const std::string&, dbservice::api::RouteHandler)’
  126 |     void addRoute(const std::string& method, const std::string& path, dbservice::api::RouteHandler handler);
      |                                                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~
/home/<USER>/database-service-build/src/api/route_controller.cpp: In lambda function:
/home/<USER>/database-service-build/src/api/route_controller.cpp:141:16: error: ‘handleDropDatabase’ was not declared in this scope
  141 |         return handleDropDatabase(request);
      |                ^~~~~~~~~~~~~~~~~~
/home/<USER>/database-service-build/src/api/route_controller.cpp: In member function ‘void dbservice::api::RouteController::registerRoutes(dbservice::api::ApiServer&)’:
/home/<USER>/database-service-build/src/api/route_controller.cpp:140:49: error: cannot convert ‘dbservice::api::RouteController::registerRoutes(dbservice::api::ApiServer&)::<lambda(const dbservice::api::ParsedRequest&)>’ to ‘dbservice::api::RouteHandler’ {aka ‘std::function<std::expected<dbservice::api::Response, std::__cxx11::basic_string<char> >(const dbservice::api::ParsedRequest&)>’}
  140 |     server.addRoute("DELETE", "/api/databases", [this](const ParsedRequest& request) {
      |                                                 ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
      |                                                 |
      |                                                 dbservice::api::RouteController::registerRoutes(dbservice::api::ApiServer&)::<lambda(const dbservice::api::ParsedRequest&)>
  141 |         return handleDropDatabase(request);
      |         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~      
  142 |     });
      |     ~                                            
/home/<USER>/database-service-build/include/database-service/api/api_server.hpp:126:100: note:   initializing argument 3 of ‘void dbservice::api::ApiServer::addRoute(const std::string&, const std::string&, dbservice::api::RouteHandler)’
  126 |     void addRoute(const std::string& method, const std::string& path, dbservice::api::RouteHandler handler);
      |                                                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~
make[2]: *** [CMakeFiles/database-service.dir/build.make:93: CMakeFiles/database-service.dir/src/api/route_controller.cpp.o] Error 1
make[2]: *** Waiting for unfinished jobs....
make[1]: *** [CMakeFiles/Makefile2:87: CMakeFiles/database-service.dir/all] Error 2
make: *** [Makefile:136: all] Error 2
