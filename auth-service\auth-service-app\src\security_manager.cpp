﻿#include "security_manager.hpp"
#include "config_manager.hpp"
#include <iostream>
#include <random>
#include <iomanip>
#include <sstream>
#include <cstring>
#include <vector>
#include <stdexcept>
#include <argon2.h>

SecurityManager::SecurityManager(std::shared_ptr<ConfigManager> config)
    : config_(config) {
    std::cout << "SecurityManager initialized with Argon2id support" << std::endl;
}

SecurityManager::~SecurityManager() = default;

std::string SecurityManager::generate_salt() const {
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, 255);

    const int salt_length = config_->get_argon2_salt_length();
    std::string salt;
    salt.reserve(salt_length);

    for (int i = 0; i < salt_length; ++i) {
        salt.push_back(static_cast<char>(dis(gen)));
    }

    return salt;
}

std::string SecurityManager::hash_password(const std::string& password) const {
    std::string salt = generate_salt();
    return hash_password_with_salt(password, salt);
}

std::string SecurityManager::hash_password_with_salt(const std::string& password, const std::string& salt) const {
    std::cout << "Hashing password with Argon2id..." << std::endl;

    // Get Argon2 parameters from configuration
    const uint32_t memory_cost = static_cast<uint32_t>(config_->get_argon2_memory_cost());
    const uint32_t time_cost = static_cast<uint32_t>(config_->get_argon2_time_cost());
    const uint32_t parallelism = static_cast<uint32_t>(config_->get_argon2_parallelism());
    const uint32_t hash_length = 32; // 32 bytes = 256 bits

    // Prepare output buffer
    std::vector<uint8_t> hash_output(hash_length);

    // Hash the password using Argon2id
    int result = argon2id_hash_raw(
        time_cost,                                    // t_cost (iterations)
        memory_cost,                                  // m_cost (memory in KB)
        parallelism,                                  // parallelism (threads)
        password.c_str(),                            // pwd
        password.length(),                           // pwdlen
        reinterpret_cast<const uint8_t*>(salt.c_str()), // salt
        salt.length(),                               // saltlen
        hash_output.data(),                          // hash
        hash_length                                  // hashlen
    );

    if (result != ARGON2_OK) {
        std::cerr << "Argon2 hashing failed: " << argon2_error_message(result) << std::endl;
        throw std::runtime_error("Password hashing failed");
    }

    // Convert hash to hex string
    std::stringstream hash_hex;
    for (uint8_t byte : hash_output) {
        hash_hex << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(byte);
    }

    // Encode the complete hash with salt for storage
    return encode_argon2_hash(hash_hex.str(), salt);
}

bool SecurityManager::verify_password(const std::string& password, const std::string& encoded_hash) const {
    std::cout << "Verifying password with Argon2id..." << std::endl;

    // Decode the stored hash to get hash and salt
    std::string stored_hash, salt;
    if (!decode_argon2_hash(encoded_hash, stored_hash, salt)) {
        std::cerr << "Failed to decode stored hash" << std::endl;
        return false;
    }

    // Hash the provided password with the stored salt
    try {
        std::string computed_hash_encoded = hash_password_with_salt(password, salt);
        std::string computed_hash, computed_salt;

        if (!decode_argon2_hash(computed_hash_encoded, computed_hash, computed_salt)) {
            return false;
        }

        // Compare the hashes
        return stored_hash == computed_hash;
    } catch (const std::exception& e) {
        std::cerr << "Password verification failed: " << e.what() << std::endl;
        return false;
    }
}

std::string SecurityManager::generate_token() const {
    std::cout << "Generating token..." << std::endl;
    return "token_12345";
}

bool SecurityManager::validate_token(const std::string& token) const {
    std::cout << "Validating token..." << std::endl;
    return token == "token_12345";
}

// Helper method to encode hash and salt for storage
std::string SecurityManager::encode_argon2_hash(const std::string& hash, const std::string& salt) const {
    // Convert salt to hex
    std::stringstream salt_hex;
    for (unsigned char byte : salt) {
        salt_hex << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(byte);
    }

    // Format: $argon2id$hash$salt
    return "$argon2id$" + hash + "$" + salt_hex.str();
}

// Helper method to decode stored hash to get hash and salt
bool SecurityManager::decode_argon2_hash(const std::string& encoded_hash, std::string& hash, std::string& salt) const {
    // Expected format: $argon2id$hash$salt
    if (encoded_hash.substr(0, 9) != "$argon2id") {
        return false;
    }

    // Find the positions of the separators
    size_t first_sep = encoded_hash.find('$', 1);
    size_t second_sep = encoded_hash.find('$', first_sep + 1);
    size_t third_sep = encoded_hash.find('$', second_sep + 1);

    if (first_sep == std::string::npos || second_sep == std::string::npos || third_sep == std::string::npos) {
        return false;
    }

    // Extract hash and salt hex strings
    hash = encoded_hash.substr(second_sep + 1, third_sep - second_sep - 1);
    std::string salt_hex = encoded_hash.substr(third_sep + 1);

    // Convert salt from hex back to binary
    salt.clear();
    for (size_t i = 0; i < salt_hex.length(); i += 2) {
        if (i + 1 < salt_hex.length()) {
            std::string byte_str = salt_hex.substr(i, 2);
            char byte = static_cast<char>(std::stoi(byte_str, nullptr, 16));
            salt.push_back(byte);
        }
    }

    return true;
}
