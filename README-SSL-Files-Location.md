# Auth-Service SSL Files Location Guide

## 📁 **Current Location**
All SSL files are currently in: `d:\Augment\project-tracker\`

## 🎯 **Where to Copy These Files**

### **1. C++23 Certificate Sync Helper**
- **Source File**: `auth_cert_sync_helper.cpp`
- **Destination**: `D:\Coding_Projects\auth-service\cert_sync_helper_app\auth_cert_sync_helper.cpp`
- **Purpose**: Enhanced version of your existing cert_sync_helper for auth-service

### **2. Certificate Sync Script**
- **Source File**: `sync-auth-certificates.sh`
- **Destination**: `D:\Coding_Projects\auth-service\cert_sync_helper_app\sync-auth-certificates.sh`
- **Deploy To**: `/opt/auth-service/scripts/sync-auth-certificates.sh` (on project-tracker.chcit.org)
- **Purpose**: Distributes certificates to auth-service dev and prod servers

### **3. PowerShell SSL Module**
- **Source File**: `Setup-AuthServiceSSL.psm1`
- **Destination**: `D:\Coding_Projects\auth-service\auth-service-deployment\deployment_scripts\Modules\Setup-AuthServiceSSL.psm1`
- **Purpose**: Adds SSL management functions to deployment script

## 🔧 **Copy Commands**

Run these PowerShell commands to copy files to correct locations:

```powershell
# Create directories if they don't exist
New-Item -Path "D:\Coding_Projects\auth-service\cert_sync_helper_app" -ItemType Directory -Force
New-Item -Path "D:\Coding_Projects\auth-service\auth-service-deployment\deployment_scripts\Modules" -ItemType Directory -Force

# Copy C++ files
Copy-Item "auth_cert_sync_helper.cpp" "D:\Coding_Projects\auth-service\cert_sync_helper_app\"
Copy-Item "sync-auth-certificates.sh" "D:\Coding_Projects\auth-service\cert_sync_helper_app\"

# Copy PowerShell module
Copy-Item "Setup-AuthServiceSSL.psm1" "D:\Coding_Projects\auth-service\auth-service-deployment\deployment_scripts\Modules\"
```

## 📋 **Next Steps After Copying**

### **1. Compile C++23 Helper**
```bash
cd D:\Coding_Projects\auth-service\cert_sync_helper_app
g++ -std=c++23 -o auth_cert_sync_helper auth_cert_sync_helper.cpp
```

### **2. Deploy to project-tracker.chcit.org**
- Copy `auth_cert_sync_helper` binary to project-tracker server
- Copy `sync-auth-certificates.sh` to `/opt/auth-service/scripts/`
- Set proper permissions (setuid for helper, executable for script)

### **3. Add SSL Menu Options**
The PowerShell module adds these new deployment menu options:
- **Menu 22**: Setup SSL Certificates
- **Menu 23**: Sync SSL Certificates  
- **Menu 24**: Verify SSL Configuration
- **Menu 25**: Test HTTPS Endpoints

## 🔒 **SSL Endpoints**
Once implemented, auth-service will be available at:
- **Development**: `https://auth-dev.chcit.org:8082`
- **Production**: `https://auth.chcit.org:8082`

## ✅ **Benefits**
- **Reuses existing *.chcit.org wildcard certificate**
- **Builds on proven cert_sync_helper pattern**
- **Automated certificate distribution and renewal**
- **Production-grade SSL security for authentication endpoints**
