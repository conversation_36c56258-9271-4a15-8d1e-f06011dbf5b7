function e(e,t){for(var n=0;n<t.length;n++){const a=t[n];if("string"!=typeof a&&!Array.isArray(a))for(const t in a)if("default"!==t&&!(t in e)){const n=Object.getOwnPropertyDescriptor(a,t);n&&Object.defineProperty(e,t,n.get?n:{enumerable:!0,get:()=>a[t]})}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}function t(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver(e=>{for(const n of e)if("childList"===n.type)for(const e of n.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)}).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();var n={exports:{}},a={},r={exports:{}},l={},s=Symbol.for("react.element"),i=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),c=Symbol.for("react.strict_mode"),u=Symbol.for("react.profiler"),d=Symbol.for("react.provider"),f=Symbol.for("react.context"),h=Symbol.for("react.forward_ref"),m=Symbol.for("react.suspense"),p=Symbol.for("react.memo"),v=Symbol.for("react.lazy"),g=Symbol.iterator;var y={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},x=Object.assign,b={};function j(e,t,n){this.props=e,this.context=t,this.refs=b,this.updater=n||y}function w(){}function k(e,t,n){this.props=e,this.context=t,this.refs=b,this.updater=n||y}j.prototype.isReactComponent={},j.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},j.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},w.prototype=j.prototype;var S=k.prototype=new w;S.constructor=k,x(S,j.prototype),S.isPureReactComponent=!0;var N=Array.isArray,_=Object.prototype.hasOwnProperty,C={current:null},E={key:!0,ref:!0,__self:!0,__source:!0};function P(e,t,n){var a,r={},l=null,i=null;if(null!=t)for(a in void 0!==t.ref&&(i=t.ref),void 0!==t.key&&(l=""+t.key),t)_.call(t,a)&&!E.hasOwnProperty(a)&&(r[a]=t[a]);var o=arguments.length-2;if(1===o)r.children=n;else if(1<o){for(var c=Array(o),u=0;u<o;u++)c[u]=arguments[u+2];r.children=c}if(e&&e.defaultProps)for(a in o=e.defaultProps)void 0===r[a]&&(r[a]=o[a]);return{$$typeof:s,type:e,key:l,ref:i,props:r,_owner:C.current}}function T(e){return"object"==typeof e&&null!==e&&e.$$typeof===s}var L=/\/+/g;function R(e,t){return"object"==typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(e){return t[e]})}(""+e.key):t.toString(36)}function D(e,t,n,a,r){var l=typeof e;"undefined"!==l&&"boolean"!==l||(e=null);var o=!1;if(null===e)o=!0;else switch(l){case"string":case"number":o=!0;break;case"object":switch(e.$$typeof){case s:case i:o=!0}}if(o)return r=r(o=e),e=""===a?"."+R(o,0):a,N(r)?(n="",null!=e&&(n=e.replace(L,"$&/")+"/"),D(r,t,n,"",function(e){return e})):null!=r&&(T(r)&&(r=function(e,t){return{$$typeof:s,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(r,n+(!r.key||o&&o.key===r.key?"":(""+r.key).replace(L,"$&/")+"/")+e)),t.push(r)),1;if(o=0,a=""===a?".":a+":",N(e))for(var c=0;c<e.length;c++){var u=a+R(l=e[c],c);o+=D(l,t,n,u,r)}else if(u=function(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=g&&e[g]||e["@@iterator"])?e:null}(e),"function"==typeof u)for(e=u.call(e),c=0;!(l=e.next()).done;)o+=D(l=l.value,t,n,u=a+R(l,c++),r);else if("object"===l)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return o}function I(e,t,n){if(null==e)return e;var a=[],r=0;return D(e,a,"","",function(e){return t.call(n,e,r++)}),a}function A(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)},function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var F={current:null},O={transition:null},z={ReactCurrentDispatcher:F,ReactCurrentBatchConfig:O,ReactCurrentOwner:C};function M(){throw Error("act(...) is not supported in production builds of React.")}l.Children={map:I,forEach:function(e,t,n){I(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return I(e,function(){t++}),t},toArray:function(e){return I(e,function(e){return e})||[]},only:function(e){if(!T(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},l.Component=j,l.Fragment=o,l.Profiler=u,l.PureComponent=k,l.StrictMode=c,l.Suspense=m,l.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=z,l.act=M,l.cloneElement=function(e,t,n){if(null==e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var a=x({},e.props),r=e.key,l=e.ref,i=e._owner;if(null!=t){if(void 0!==t.ref&&(l=t.ref,i=C.current),void 0!==t.key&&(r=""+t.key),e.type&&e.type.defaultProps)var o=e.type.defaultProps;for(c in t)_.call(t,c)&&!E.hasOwnProperty(c)&&(a[c]=void 0===t[c]&&void 0!==o?o[c]:t[c])}var c=arguments.length-2;if(1===c)a.children=n;else if(1<c){o=Array(c);for(var u=0;u<c;u++)o[u]=arguments[u+2];a.children=o}return{$$typeof:s,type:e.type,key:r,ref:l,props:a,_owner:i}},l.createContext=function(e){return(e={$$typeof:f,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:d,_context:e},e.Consumer=e},l.createElement=P,l.createFactory=function(e){var t=P.bind(null,e);return t.type=e,t},l.createRef=function(){return{current:null}},l.forwardRef=function(e){return{$$typeof:h,render:e}},l.isValidElement=T,l.lazy=function(e){return{$$typeof:v,_payload:{_status:-1,_result:e},_init:A}},l.memo=function(e,t){return{$$typeof:p,type:e,compare:void 0===t?null:t}},l.startTransition=function(e){var t=O.transition;O.transition={};try{e()}finally{O.transition=t}},l.unstable_act=M,l.useCallback=function(e,t){return F.current.useCallback(e,t)},l.useContext=function(e){return F.current.useContext(e)},l.useDebugValue=function(){},l.useDeferredValue=function(e){return F.current.useDeferredValue(e)},l.useEffect=function(e,t){return F.current.useEffect(e,t)},l.useId=function(){return F.current.useId()},l.useImperativeHandle=function(e,t,n){return F.current.useImperativeHandle(e,t,n)},l.useInsertionEffect=function(e,t){return F.current.useInsertionEffect(e,t)},l.useLayoutEffect=function(e,t){return F.current.useLayoutEffect(e,t)},l.useMemo=function(e,t){return F.current.useMemo(e,t)},l.useReducer=function(e,t,n){return F.current.useReducer(e,t,n)},l.useRef=function(e){return F.current.useRef(e)},l.useState=function(e){return F.current.useState(e)},l.useSyncExternalStore=function(e,t,n){return F.current.useSyncExternalStore(e,t,n)},l.useTransition=function(){return F.current.useTransition()},l.version="18.3.1",r.exports=l;var U=r.exports;const $=t(U),q=e({__proto__:null,default:$},[U]);
/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var B=U,V=Symbol.for("react.element"),Q=Symbol.for("react.fragment"),W=Object.prototype.hasOwnProperty,H=B.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,K={key:!0,ref:!0,__self:!0,__source:!0};function Y(e,t,n){var a,r={},l=null,s=null;for(a in void 0!==n&&(l=""+n),void 0!==t.key&&(l=""+t.key),void 0!==t.ref&&(s=t.ref),t)W.call(t,a)&&!K.hasOwnProperty(a)&&(r[a]=t[a]);if(e&&e.defaultProps)for(a in t=e.defaultProps)void 0===r[a]&&(r[a]=t[a]);return{$$typeof:V,type:e,key:l,ref:s,props:r,_owner:H.current}}a.Fragment=Q,a.jsx=Y,a.jsxs=Y,n.exports=a;var J=n.exports,G={},X={exports:{}},Z={},ee={exports:{}},te={};
/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
!function(e){function t(e,t){var n=e.length;e.push(t);e:for(;0<n;){var a=n-1>>>1,l=e[a];if(!(0<r(l,t)))break e;e[a]=t,e[n]=l,n=a}}function n(e){return 0===e.length?null:e[0]}function a(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var a=0,l=e.length,s=l>>>1;a<s;){var i=2*(a+1)-1,o=e[i],c=i+1,u=e[c];if(0>r(o,n))c<l&&0>r(u,o)?(e[a]=u,e[c]=n,a=c):(e[a]=o,e[i]=n,a=i);else{if(!(c<l&&0>r(u,n)))break e;e[a]=u,e[c]=n,a=c}}}return t}function r(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if("object"==typeof performance&&"function"==typeof performance.now){var l=performance;e.unstable_now=function(){return l.now()}}else{var s=Date,i=s.now();e.unstable_now=function(){return s.now()-i}}var o=[],c=[],u=1,d=null,f=3,h=!1,m=!1,p=!1,v="function"==typeof setTimeout?setTimeout:null,g="function"==typeof clearTimeout?clearTimeout:null,y="undefined"!=typeof setImmediate?setImmediate:null;function x(e){for(var r=n(c);null!==r;){if(null===r.callback)a(c);else{if(!(r.startTime<=e))break;a(c),r.sortIndex=r.expirationTime,t(o,r)}r=n(c)}}function b(e){if(p=!1,x(e),!m)if(null!==n(o))m=!0,R(j);else{var t=n(c);null!==t&&D(b,t.startTime-e)}}function j(t,r){m=!1,p&&(p=!1,g(N),N=-1),h=!0;var l=f;try{for(x(r),d=n(o);null!==d&&(!(d.expirationTime>r)||t&&!E());){var s=d.callback;if("function"==typeof s){d.callback=null,f=d.priorityLevel;var i=s(d.expirationTime<=r);r=e.unstable_now(),"function"==typeof i?d.callback=i:d===n(o)&&a(o),x(r)}else a(o);d=n(o)}if(null!==d)var u=!0;else{var v=n(c);null!==v&&D(b,v.startTime-r),u=!1}return u}finally{d=null,f=l,h=!1}}"undefined"!=typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var w,k=!1,S=null,N=-1,_=5,C=-1;function E(){return!(e.unstable_now()-C<_)}function P(){if(null!==S){var t=e.unstable_now();C=t;var n=!0;try{n=S(!0,t)}finally{n?w():(k=!1,S=null)}}else k=!1}if("function"==typeof y)w=function(){y(P)};else if("undefined"!=typeof MessageChannel){var T=new MessageChannel,L=T.port2;T.port1.onmessage=P,w=function(){L.postMessage(null)}}else w=function(){v(P,0)};function R(e){S=e,k||(k=!0,w())}function D(t,n){N=v(function(){t(e.unstable_now())},n)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(e){e.callback=null},e.unstable_continueExecution=function(){m||h||(m=!0,R(j))},e.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):_=0<e?Math.floor(1e3/e):5},e.unstable_getCurrentPriorityLevel=function(){return f},e.unstable_getFirstCallbackNode=function(){return n(o)},e.unstable_next=function(e){switch(f){case 1:case 2:case 3:var t=3;break;default:t=f}var n=f;f=t;try{return e()}finally{f=n}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=f;f=e;try{return t()}finally{f=n}},e.unstable_scheduleCallback=function(a,r,l){var s=e.unstable_now();switch("object"==typeof l&&null!==l?l="number"==typeof(l=l.delay)&&0<l?s+l:s:l=s,a){case 1:var i=-1;break;case 2:i=250;break;case 5:i=**********;break;case 4:i=1e4;break;default:i=5e3}return a={id:u++,callback:r,priorityLevel:a,startTime:l,expirationTime:i=l+i,sortIndex:-1},l>s?(a.sortIndex=l,t(c,a),null===n(o)&&a===n(c)&&(p?(g(N),N=-1):p=!0,D(b,l-s))):(a.sortIndex=i,t(o,a),m||h||(m=!0,R(j))),a},e.unstable_shouldYield=E,e.unstable_wrapCallback=function(e){var t=f;return function(){var n=f;f=t;try{return e.apply(this,arguments)}finally{f=n}}}}(te),ee.exports=te;var ne=ee.exports,ae=U,re=ne;
/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */function le(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var se=new Set,ie={};function oe(e,t){ce(e,t),ce(e+"Capture",t)}function ce(e,t){for(ie[e]=t,e=0;e<t.length;e++)se.add(t[e])}var ue=!("undefined"==typeof window||void 0===window.document||void 0===window.document.createElement),de=Object.prototype.hasOwnProperty,fe=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,he={},me={};function pe(e,t,n,a,r,l,s){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=a,this.attributeNamespace=r,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=l,this.removeEmptyString=s}var ve={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){ve[e]=new pe(e,0,!1,e,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];ve[t]=new pe(t,1,!1,e[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(e){ve[e]=new pe(e,2,!1,e.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){ve[e]=new pe(e,2,!1,e,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){ve[e]=new pe(e,3,!1,e.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(e){ve[e]=new pe(e,3,!0,e,null,!1,!1)}),["capture","download"].forEach(function(e){ve[e]=new pe(e,4,!1,e,null,!1,!1)}),["cols","rows","size","span"].forEach(function(e){ve[e]=new pe(e,6,!1,e,null,!1,!1)}),["rowSpan","start"].forEach(function(e){ve[e]=new pe(e,5,!1,e.toLowerCase(),null,!1,!1)});var ge=/[\-:]([a-z])/g;function ye(e){return e[1].toUpperCase()}function xe(e,t,n,a){var r=ve.hasOwnProperty(t)?ve[t]:null;(null!==r?0!==r.type:a||!(2<t.length)||"o"!==t[0]&&"O"!==t[0]||"n"!==t[1]&&"N"!==t[1])&&(function(e,t,n,a){if(null==t||function(e,t,n,a){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!a&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,a))return!0;if(a)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,r,a)&&(n=null),a||null===r?function(e){return!!de.call(me,e)||!de.call(he,e)&&(fe.test(e)?me[e]=!0:(he[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):r.mustUseProperty?e[r.propertyName]=null===n?3!==r.type&&"":n:(t=r.attributeName,a=r.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(r=r.type)||4===r&&!0===n?"":""+n,a?e.setAttributeNS(a,t,n):e.setAttribute(t,n))))}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(ge,ye);ve[t]=new pe(t,1,!1,e,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(ge,ye);ve[t]=new pe(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(ge,ye);ve[t]=new pe(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(e){ve[e]=new pe(e,1,!1,e.toLowerCase(),null,!1,!1)}),ve.xlinkHref=new pe("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(e){ve[e]=new pe(e,1,!1,e.toLowerCase(),null,!0,!0)});var be=ae.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,je=Symbol.for("react.element"),we=Symbol.for("react.portal"),ke=Symbol.for("react.fragment"),Se=Symbol.for("react.strict_mode"),Ne=Symbol.for("react.profiler"),_e=Symbol.for("react.provider"),Ce=Symbol.for("react.context"),Ee=Symbol.for("react.forward_ref"),Pe=Symbol.for("react.suspense"),Te=Symbol.for("react.suspense_list"),Le=Symbol.for("react.memo"),Re=Symbol.for("react.lazy"),De=Symbol.for("react.offscreen"),Ie=Symbol.iterator;function Ae(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=Ie&&e[Ie]||e["@@iterator"])?e:null}var Fe,Oe=Object.assign;function ze(e){if(void 0===Fe)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Fe=t&&t[1]||""}return"\n"+Fe+e}var Me=!1;function Ue(e,t){if(!e||Me)return"";Me=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"==typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var a=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){a=c}e.call(t.prototype)}else{try{throw Error()}catch(c){a=c}e()}}catch(c){if(c&&a&&"string"==typeof c.stack){for(var r=c.stack.split("\n"),l=a.stack.split("\n"),s=r.length-1,i=l.length-1;1<=s&&0<=i&&r[s]!==l[i];)i--;for(;1<=s&&0<=i;s--,i--)if(r[s]!==l[i]){if(1!==s||1!==i)do{if(s--,0>--i||r[s]!==l[i]){var o="\n"+r[s].replace(" at new "," at ");return e.displayName&&o.includes("<anonymous>")&&(o=o.replace("<anonymous>",e.displayName)),o}}while(1<=s&&0<=i);break}}}finally{Me=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?ze(e):""}function $e(e){switch(e.tag){case 5:return ze(e.type);case 16:return ze("Lazy");case 13:return ze("Suspense");case 19:return ze("SuspenseList");case 0:case 2:case 15:return e=Ue(e.type,!1);case 11:return e=Ue(e.type.render,!1);case 1:return e=Ue(e.type,!0);default:return""}}function qe(e){if(null==e)return null;if("function"==typeof e)return e.displayName||e.name||null;if("string"==typeof e)return e;switch(e){case ke:return"Fragment";case we:return"Portal";case Ne:return"Profiler";case Se:return"StrictMode";case Pe:return"Suspense";case Te:return"SuspenseList"}if("object"==typeof e)switch(e.$$typeof){case Ce:return(e.displayName||"Context")+".Consumer";case _e:return(e._context.displayName||"Context")+".Provider";case Ee:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case Le:return null!==(t=e.displayName||null)?t:qe(e.type)||"Memo";case Re:t=e._payload,e=e._init;try{return qe(e(t))}catch(n){}}return null}function Be(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=(e=t.render).displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return qe(t);case 8:return t===Se?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"==typeof t)return t.displayName||t.name||null;if("string"==typeof t)return t}return null}function Ve(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function Qe(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function We(e){e._valueTracker||(e._valueTracker=function(e){var t=Qe(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),a=""+e[t];if(!e.hasOwnProperty(t)&&void 0!==n&&"function"==typeof n.get&&"function"==typeof n.set){var r=n.get,l=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return r.call(this)},set:function(e){a=""+e,l.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return a},setValue:function(e){a=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function He(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),a="";return e&&(a=Qe(e)?e.checked?"true":"false":e.value),(e=a)!==n&&(t.setValue(e),!0)}function Ke(e){if(void 0===(e=e||("undefined"!=typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function Ye(e,t){var n=t.checked;return Oe({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function Je(e,t){var n=null==t.defaultValue?"":t.defaultValue,a=null!=t.checked?t.checked:t.defaultChecked;n=Ve(null!=t.value?t.value:n),e._wrapperState={initialChecked:a,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function Ge(e,t){null!=(t=t.checked)&&xe(e,"checked",t,!1)}function Xe(e,t){Ge(e,t);var n=Ve(t.value),a=t.type;if(null!=n)"number"===a?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===a||"reset"===a)return void e.removeAttribute("value");t.hasOwnProperty("value")?et(e,t.type,n):t.hasOwnProperty("defaultValue")&&et(e,t.type,Ve(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function Ze(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var a=t.type;if(!("submit"!==a&&"reset"!==a||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function et(e,t,n){"number"===t&&Ke(e.ownerDocument)===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var tt=Array.isArray;function nt(e,t,n,a){if(e=e.options,t){t={};for(var r=0;r<n.length;r++)t["$"+n[r]]=!0;for(n=0;n<e.length;n++)r=t.hasOwnProperty("$"+e[n].value),e[n].selected!==r&&(e[n].selected=r),r&&a&&(e[n].defaultSelected=!0)}else{for(n=""+Ve(n),t=null,r=0;r<e.length;r++){if(e[r].value===n)return e[r].selected=!0,void(a&&(e[r].defaultSelected=!0));null!==t||e[r].disabled||(t=e[r])}null!==t&&(t.selected=!0)}}function at(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(le(91));return Oe({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function rt(e,t){var n=t.value;if(null==n){if(n=t.children,t=t.defaultValue,null!=n){if(null!=t)throw Error(le(92));if(tt(n)){if(1<n.length)throw Error(le(93));n=n[0]}t=n}null==t&&(t=""),n=t}e._wrapperState={initialValue:Ve(n)}}function lt(e,t){var n=Ve(t.value),a=Ve(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=a&&(e.defaultValue=""+a)}function st(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}function it(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function ot(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?it(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var ct,ut,dt=(ut=function(e,t){if("http://www.w3.org/2000/svg"!==e.namespaceURI||"innerHTML"in e)e.innerHTML=t;else{for((ct=ct||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ct.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}},"undefined"!=typeof MSApp&&MSApp.execUnsafeLocalFunction?function(e,t,n,a){MSApp.execUnsafeLocalFunction(function(){return ut(e,t)})}:ut);function ft(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var ht={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},mt=["Webkit","ms","Moz","O"];function pt(e,t,n){return null==t||"boolean"==typeof t||""===t?"":n||"number"!=typeof t||0===t||ht.hasOwnProperty(e)&&ht[e]?(""+t).trim():t+"px"}function vt(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var a=0===n.indexOf("--"),r=pt(n,t[n],a);"float"===n&&(n="cssFloat"),a?e.setProperty(n,r):e[n]=r}}Object.keys(ht).forEach(function(e){mt.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),ht[t]=ht[e]})});var gt=Oe({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function yt(e,t){if(t){if(gt[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(le(137,e));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(le(60));if("object"!=typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(le(61))}if(null!=t.style&&"object"!=typeof t.style)throw Error(le(62))}}function xt(e,t){if(-1===e.indexOf("-"))return"string"==typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var bt=null;function jt(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var wt=null,kt=null,St=null;function Nt(e){if(e=xl(e)){if("function"!=typeof wt)throw Error(le(280));var t=e.stateNode;t&&(t=jl(t),wt(e.stateNode,e.type,t))}}function _t(e){kt?St?St.push(e):St=[e]:kt=e}function Ct(){if(kt){var e=kt,t=St;if(St=kt=null,Nt(e),t)for(e=0;e<t.length;e++)Nt(t[e])}}function Et(e,t){return e(t)}function Pt(){}var Tt=!1;function Lt(e,t,n){if(Tt)return e(t,n);Tt=!0;try{return Et(e,t,n)}finally{Tt=!1,(null!==kt||null!==St)&&(Pt(),Ct())}}function Rt(e,t){var n=e.stateNode;if(null===n)return null;var a=jl(n);if(null===a)return null;n=a[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(a=!a.disabled)||(a=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!a;break e;default:e=!1}if(e)return null;if(n&&"function"!=typeof n)throw Error(le(231,t,typeof n));return n}var Dt=!1;if(ue)try{var It={};Object.defineProperty(It,"passive",{get:function(){Dt=!0}}),window.addEventListener("test",It,It),window.removeEventListener("test",It,It)}catch(ut){Dt=!1}function At(e,t,n,a,r,l,s,i,o){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(u){this.onError(u)}}var Ft=!1,Ot=null,zt=!1,Mt=null,Ut={onError:function(e){Ft=!0,Ot=e}};function $t(e,t,n,a,r,l,s,i,o){Ft=!1,Ot=null,At.apply(Ut,arguments)}function qt(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{!!(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function Bt(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function Vt(e){if(qt(e)!==e)throw Error(le(188))}function Qt(e){return null!==(e=function(e){var t=e.alternate;if(!t){if(null===(t=qt(e)))throw Error(le(188));return t!==e?null:e}for(var n=e,a=t;;){var r=n.return;if(null===r)break;var l=r.alternate;if(null===l){if(null!==(a=r.return)){n=a;continue}break}if(r.child===l.child){for(l=r.child;l;){if(l===n)return Vt(r),e;if(l===a)return Vt(r),t;l=l.sibling}throw Error(le(188))}if(n.return!==a.return)n=r,a=l;else{for(var s=!1,i=r.child;i;){if(i===n){s=!0,n=r,a=l;break}if(i===a){s=!0,a=r,n=l;break}i=i.sibling}if(!s){for(i=l.child;i;){if(i===n){s=!0,n=l,a=r;break}if(i===a){s=!0,a=l,n=r;break}i=i.sibling}if(!s)throw Error(le(189))}}if(n.alternate!==a)throw Error(le(190))}if(3!==n.tag)throw Error(le(188));return n.stateNode.current===n?e:t}(e))?Wt(e):null}function Wt(e){if(5===e.tag||6===e.tag)return e;for(e=e.child;null!==e;){var t=Wt(e);if(null!==t)return t;e=e.sibling}return null}var Ht=re.unstable_scheduleCallback,Kt=re.unstable_cancelCallback,Yt=re.unstable_shouldYield,Jt=re.unstable_requestPaint,Gt=re.unstable_now,Xt=re.unstable_getCurrentPriorityLevel,Zt=re.unstable_ImmediatePriority,en=re.unstable_UserBlockingPriority,tn=re.unstable_NormalPriority,nn=re.unstable_LowPriority,an=re.unstable_IdlePriority,rn=null,ln=null;var sn=Math.clz32?Math.clz32:function(e){return e>>>=0,0===e?32:31-(on(e)/cn|0)|0},on=Math.log,cn=Math.LN2;var un=64,dn=4194304;function fn(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194240&e;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return 130023424&e;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function hn(e,t){var n=e.pendingLanes;if(0===n)return 0;var a=0,r=e.suspendedLanes,l=e.pingedLanes,s=268435455&n;if(0!==s){var i=s&~r;0!==i?a=fn(i):0!==(l&=s)&&(a=fn(l))}else 0!==(s=n&~r)?a=fn(s):0!==l&&(a=fn(l));if(0===a)return 0;if(0!==t&&t!==a&&0===(t&r)&&((r=a&-a)>=(l=t&-t)||16===r&&4194240&l))return t;if(4&a&&(a|=16&n),0!==(t=e.entangledLanes))for(e=e.entanglements,t&=a;0<t;)r=1<<(n=31-sn(t)),a|=e[n],t&=~r;return a}function mn(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function pn(e){return 0!==(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function vn(){var e=un;return!(4194240&(un<<=1))&&(un=64),e}function gn(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function yn(e,t,n){e.pendingLanes|=t,536870912!==t&&(e.suspendedLanes=0,e.pingedLanes=0),(e=e.eventTimes)[t=31-sn(t)]=n}function xn(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var a=31-sn(n),r=1<<a;r&t|e[a]&t&&(e[a]|=t),n&=~r}}var bn=0;function jn(e){return 1<(e&=-e)?4<e?268435455&e?16:536870912:4:1}var wn,kn,Sn,Nn,_n,Cn=!1,En=[],Pn=null,Tn=null,Ln=null,Rn=new Map,Dn=new Map,In=[],An="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Fn(e,t){switch(e){case"focusin":case"focusout":Pn=null;break;case"dragenter":case"dragleave":Tn=null;break;case"mouseover":case"mouseout":Ln=null;break;case"pointerover":case"pointerout":Rn.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Dn.delete(t.pointerId)}}function On(e,t,n,a,r,l){return null===e||e.nativeEvent!==l?(e={blockedOn:t,domEventName:n,eventSystemFlags:a,nativeEvent:l,targetContainers:[r]},null!==t&&(null!==(t=xl(t))&&kn(t)),e):(e.eventSystemFlags|=a,t=e.targetContainers,null!==r&&-1===t.indexOf(r)&&t.push(r),e)}function zn(e){var t=yl(e.target);if(null!==t){var n=qt(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=Bt(n)))return e.blockedOn=t,void _n(e.priority,function(){Sn(n)})}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function Mn(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=Jn(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==n)return null!==(t=xl(n))&&kn(t),e.blockedOn=n,!1;var a=new(n=e.nativeEvent).constructor(n.type,n);bt=a,n.target.dispatchEvent(a),bt=null,t.shift()}return!0}function Un(e,t,n){Mn(e)&&n.delete(t)}function $n(){Cn=!1,null!==Pn&&Mn(Pn)&&(Pn=null),null!==Tn&&Mn(Tn)&&(Tn=null),null!==Ln&&Mn(Ln)&&(Ln=null),Rn.forEach(Un),Dn.forEach(Un)}function qn(e,t){e.blockedOn===t&&(e.blockedOn=null,Cn||(Cn=!0,re.unstable_scheduleCallback(re.unstable_NormalPriority,$n)))}function Bn(e){function t(t){return qn(t,e)}if(0<En.length){qn(En[0],e);for(var n=1;n<En.length;n++){var a=En[n];a.blockedOn===e&&(a.blockedOn=null)}}for(null!==Pn&&qn(Pn,e),null!==Tn&&qn(Tn,e),null!==Ln&&qn(Ln,e),Rn.forEach(t),Dn.forEach(t),n=0;n<In.length;n++)(a=In[n]).blockedOn===e&&(a.blockedOn=null);for(;0<In.length&&null===(n=In[0]).blockedOn;)zn(n),null===n.blockedOn&&In.shift()}var Vn=be.ReactCurrentBatchConfig,Qn=!0;function Wn(e,t,n,a){var r=bn,l=Vn.transition;Vn.transition=null;try{bn=1,Kn(e,t,n,a)}finally{bn=r,Vn.transition=l}}function Hn(e,t,n,a){var r=bn,l=Vn.transition;Vn.transition=null;try{bn=4,Kn(e,t,n,a)}finally{bn=r,Vn.transition=l}}function Kn(e,t,n,a){if(Qn){var r=Jn(e,t,n,a);if(null===r)Vr(e,t,a,Yn,n),Fn(e,a);else if(function(e,t,n,a,r){switch(t){case"focusin":return Pn=On(Pn,e,t,n,a,r),!0;case"dragenter":return Tn=On(Tn,e,t,n,a,r),!0;case"mouseover":return Ln=On(Ln,e,t,n,a,r),!0;case"pointerover":var l=r.pointerId;return Rn.set(l,On(Rn.get(l)||null,e,t,n,a,r)),!0;case"gotpointercapture":return l=r.pointerId,Dn.set(l,On(Dn.get(l)||null,e,t,n,a,r)),!0}return!1}(r,e,t,n,a))a.stopPropagation();else if(Fn(e,a),4&t&&-1<An.indexOf(e)){for(;null!==r;){var l=xl(r);if(null!==l&&wn(l),null===(l=Jn(e,t,n,a))&&Vr(e,t,a,Yn,n),l===r)break;r=l}null!==r&&a.stopPropagation()}else Vr(e,t,a,null,n)}}var Yn=null;function Jn(e,t,n,a){if(Yn=null,null!==(e=yl(e=jt(a))))if(null===(t=qt(e)))e=null;else if(13===(n=t.tag)){if(null!==(e=Bt(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Yn=e,null}function Gn(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Xt()){case Zt:return 1;case en:return 4;case tn:case nn:return 16;case an:return 536870912;default:return 16}default:return 16}}var Xn=null,Zn=null,ea=null;function ta(){if(ea)return ea;var e,t,n=Zn,a=n.length,r="value"in Xn?Xn.value:Xn.textContent,l=r.length;for(e=0;e<a&&n[e]===r[e];e++);var s=a-e;for(t=1;t<=s&&n[a-t]===r[l-t];t++);return ea=r.slice(e,1<t?1-t:void 0)}function na(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function aa(){return!0}function ra(){return!1}function la(e){function t(t,n,a,r,l){for(var s in this._reactName=t,this._targetInst=a,this.type=n,this.nativeEvent=r,this.target=l,this.currentTarget=null,e)e.hasOwnProperty(s)&&(t=e[s],this[s]=t?t(r):r[s]);return this.isDefaultPrevented=(null!=r.defaultPrevented?r.defaultPrevented:!1===r.returnValue)?aa:ra,this.isPropagationStopped=ra,this}return Oe(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!=typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=aa)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!=typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=aa)},persist:function(){},isPersistent:aa}),t}var sa,ia,oa,ca={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},ua=la(ca),da=Oe({},ca,{view:0,detail:0}),fa=la(da),ha=Oe({},da,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Na,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==oa&&(oa&&"mousemove"===e.type?(sa=e.screenX-oa.screenX,ia=e.screenY-oa.screenY):ia=sa=0,oa=e),sa)},movementY:function(e){return"movementY"in e?e.movementY:ia}}),ma=la(ha),pa=la(Oe({},ha,{dataTransfer:0})),va=la(Oe({},da,{relatedTarget:0})),ga=la(Oe({},ca,{animationName:0,elapsedTime:0,pseudoElement:0})),ya=Oe({},ca,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),xa=la(ya),ba=la(Oe({},ca,{data:0})),ja={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},wa={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},ka={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Sa(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=ka[e])&&!!t[e]}function Na(){return Sa}var _a=Oe({},da,{key:function(e){if(e.key){var t=ja[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=na(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?wa[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Na,charCode:function(e){return"keypress"===e.type?na(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?na(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),Ca=la(_a),Ea=la(Oe({},ha,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),Pa=la(Oe({},da,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Na})),Ta=la(Oe({},ca,{propertyName:0,elapsedTime:0,pseudoElement:0})),La=Oe({},ha,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Ra=la(La),Da=[9,13,27,32],Ia=ue&&"CompositionEvent"in window,Aa=null;ue&&"documentMode"in document&&(Aa=document.documentMode);var Fa=ue&&"TextEvent"in window&&!Aa,Oa=ue&&(!Ia||Aa&&8<Aa&&11>=Aa),za=String.fromCharCode(32),Ma=!1;function Ua(e,t){switch(e){case"keyup":return-1!==Da.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function $a(e){return"object"==typeof(e=e.detail)&&"data"in e?e.data:null}var qa=!1;var Ba={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Va(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Ba[e.type]:"textarea"===t}function Qa(e,t,n,a){_t(a),0<(t=Wr(t,"onChange")).length&&(n=new ua("onChange","change",null,n,a),e.push({event:n,listeners:t}))}var Wa=null,Ha=null;function Ka(e){zr(e,0)}function Ya(e){if(He(bl(e)))return e}function Ja(e,t){if("change"===e)return t}var Ga=!1;if(ue){var Xa;if(ue){var Za="oninput"in document;if(!Za){var er=document.createElement("div");er.setAttribute("oninput","return;"),Za="function"==typeof er.oninput}Xa=Za}else Xa=!1;Ga=Xa&&(!document.documentMode||9<document.documentMode)}function tr(){Wa&&(Wa.detachEvent("onpropertychange",nr),Ha=Wa=null)}function nr(e){if("value"===e.propertyName&&Ya(Ha)){var t=[];Qa(t,Ha,e,jt(e)),Lt(Ka,t)}}function ar(e,t,n){"focusin"===e?(tr(),Ha=n,(Wa=t).attachEvent("onpropertychange",nr)):"focusout"===e&&tr()}function rr(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Ya(Ha)}function lr(e,t){if("click"===e)return Ya(t)}function sr(e,t){if("input"===e||"change"===e)return Ya(t)}var ir="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t};function or(e,t){if(ir(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;var n=Object.keys(e),a=Object.keys(t);if(n.length!==a.length)return!1;for(a=0;a<n.length;a++){var r=n[a];if(!de.call(t,r)||!ir(e[r],t[r]))return!1}return!0}function cr(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function ur(e,t){var n,a=cr(e);for(e=0;a;){if(3===a.nodeType){if(n=e+a.textContent.length,e<=t&&n>=t)return{node:a,offset:t-e};e=n}e:{for(;a;){if(a.nextSibling){a=a.nextSibling;break e}a=a.parentNode}a=void 0}a=cr(a)}}function dr(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?dr(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function fr(){for(var e=window,t=Ke();t instanceof e.HTMLIFrameElement;){try{var n="string"==typeof t.contentWindow.location.href}catch(a){n=!1}if(!n)break;t=Ke((e=t.contentWindow).document)}return t}function hr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function mr(e){var t=fr(),n=e.focusedElem,a=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&dr(n.ownerDocument.documentElement,n)){if(null!==a&&hr(n))if(t=a.start,void 0===(e=a.end)&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if((e=(t=n.ownerDocument||document)&&t.defaultView||window).getSelection){e=e.getSelection();var r=n.textContent.length,l=Math.min(a.start,r);a=void 0===a.end?l:Math.min(a.end,r),!e.extend&&l>a&&(r=a,a=l,l=r),r=ur(n,l);var s=ur(n,a);r&&s&&(1!==e.rangeCount||e.anchorNode!==r.node||e.anchorOffset!==r.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&((t=t.createRange()).setStart(r.node,r.offset),e.removeAllRanges(),l>a?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}for(t=[],e=n;e=e.parentNode;)1===e.nodeType&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for("function"==typeof n.focus&&n.focus(),n=0;n<t.length;n++)(e=t[n]).element.scrollLeft=e.left,e.element.scrollTop=e.top}}var pr=ue&&"documentMode"in document&&11>=document.documentMode,vr=null,gr=null,yr=null,xr=!1;function br(e,t,n){var a=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;xr||null==vr||vr!==Ke(a)||("selectionStart"in(a=vr)&&hr(a)?a={start:a.selectionStart,end:a.selectionEnd}:a={anchorNode:(a=(a.ownerDocument&&a.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:a.anchorOffset,focusNode:a.focusNode,focusOffset:a.focusOffset},yr&&or(yr,a)||(yr=a,0<(a=Wr(gr,"onSelect")).length&&(t=new ua("onSelect","select",null,t,n),e.push({event:t,listeners:a}),t.target=vr)))}function jr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var wr={animationend:jr("Animation","AnimationEnd"),animationiteration:jr("Animation","AnimationIteration"),animationstart:jr("Animation","AnimationStart"),transitionend:jr("Transition","TransitionEnd")},kr={},Sr={};function Nr(e){if(kr[e])return kr[e];if(!wr[e])return e;var t,n=wr[e];for(t in n)if(n.hasOwnProperty(t)&&t in Sr)return kr[e]=n[t];return e}ue&&(Sr=document.createElement("div").style,"AnimationEvent"in window||(delete wr.animationend.animation,delete wr.animationiteration.animation,delete wr.animationstart.animation),"TransitionEvent"in window||delete wr.transitionend.transition);var _r=Nr("animationend"),Cr=Nr("animationiteration"),Er=Nr("animationstart"),Pr=Nr("transitionend"),Tr=new Map,Lr="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Rr(e,t){Tr.set(e,t),oe(t,[e])}for(var Dr=0;Dr<Lr.length;Dr++){var Ir=Lr[Dr];Rr(Ir.toLowerCase(),"on"+(Ir[0].toUpperCase()+Ir.slice(1)))}Rr(_r,"onAnimationEnd"),Rr(Cr,"onAnimationIteration"),Rr(Er,"onAnimationStart"),Rr("dblclick","onDoubleClick"),Rr("focusin","onFocus"),Rr("focusout","onBlur"),Rr(Pr,"onTransitionEnd"),ce("onMouseEnter",["mouseout","mouseover"]),ce("onMouseLeave",["mouseout","mouseover"]),ce("onPointerEnter",["pointerout","pointerover"]),ce("onPointerLeave",["pointerout","pointerover"]),oe("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),oe("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),oe("onBeforeInput",["compositionend","keypress","textInput","paste"]),oe("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),oe("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),oe("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Ar="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Fr=new Set("cancel close invalid load scroll toggle".split(" ").concat(Ar));function Or(e,t,n){var a=e.type||"unknown-event";e.currentTarget=n,function(e,t,n,a,r,l,s,i,o){if($t.apply(this,arguments),Ft){if(!Ft)throw Error(le(198));var c=Ot;Ft=!1,Ot=null,zt||(zt=!0,Mt=c)}}(a,t,void 0,e),e.currentTarget=null}function zr(e,t){t=!!(4&t);for(var n=0;n<e.length;n++){var a=e[n],r=a.event;a=a.listeners;e:{var l=void 0;if(t)for(var s=a.length-1;0<=s;s--){var i=a[s],o=i.instance,c=i.currentTarget;if(i=i.listener,o!==l&&r.isPropagationStopped())break e;Or(r,i,c),l=o}else for(s=0;s<a.length;s++){if(o=(i=a[s]).instance,c=i.currentTarget,i=i.listener,o!==l&&r.isPropagationStopped())break e;Or(r,i,c),l=o}}}if(zt)throw e=Mt,zt=!1,Mt=null,e}function Mr(e,t){var n=t[pl];void 0===n&&(n=t[pl]=new Set);var a=e+"__bubble";n.has(a)||(Br(t,e,2,!1),n.add(a))}function Ur(e,t,n){var a=0;t&&(a|=4),Br(n,e,a,t)}var $r="_reactListening"+Math.random().toString(36).slice(2);function qr(e){if(!e[$r]){e[$r]=!0,se.forEach(function(t){"selectionchange"!==t&&(Fr.has(t)||Ur(t,!1,e),Ur(t,!0,e))});var t=9===e.nodeType?e:e.ownerDocument;null===t||t[$r]||(t[$r]=!0,Ur("selectionchange",!1,t))}}function Br(e,t,n,a){switch(Gn(t)){case 1:var r=Wn;break;case 4:r=Hn;break;default:r=Kn}n=r.bind(null,t,n,e),r=void 0,!Dt||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(r=!0),a?void 0!==r?e.addEventListener(t,n,{capture:!0,passive:r}):e.addEventListener(t,n,!0):void 0!==r?e.addEventListener(t,n,{passive:r}):e.addEventListener(t,n,!1)}function Vr(e,t,n,a,r){var l=a;if(!(1&t||2&t||null===a))e:for(;;){if(null===a)return;var s=a.tag;if(3===s||4===s){var i=a.stateNode.containerInfo;if(i===r||8===i.nodeType&&i.parentNode===r)break;if(4===s)for(s=a.return;null!==s;){var o=s.tag;if((3===o||4===o)&&((o=s.stateNode.containerInfo)===r||8===o.nodeType&&o.parentNode===r))return;s=s.return}for(;null!==i;){if(null===(s=yl(i)))return;if(5===(o=s.tag)||6===o){a=l=s;continue e}i=i.parentNode}}a=a.return}Lt(function(){var a=l,r=jt(n),s=[];e:{var i=Tr.get(e);if(void 0!==i){var o=ua,c=e;switch(e){case"keypress":if(0===na(n))break e;case"keydown":case"keyup":o=Ca;break;case"focusin":c="focus",o=va;break;case"focusout":c="blur",o=va;break;case"beforeblur":case"afterblur":o=va;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":o=ma;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":o=pa;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":o=Pa;break;case _r:case Cr:case Er:o=ga;break;case Pr:o=Ta;break;case"scroll":o=fa;break;case"wheel":o=Ra;break;case"copy":case"cut":case"paste":o=xa;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":o=Ea}var u=!!(4&t),d=!u&&"scroll"===e,f=u?null!==i?i+"Capture":null:i;u=[];for(var h,m=a;null!==m;){var p=(h=m).stateNode;if(5===h.tag&&null!==p&&(h=p,null!==f&&(null!=(p=Rt(m,f))&&u.push(Qr(m,p,h)))),d)break;m=m.return}0<u.length&&(i=new o(i,c,null,n,r),s.push({event:i,listeners:u}))}}if(!(7&t)){if(o="mouseout"===e||"pointerout"===e,(!(i="mouseover"===e||"pointerover"===e)||n===bt||!(c=n.relatedTarget||n.fromElement)||!yl(c)&&!c[ml])&&(o||i)&&(i=r.window===r?r:(i=r.ownerDocument)?i.defaultView||i.parentWindow:window,o?(o=a,null!==(c=(c=n.relatedTarget||n.toElement)?yl(c):null)&&(c!==(d=qt(c))||5!==c.tag&&6!==c.tag)&&(c=null)):(o=null,c=a),o!==c)){if(u=ma,p="onMouseLeave",f="onMouseEnter",m="mouse","pointerout"!==e&&"pointerover"!==e||(u=Ea,p="onPointerLeave",f="onPointerEnter",m="pointer"),d=null==o?i:bl(o),h=null==c?i:bl(c),(i=new u(p,m+"leave",o,n,r)).target=d,i.relatedTarget=h,p=null,yl(r)===a&&((u=new u(f,m+"enter",c,n,r)).target=h,u.relatedTarget=d,p=u),d=p,o&&c)e:{for(f=c,m=0,h=u=o;h;h=Hr(h))m++;for(h=0,p=f;p;p=Hr(p))h++;for(;0<m-h;)u=Hr(u),m--;for(;0<h-m;)f=Hr(f),h--;for(;m--;){if(u===f||null!==f&&u===f.alternate)break e;u=Hr(u),f=Hr(f)}u=null}else u=null;null!==o&&Kr(s,i,o,u,!1),null!==c&&null!==d&&Kr(s,d,c,u,!0)}if("select"===(o=(i=a?bl(a):window).nodeName&&i.nodeName.toLowerCase())||"input"===o&&"file"===i.type)var v=Ja;else if(Va(i))if(Ga)v=sr;else{v=rr;var g=ar}else(o=i.nodeName)&&"input"===o.toLowerCase()&&("checkbox"===i.type||"radio"===i.type)&&(v=lr);switch(v&&(v=v(e,a))?Qa(s,v,n,r):(g&&g(e,i,a),"focusout"===e&&(g=i._wrapperState)&&g.controlled&&"number"===i.type&&et(i,"number",i.value)),g=a?bl(a):window,e){case"focusin":(Va(g)||"true"===g.contentEditable)&&(vr=g,gr=a,yr=null);break;case"focusout":yr=gr=vr=null;break;case"mousedown":xr=!0;break;case"contextmenu":case"mouseup":case"dragend":xr=!1,br(s,n,r);break;case"selectionchange":if(pr)break;case"keydown":case"keyup":br(s,n,r)}var y;if(Ia)e:{switch(e){case"compositionstart":var x="onCompositionStart";break e;case"compositionend":x="onCompositionEnd";break e;case"compositionupdate":x="onCompositionUpdate";break e}x=void 0}else qa?Ua(e,n)&&(x="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(x="onCompositionStart");x&&(Oa&&"ko"!==n.locale&&(qa||"onCompositionStart"!==x?"onCompositionEnd"===x&&qa&&(y=ta()):(Zn="value"in(Xn=r)?Xn.value:Xn.textContent,qa=!0)),0<(g=Wr(a,x)).length&&(x=new ba(x,e,null,n,r),s.push({event:x,listeners:g}),y?x.data=y:null!==(y=$a(n))&&(x.data=y))),(y=Fa?function(e,t){switch(e){case"compositionend":return $a(t);case"keypress":return 32!==t.which?null:(Ma=!0,za);case"textInput":return(e=t.data)===za&&Ma?null:e;default:return null}}(e,n):function(e,t){if(qa)return"compositionend"===e||!Ia&&Ua(e,t)?(e=ta(),ea=Zn=Xn=null,qa=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Oa&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(a=Wr(a,"onBeforeInput")).length&&(r=new ba("onBeforeInput","beforeinput",null,n,r),s.push({event:r,listeners:a}),r.data=y))}zr(s,t)})}function Qr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Wr(e,t){for(var n=t+"Capture",a=[];null!==e;){var r=e,l=r.stateNode;5===r.tag&&null!==l&&(r=l,null!=(l=Rt(e,n))&&a.unshift(Qr(e,l,r)),null!=(l=Rt(e,t))&&a.push(Qr(e,l,r))),e=e.return}return a}function Hr(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag);return e||null}function Kr(e,t,n,a,r){for(var l=t._reactName,s=[];null!==n&&n!==a;){var i=n,o=i.alternate,c=i.stateNode;if(null!==o&&o===a)break;5===i.tag&&null!==c&&(i=c,r?null!=(o=Rt(n,l))&&s.unshift(Qr(n,o,i)):r||null!=(o=Rt(n,l))&&s.push(Qr(n,o,i))),n=n.return}0!==s.length&&e.push({event:t,listeners:s})}var Yr=/\r\n?/g,Jr=/\u0000|\uFFFD/g;function Gr(e){return("string"==typeof e?e:""+e).replace(Yr,"\n").replace(Jr,"")}function Xr(e,t,n){if(t=Gr(t),Gr(e)!==t&&n)throw Error(le(425))}function Zr(){}var el=null,tl=null;function nl(e,t){return"textarea"===e||"noscript"===e||"string"==typeof t.children||"number"==typeof t.children||"object"==typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var al="function"==typeof setTimeout?setTimeout:void 0,rl="function"==typeof clearTimeout?clearTimeout:void 0,ll="function"==typeof Promise?Promise:void 0,sl="function"==typeof queueMicrotask?queueMicrotask:void 0!==ll?function(e){return ll.resolve(null).then(e).catch(il)}:al;function il(e){setTimeout(function(){throw e})}function ol(e,t){var n=t,a=0;do{var r=n.nextSibling;if(e.removeChild(n),r&&8===r.nodeType)if("/$"===(n=r.data)){if(0===a)return e.removeChild(r),void Bn(t);a--}else"$"!==n&&"$?"!==n&&"$!"!==n||a++;n=r}while(n);Bn(t)}function cl(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t)break;if("/$"===t)return null}}return e}function ul(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}var dl=Math.random().toString(36).slice(2),fl="__reactFiber$"+dl,hl="__reactProps$"+dl,ml="__reactContainer$"+dl,pl="__reactEvents$"+dl,vl="__reactListeners$"+dl,gl="__reactHandles$"+dl;function yl(e){var t=e[fl];if(t)return t;for(var n=e.parentNode;n;){if(t=n[ml]||n[fl]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=ul(e);null!==e;){if(n=e[fl])return n;e=ul(e)}return t}n=(e=n).parentNode}return null}function xl(e){return!(e=e[fl]||e[ml])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function bl(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(le(33))}function jl(e){return e[hl]||null}var wl=[],kl=-1;function Sl(e){return{current:e}}function Nl(e){0>kl||(e.current=wl[kl],wl[kl]=null,kl--)}function _l(e,t){kl++,wl[kl]=e.current,e.current=t}var Cl={},El=Sl(Cl),Pl=Sl(!1),Tl=Cl;function Ll(e,t){var n=e.type.contextTypes;if(!n)return Cl;var a=e.stateNode;if(a&&a.__reactInternalMemoizedUnmaskedChildContext===t)return a.__reactInternalMemoizedMaskedChildContext;var r,l={};for(r in n)l[r]=t[r];return a&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=l),l}function Rl(e){return null!=(e=e.childContextTypes)}function Dl(){Nl(Pl),Nl(El)}function Il(e,t,n){if(El.current!==Cl)throw Error(le(168));_l(El,t),_l(Pl,n)}function Al(e,t,n){var a=e.stateNode;if(t=t.childContextTypes,"function"!=typeof a.getChildContext)return n;for(var r in a=a.getChildContext())if(!(r in t))throw Error(le(108,Be(e)||"Unknown",r));return Oe({},n,a)}function Fl(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Cl,Tl=El.current,_l(El,e),_l(Pl,Pl.current),!0}function Ol(e,t,n){var a=e.stateNode;if(!a)throw Error(le(169));n?(e=Al(e,t,Tl),a.__reactInternalMemoizedMergedChildContext=e,Nl(Pl),Nl(El),_l(El,e)):Nl(Pl),_l(Pl,n)}var zl=null,Ml=!1,Ul=!1;function $l(e){null===zl?zl=[e]:zl.push(e)}function ql(){if(!Ul&&null!==zl){Ul=!0;var e=0,t=bn;try{var n=zl;for(bn=1;e<n.length;e++){var a=n[e];do{a=a(!0)}while(null!==a)}zl=null,Ml=!1}catch(r){throw null!==zl&&(zl=zl.slice(e+1)),Ht(Zt,ql),r}finally{bn=t,Ul=!1}}return null}var Bl=[],Vl=0,Ql=null,Wl=0,Hl=[],Kl=0,Yl=null,Jl=1,Gl="";function Xl(e,t){Bl[Vl++]=Wl,Bl[Vl++]=Ql,Ql=e,Wl=t}function Zl(e,t,n){Hl[Kl++]=Jl,Hl[Kl++]=Gl,Hl[Kl++]=Yl,Yl=e;var a=Jl;e=Gl;var r=32-sn(a)-1;a&=~(1<<r),n+=1;var l=32-sn(t)+r;if(30<l){var s=r-r%5;l=(a&(1<<s)-1).toString(32),a>>=s,r-=s,Jl=1<<32-sn(t)+r|n<<r|a,Gl=l+e}else Jl=1<<l|n<<r|a,Gl=e}function es(e){null!==e.return&&(Xl(e,1),Zl(e,1,0))}function ts(e){for(;e===Ql;)Ql=Bl[--Vl],Bl[Vl]=null,Wl=Bl[--Vl],Bl[Vl]=null;for(;e===Yl;)Yl=Hl[--Kl],Hl[Kl]=null,Gl=Hl[--Kl],Hl[Kl]=null,Jl=Hl[--Kl],Hl[Kl]=null}var ns=null,as=null,rs=!1,ls=null;function ss(e,t){var n=Lu(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,null===(t=e.deletions)?(e.deletions=[n],e.flags|=16):t.push(n)}function is(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,ns=e,as=cl(t.firstChild),!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,ns=e,as=null,!0);case 13:return null!==(t=8!==t.nodeType?null:t)&&(n=null!==Yl?{id:Jl,overflow:Gl}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},(n=Lu(18,null,null,0)).stateNode=t,n.return=e,e.child=n,ns=e,as=null,!0);default:return!1}}function os(e){return!(!(1&e.mode)||128&e.flags)}function cs(e){if(rs){var t=as;if(t){var n=t;if(!is(e,t)){if(os(e))throw Error(le(418));t=cl(n.nextSibling);var a=ns;t&&is(e,t)?ss(a,n):(e.flags=-4097&e.flags|2,rs=!1,ns=e)}}else{if(os(e))throw Error(le(418));e.flags=-4097&e.flags|2,rs=!1,ns=e}}}function us(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;ns=e}function ds(e){if(e!==ns)return!1;if(!rs)return us(e),rs=!0,!1;var t;if((t=3!==e.tag)&&!(t=5!==e.tag)&&(t="head"!==(t=e.type)&&"body"!==t&&!nl(e.type,e.memoizedProps)),t&&(t=as)){if(os(e))throw fs(),Error(le(418));for(;t;)ss(e,t),t=cl(t.nextSibling)}if(us(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(le(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var n=e.data;if("/$"===n){if(0===t){as=cl(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++}e=e.nextSibling}as=null}}else as=ns?cl(e.stateNode.nextSibling):null;return!0}function fs(){for(var e=as;e;)e=cl(e.nextSibling)}function hs(){as=ns=null,rs=!1}function ms(e){null===ls?ls=[e]:ls.push(e)}var ps=be.ReactCurrentBatchConfig;function vs(e,t,n){if(null!==(e=n.ref)&&"function"!=typeof e&&"object"!=typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(le(309));var a=n.stateNode}if(!a)throw Error(le(147,e));var r=a,l=""+e;return null!==t&&null!==t.ref&&"function"==typeof t.ref&&t.ref._stringRef===l?t.ref:((t=function(e){var t=r.refs;null===e?delete t[l]:t[l]=e})._stringRef=l,t)}if("string"!=typeof e)throw Error(le(284));if(!n._owner)throw Error(le(290,e))}return e}function gs(e,t){throw e=Object.prototype.toString.call(t),Error(le(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function ys(e){return(0,e._init)(e._payload)}function xs(e){function t(t,n){if(e){var a=t.deletions;null===a?(t.deletions=[n],t.flags|=16):a.push(n)}}function n(n,a){if(!e)return null;for(;null!==a;)t(n,a),a=a.sibling;return null}function a(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function r(e,t){return(e=Du(e,t)).index=0,e.sibling=null,e}function l(t,n,a){return t.index=a,e?null!==(a=t.alternate)?(a=a.index)<n?(t.flags|=2,n):a:(t.flags|=2,n):(t.flags|=1048576,n)}function s(t){return e&&null===t.alternate&&(t.flags|=2),t}function i(e,t,n,a){return null===t||6!==t.tag?((t=Ou(n,e.mode,a)).return=e,t):((t=r(t,n)).return=e,t)}function o(e,t,n,a){var l=n.type;return l===ke?u(e,t,n.props.children,a,n.key):null!==t&&(t.elementType===l||"object"==typeof l&&null!==l&&l.$$typeof===Re&&ys(l)===t.type)?((a=r(t,n.props)).ref=vs(e,t,n),a.return=e,a):((a=Iu(n.type,n.key,n.props,null,e.mode,a)).ref=vs(e,t,n),a.return=e,a)}function c(e,t,n,a){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=zu(n,e.mode,a)).return=e,t):((t=r(t,n.children||[])).return=e,t)}function u(e,t,n,a,l){return null===t||7!==t.tag?((t=Au(n,e.mode,a,l)).return=e,t):((t=r(t,n)).return=e,t)}function d(e,t,n){if("string"==typeof t&&""!==t||"number"==typeof t)return(t=Ou(""+t,e.mode,n)).return=e,t;if("object"==typeof t&&null!==t){switch(t.$$typeof){case je:return(n=Iu(t.type,t.key,t.props,null,e.mode,n)).ref=vs(e,null,t),n.return=e,n;case we:return(t=zu(t,e.mode,n)).return=e,t;case Re:return d(e,(0,t._init)(t._payload),n)}if(tt(t)||Ae(t))return(t=Au(t,e.mode,n,null)).return=e,t;gs(e,t)}return null}function f(e,t,n,a){var r=null!==t?t.key:null;if("string"==typeof n&&""!==n||"number"==typeof n)return null!==r?null:i(e,t,""+n,a);if("object"==typeof n&&null!==n){switch(n.$$typeof){case je:return n.key===r?o(e,t,n,a):null;case we:return n.key===r?c(e,t,n,a):null;case Re:return f(e,t,(r=n._init)(n._payload),a)}if(tt(n)||Ae(n))return null!==r?null:u(e,t,n,a,null);gs(e,n)}return null}function h(e,t,n,a,r){if("string"==typeof a&&""!==a||"number"==typeof a)return i(t,e=e.get(n)||null,""+a,r);if("object"==typeof a&&null!==a){switch(a.$$typeof){case je:return o(t,e=e.get(null===a.key?n:a.key)||null,a,r);case we:return c(t,e=e.get(null===a.key?n:a.key)||null,a,r);case Re:return h(e,t,n,(0,a._init)(a._payload),r)}if(tt(a)||Ae(a))return u(t,e=e.get(n)||null,a,r,null);gs(t,a)}return null}return function i(o,c,u,m){if("object"==typeof u&&null!==u&&u.type===ke&&null===u.key&&(u=u.props.children),"object"==typeof u&&null!==u){switch(u.$$typeof){case je:e:{for(var p=u.key,v=c;null!==v;){if(v.key===p){if((p=u.type)===ke){if(7===v.tag){n(o,v.sibling),(c=r(v,u.props.children)).return=o,o=c;break e}}else if(v.elementType===p||"object"==typeof p&&null!==p&&p.$$typeof===Re&&ys(p)===v.type){n(o,v.sibling),(c=r(v,u.props)).ref=vs(o,v,u),c.return=o,o=c;break e}n(o,v);break}t(o,v),v=v.sibling}u.type===ke?((c=Au(u.props.children,o.mode,m,u.key)).return=o,o=c):((m=Iu(u.type,u.key,u.props,null,o.mode,m)).ref=vs(o,c,u),m.return=o,o=m)}return s(o);case we:e:{for(v=u.key;null!==c;){if(c.key===v){if(4===c.tag&&c.stateNode.containerInfo===u.containerInfo&&c.stateNode.implementation===u.implementation){n(o,c.sibling),(c=r(c,u.children||[])).return=o,o=c;break e}n(o,c);break}t(o,c),c=c.sibling}(c=zu(u,o.mode,m)).return=o,o=c}return s(o);case Re:return i(o,c,(v=u._init)(u._payload),m)}if(tt(u))return function(r,s,i,o){for(var c=null,u=null,m=s,p=s=0,v=null;null!==m&&p<i.length;p++){m.index>p?(v=m,m=null):v=m.sibling;var g=f(r,m,i[p],o);if(null===g){null===m&&(m=v);break}e&&m&&null===g.alternate&&t(r,m),s=l(g,s,p),null===u?c=g:u.sibling=g,u=g,m=v}if(p===i.length)return n(r,m),rs&&Xl(r,p),c;if(null===m){for(;p<i.length;p++)null!==(m=d(r,i[p],o))&&(s=l(m,s,p),null===u?c=m:u.sibling=m,u=m);return rs&&Xl(r,p),c}for(m=a(r,m);p<i.length;p++)null!==(v=h(m,r,p,i[p],o))&&(e&&null!==v.alternate&&m.delete(null===v.key?p:v.key),s=l(v,s,p),null===u?c=v:u.sibling=v,u=v);return e&&m.forEach(function(e){return t(r,e)}),rs&&Xl(r,p),c}(o,c,u,m);if(Ae(u))return function(r,s,i,o){var c=Ae(i);if("function"!=typeof c)throw Error(le(150));if(null==(i=c.call(i)))throw Error(le(151));for(var u=c=null,m=s,p=s=0,v=null,g=i.next();null!==m&&!g.done;p++,g=i.next()){m.index>p?(v=m,m=null):v=m.sibling;var y=f(r,m,g.value,o);if(null===y){null===m&&(m=v);break}e&&m&&null===y.alternate&&t(r,m),s=l(y,s,p),null===u?c=y:u.sibling=y,u=y,m=v}if(g.done)return n(r,m),rs&&Xl(r,p),c;if(null===m){for(;!g.done;p++,g=i.next())null!==(g=d(r,g.value,o))&&(s=l(g,s,p),null===u?c=g:u.sibling=g,u=g);return rs&&Xl(r,p),c}for(m=a(r,m);!g.done;p++,g=i.next())null!==(g=h(m,r,p,g.value,o))&&(e&&null!==g.alternate&&m.delete(null===g.key?p:g.key),s=l(g,s,p),null===u?c=g:u.sibling=g,u=g);return e&&m.forEach(function(e){return t(r,e)}),rs&&Xl(r,p),c}(o,c,u,m);gs(o,u)}return"string"==typeof u&&""!==u||"number"==typeof u?(u=""+u,null!==c&&6===c.tag?(n(o,c.sibling),(c=r(c,u)).return=o,o=c):(n(o,c),(c=Ou(u,o.mode,m)).return=o,o=c),s(o)):n(o,c)}}var bs=xs(!0),js=xs(!1),ws=Sl(null),ks=null,Ss=null,Ns=null;function _s(){Ns=Ss=ks=null}function Cs(e){var t=ws.current;Nl(ws),e._currentValue=t}function Es(e,t,n){for(;null!==e;){var a=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==a&&(a.childLanes|=t)):null!==a&&(a.childLanes&t)!==t&&(a.childLanes|=t),e===n)break;e=e.return}}function Ps(e,t){ks=e,Ns=Ss=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(0!==(e.lanes&t)&&(xo=!0),e.firstContext=null)}function Ts(e){var t=e._currentValue;if(Ns!==e)if(e={context:e,memoizedValue:t,next:null},null===Ss){if(null===ks)throw Error(le(308));Ss=e,ks.dependencies={lanes:0,firstContext:e}}else Ss=Ss.next=e;return t}var Ls=null;function Rs(e){null===Ls?Ls=[e]:Ls.push(e)}function Ds(e,t,n,a){var r=t.interleaved;return null===r?(n.next=n,Rs(t)):(n.next=r.next,r.next=n),t.interleaved=n,Is(e,a)}function Is(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}var As=!1;function Fs(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Os(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function zs(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Ms(e,t,n){var a=e.updateQueue;if(null===a)return null;if(a=a.shared,2&Ec){var r=a.pending;return null===r?t.next=t:(t.next=r.next,r.next=t),a.pending=t,Is(e,n)}return null===(r=a.interleaved)?(t.next=t,Rs(a)):(t.next=r.next,r.next=t),a.interleaved=t,Is(e,n)}function Us(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,4194240&n)){var a=t.lanes;n|=a&=e.pendingLanes,t.lanes=n,xn(e,n)}}function $s(e,t){var n=e.updateQueue,a=e.alternate;if(null!==a&&n===(a=a.updateQueue)){var r=null,l=null;if(null!==(n=n.firstBaseUpdate)){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===l?r=l=s:l=l.next=s,n=n.next}while(null!==n);null===l?r=l=t:l=l.next=t}else r=l=t;return n={baseState:a.baseState,firstBaseUpdate:r,lastBaseUpdate:l,shared:a.shared,effects:a.effects},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function qs(e,t,n,a){var r=e.updateQueue;As=!1;var l=r.firstBaseUpdate,s=r.lastBaseUpdate,i=r.shared.pending;if(null!==i){r.shared.pending=null;var o=i,c=o.next;o.next=null,null===s?l=c:s.next=c,s=o;var u=e.alternate;null!==u&&((i=(u=u.updateQueue).lastBaseUpdate)!==s&&(null===i?u.firstBaseUpdate=c:i.next=c,u.lastBaseUpdate=o))}if(null!==l){var d=r.baseState;for(s=0,u=c=o=null,i=l;;){var f=i.lane,h=i.eventTime;if((a&f)===f){null!==u&&(u=u.next={eventTime:h,lane:0,tag:i.tag,payload:i.payload,callback:i.callback,next:null});e:{var m=e,p=i;switch(f=t,h=n,p.tag){case 1:if("function"==typeof(m=p.payload)){d=m.call(h,d,f);break e}d=m;break e;case 3:m.flags=-65537&m.flags|128;case 0:if(null==(f="function"==typeof(m=p.payload)?m.call(h,d,f):m))break e;d=Oe({},d,f);break e;case 2:As=!0}}null!==i.callback&&0!==i.lane&&(e.flags|=64,null===(f=r.effects)?r.effects=[i]:f.push(i))}else h={eventTime:h,lane:f,tag:i.tag,payload:i.payload,callback:i.callback,next:null},null===u?(c=u=h,o=d):u=u.next=h,s|=f;if(null===(i=i.next)){if(null===(i=r.shared.pending))break;i=(f=i).next,f.next=null,r.lastBaseUpdate=f,r.shared.pending=null}}if(null===u&&(o=d),r.baseState=o,r.firstBaseUpdate=c,r.lastBaseUpdate=u,null!==(t=r.shared.interleaved)){r=t;do{s|=r.lane,r=r.next}while(r!==t)}else null===l&&(r.shared.lanes=0);Fc|=s,e.lanes=s,e.memoizedState=d}}function Bs(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var a=e[t],r=a.callback;if(null!==r){if(a.callback=null,a=n,"function"!=typeof r)throw Error(le(191,r));r.call(a)}}}var Vs={},Qs=Sl(Vs),Ws=Sl(Vs),Hs=Sl(Vs);function Ks(e){if(e===Vs)throw Error(le(174));return e}function Ys(e,t){switch(_l(Hs,t),_l(Ws,e),_l(Qs,Vs),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:ot(null,"");break;default:t=ot(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}Nl(Qs),_l(Qs,t)}function Js(){Nl(Qs),Nl(Ws),Nl(Hs)}function Gs(e){Ks(Hs.current);var t=Ks(Qs.current),n=ot(t,e.type);t!==n&&(_l(Ws,e),_l(Qs,n))}function Xs(e){Ws.current===e&&(Nl(Qs),Nl(Ws))}var Zs=Sl(0);function ei(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(128&t.flags)return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ti=[];function ni(){for(var e=0;e<ti.length;e++)ti[e]._workInProgressVersionPrimary=null;ti.length=0}var ai=be.ReactCurrentDispatcher,ri=be.ReactCurrentBatchConfig,li=0,si=null,ii=null,oi=null,ci=!1,ui=!1,di=0,fi=0;function hi(){throw Error(le(321))}function mi(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!ir(e[n],t[n]))return!1;return!0}function pi(e,t,n,a,r,l){if(li=l,si=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,ai.current=null===e||null===e.memoizedState?Xi:Zi,e=n(a,r),ui){l=0;do{if(ui=!1,di=0,25<=l)throw Error(le(301));l+=1,oi=ii=null,t.updateQueue=null,ai.current=eo,e=n(a,r)}while(ui)}if(ai.current=Gi,t=null!==ii&&null!==ii.next,li=0,oi=ii=si=null,ci=!1,t)throw Error(le(300));return e}function vi(){var e=0!==di;return di=0,e}function gi(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===oi?si.memoizedState=oi=e:oi=oi.next=e,oi}function yi(){if(null===ii){var e=si.alternate;e=null!==e?e.memoizedState:null}else e=ii.next;var t=null===oi?si.memoizedState:oi.next;if(null!==t)oi=t,ii=e;else{if(null===e)throw Error(le(310));e={memoizedState:(ii=e).memoizedState,baseState:ii.baseState,baseQueue:ii.baseQueue,queue:ii.queue,next:null},null===oi?si.memoizedState=oi=e:oi=oi.next=e}return oi}function xi(e,t){return"function"==typeof t?t(e):t}function bi(e){var t=yi(),n=t.queue;if(null===n)throw Error(le(311));n.lastRenderedReducer=e;var a=ii,r=a.baseQueue,l=n.pending;if(null!==l){if(null!==r){var s=r.next;r.next=l.next,l.next=s}a.baseQueue=r=l,n.pending=null}if(null!==r){l=r.next,a=a.baseState;var i=s=null,o=null,c=l;do{var u=c.lane;if((li&u)===u)null!==o&&(o=o.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),a=c.hasEagerState?c.eagerState:e(a,c.action);else{var d={lane:u,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};null===o?(i=o=d,s=a):o=o.next=d,si.lanes|=u,Fc|=u}c=c.next}while(null!==c&&c!==l);null===o?s=a:o.next=i,ir(a,t.memoizedState)||(xo=!0),t.memoizedState=a,t.baseState=s,t.baseQueue=o,n.lastRenderedState=a}if(null!==(e=n.interleaved)){r=e;do{l=r.lane,si.lanes|=l,Fc|=l,r=r.next}while(r!==e)}else null===r&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function ji(e){var t=yi(),n=t.queue;if(null===n)throw Error(le(311));n.lastRenderedReducer=e;var a=n.dispatch,r=n.pending,l=t.memoizedState;if(null!==r){n.pending=null;var s=r=r.next;do{l=e(l,s.action),s=s.next}while(s!==r);ir(l,t.memoizedState)||(xo=!0),t.memoizedState=l,null===t.baseQueue&&(t.baseState=l),n.lastRenderedState=l}return[l,a]}function wi(){}function ki(e,t){var n=si,a=yi(),r=t(),l=!ir(a.memoizedState,r);if(l&&(a.memoizedState=r,xo=!0),a=a.queue,Ai(_i.bind(null,n,a,e),[e]),a.getSnapshot!==t||l||null!==oi&&1&oi.memoizedState.tag){if(n.flags|=2048,Ti(9,Ni.bind(null,n,a,r,t),void 0,null),null===Pc)throw Error(le(349));30&li||Si(n,t,r)}return r}function Si(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=si.updateQueue)?(t={lastEffect:null,stores:null},si.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Ni(e,t,n,a){t.value=n,t.getSnapshot=a,Ci(t)&&Ei(e)}function _i(e,t,n){return n(function(){Ci(t)&&Ei(e)})}function Ci(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!ir(e,n)}catch(a){return!0}}function Ei(e){var t=Is(e,1);null!==t&&nu(t,e,1,-1)}function Pi(e){var t=gi();return"function"==typeof e&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:xi,lastRenderedState:e},t.queue=e,e=e.dispatch=Hi.bind(null,si,e),[t.memoizedState,e]}function Ti(e,t,n,a){return e={tag:e,create:t,destroy:n,deps:a,next:null},null===(t=si.updateQueue)?(t={lastEffect:null,stores:null},si.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(a=n.next,n.next=e,e.next=a,t.lastEffect=e),e}function Li(){return yi().memoizedState}function Ri(e,t,n,a){var r=gi();si.flags|=e,r.memoizedState=Ti(1|t,n,void 0,void 0===a?null:a)}function Di(e,t,n,a){var r=yi();a=void 0===a?null:a;var l=void 0;if(null!==ii){var s=ii.memoizedState;if(l=s.destroy,null!==a&&mi(a,s.deps))return void(r.memoizedState=Ti(t,n,l,a))}si.flags|=e,r.memoizedState=Ti(1|t,n,l,a)}function Ii(e,t){return Ri(8390656,8,e,t)}function Ai(e,t){return Di(2048,8,e,t)}function Fi(e,t){return Di(4,2,e,t)}function Oi(e,t){return Di(4,4,e,t)}function zi(e,t){return"function"==typeof t?(e=e(),t(e),function(){t(null)}):null!=t?(e=e(),t.current=e,function(){t.current=null}):void 0}function Mi(e,t,n){return n=null!=n?n.concat([e]):null,Di(4,4,zi.bind(null,t,e),n)}function Ui(){}function $i(e,t){var n=yi();t=void 0===t?null:t;var a=n.memoizedState;return null!==a&&null!==t&&mi(t,a[1])?a[0]:(n.memoizedState=[e,t],e)}function qi(e,t){var n=yi();t=void 0===t?null:t;var a=n.memoizedState;return null!==a&&null!==t&&mi(t,a[1])?a[0]:(e=e(),n.memoizedState=[e,t],e)}function Bi(e,t,n){return 21&li?(ir(n,t)||(n=vn(),si.lanes|=n,Fc|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,xo=!0),e.memoizedState=n)}function Vi(e,t){var n=bn;bn=0!==n&&4>n?n:4,e(!0);var a=ri.transition;ri.transition={};try{e(!1),t()}finally{bn=n,ri.transition=a}}function Qi(){return yi().memoizedState}function Wi(e,t,n){var a=tu(e);if(n={lane:a,action:n,hasEagerState:!1,eagerState:null,next:null},Ki(e))Yi(t,n);else if(null!==(n=Ds(e,t,n,a))){nu(n,e,a,eu()),Ji(n,t,a)}}function Hi(e,t,n){var a=tu(e),r={lane:a,action:n,hasEagerState:!1,eagerState:null,next:null};if(Ki(e))Yi(t,r);else{var l=e.alternate;if(0===e.lanes&&(null===l||0===l.lanes)&&null!==(l=t.lastRenderedReducer))try{var s=t.lastRenderedState,i=l(s,n);if(r.hasEagerState=!0,r.eagerState=i,ir(i,s)){var o=t.interleaved;return null===o?(r.next=r,Rs(t)):(r.next=o.next,o.next=r),void(t.interleaved=r)}}catch(c){}null!==(n=Ds(e,t,r,a))&&(nu(n,e,a,r=eu()),Ji(n,t,a))}}function Ki(e){var t=e.alternate;return e===si||null!==t&&t===si}function Yi(e,t){ui=ci=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Ji(e,t,n){if(4194240&n){var a=t.lanes;n|=a&=e.pendingLanes,t.lanes=n,xn(e,n)}}var Gi={readContext:Ts,useCallback:hi,useContext:hi,useEffect:hi,useImperativeHandle:hi,useInsertionEffect:hi,useLayoutEffect:hi,useMemo:hi,useReducer:hi,useRef:hi,useState:hi,useDebugValue:hi,useDeferredValue:hi,useTransition:hi,useMutableSource:hi,useSyncExternalStore:hi,useId:hi,unstable_isNewReconciler:!1},Xi={readContext:Ts,useCallback:function(e,t){return gi().memoizedState=[e,void 0===t?null:t],e},useContext:Ts,useEffect:Ii,useImperativeHandle:function(e,t,n){return n=null!=n?n.concat([e]):null,Ri(4194308,4,zi.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Ri(4194308,4,e,t)},useInsertionEffect:function(e,t){return Ri(4,2,e,t)},useMemo:function(e,t){var n=gi();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var a=gi();return t=void 0!==n?n(t):t,a.memoizedState=a.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},a.queue=e,e=e.dispatch=Wi.bind(null,si,e),[a.memoizedState,e]},useRef:function(e){return e={current:e},gi().memoizedState=e},useState:Pi,useDebugValue:Ui,useDeferredValue:function(e){return gi().memoizedState=e},useTransition:function(){var e=Pi(!1),t=e[0];return e=Vi.bind(null,e[1]),gi().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var a=si,r=gi();if(rs){if(void 0===n)throw Error(le(407));n=n()}else{if(n=t(),null===Pc)throw Error(le(349));30&li||Si(a,t,n)}r.memoizedState=n;var l={value:n,getSnapshot:t};return r.queue=l,Ii(_i.bind(null,a,l,e),[e]),a.flags|=2048,Ti(9,Ni.bind(null,a,l,n,t),void 0,null),n},useId:function(){var e=gi(),t=Pc.identifierPrefix;if(rs){var n=Gl;t=":"+t+"R"+(n=(Jl&~(1<<32-sn(Jl)-1)).toString(32)+n),0<(n=di++)&&(t+="H"+n.toString(32)),t+=":"}else t=":"+t+"r"+(n=fi++).toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Zi={readContext:Ts,useCallback:$i,useContext:Ts,useEffect:Ai,useImperativeHandle:Mi,useInsertionEffect:Fi,useLayoutEffect:Oi,useMemo:qi,useReducer:bi,useRef:Li,useState:function(){return bi(xi)},useDebugValue:Ui,useDeferredValue:function(e){return Bi(yi(),ii.memoizedState,e)},useTransition:function(){return[bi(xi)[0],yi().memoizedState]},useMutableSource:wi,useSyncExternalStore:ki,useId:Qi,unstable_isNewReconciler:!1},eo={readContext:Ts,useCallback:$i,useContext:Ts,useEffect:Ai,useImperativeHandle:Mi,useInsertionEffect:Fi,useLayoutEffect:Oi,useMemo:qi,useReducer:ji,useRef:Li,useState:function(){return ji(xi)},useDebugValue:Ui,useDeferredValue:function(e){var t=yi();return null===ii?t.memoizedState=e:Bi(t,ii.memoizedState,e)},useTransition:function(){return[ji(xi)[0],yi().memoizedState]},useMutableSource:wi,useSyncExternalStore:ki,useId:Qi,unstable_isNewReconciler:!1};function to(e,t){if(e&&e.defaultProps){for(var n in t=Oe({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}return t}function no(e,t,n,a){n=null==(n=n(a,t=e.memoizedState))?t:Oe({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var ao={isMounted:function(e){return!!(e=e._reactInternals)&&qt(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var a=eu(),r=tu(e),l=zs(a,r);l.payload=t,null!=n&&(l.callback=n),null!==(t=Ms(e,l,r))&&(nu(t,e,r,a),Us(t,e,r))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var a=eu(),r=tu(e),l=zs(a,r);l.tag=1,l.payload=t,null!=n&&(l.callback=n),null!==(t=Ms(e,l,r))&&(nu(t,e,r,a),Us(t,e,r))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=eu(),a=tu(e),r=zs(n,a);r.tag=2,null!=t&&(r.callback=t),null!==(t=Ms(e,r,a))&&(nu(t,e,a,n),Us(t,e,a))}};function ro(e,t,n,a,r,l,s){return"function"==typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(a,l,s):!t.prototype||!t.prototype.isPureReactComponent||(!or(n,a)||!or(r,l))}function lo(e,t,n){var a=!1,r=Cl,l=t.contextType;return"object"==typeof l&&null!==l?l=Ts(l):(r=Rl(t)?Tl:El.current,l=(a=null!=(a=t.contextTypes))?Ll(e,r):Cl),t=new t(n,l),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=ao,e.stateNode=t,t._reactInternals=e,a&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=r,e.__reactInternalMemoizedMaskedChildContext=l),t}function so(e,t,n,a){e=t.state,"function"==typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,a),"function"==typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,a),t.state!==e&&ao.enqueueReplaceState(t,t.state,null)}function io(e,t,n,a){var r=e.stateNode;r.props=n,r.state=e.memoizedState,r.refs={},Fs(e);var l=t.contextType;"object"==typeof l&&null!==l?r.context=Ts(l):(l=Rl(t)?Tl:El.current,r.context=Ll(e,l)),r.state=e.memoizedState,"function"==typeof(l=t.getDerivedStateFromProps)&&(no(e,t,l,n),r.state=e.memoizedState),"function"==typeof t.getDerivedStateFromProps||"function"==typeof r.getSnapshotBeforeUpdate||"function"!=typeof r.UNSAFE_componentWillMount&&"function"!=typeof r.componentWillMount||(t=r.state,"function"==typeof r.componentWillMount&&r.componentWillMount(),"function"==typeof r.UNSAFE_componentWillMount&&r.UNSAFE_componentWillMount(),t!==r.state&&ao.enqueueReplaceState(r,r.state,null),qs(e,n,r,a),r.state=e.memoizedState),"function"==typeof r.componentDidMount&&(e.flags|=4194308)}function oo(e,t){try{var n="",a=t;do{n+=$e(a),a=a.return}while(a);var r=n}catch(l){r="\nError generating stack: "+l.message+"\n"+l.stack}return{value:e,source:t,stack:r,digest:null}}function co(e,t,n){return{value:e,source:null,stack:null!=n?n:null,digest:null!=t?t:null}}function uo(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var fo="function"==typeof WeakMap?WeakMap:Map;function ho(e,t,n){(n=zs(-1,n)).tag=3,n.payload={element:null};var a=t.value;return n.callback=function(){Vc||(Vc=!0,Qc=a),uo(0,t)},n}function mo(e,t,n){(n=zs(-1,n)).tag=3;var a=e.type.getDerivedStateFromError;if("function"==typeof a){var r=t.value;n.payload=function(){return a(r)},n.callback=function(){uo(0,t)}}var l=e.stateNode;return null!==l&&"function"==typeof l.componentDidCatch&&(n.callback=function(){uo(0,t),"function"!=typeof a&&(null===Wc?Wc=new Set([this]):Wc.add(this));var e=t.stack;this.componentDidCatch(t.value,{componentStack:null!==e?e:""})}),n}function po(e,t,n){var a=e.pingCache;if(null===a){a=e.pingCache=new fo;var r=new Set;a.set(t,r)}else void 0===(r=a.get(t))&&(r=new Set,a.set(t,r));r.has(n)||(r.add(n),e=Nu.bind(null,e,t,n),t.then(e,e))}function vo(e){do{var t;if((t=13===e.tag)&&(t=null===(t=e.memoizedState)||null!==t.dehydrated),t)return e;e=e.return}while(null!==e);return null}function go(e,t,n,a,r){return 1&e.mode?(e.flags|=65536,e.lanes=r,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,1===n.tag&&(null===n.alternate?n.tag=17:((t=zs(-1,1)).tag=2,Ms(n,t,1))),n.lanes|=1),e)}var yo=be.ReactCurrentOwner,xo=!1;function bo(e,t,n,a){t.child=null===e?js(t,null,n,a):bs(t,e.child,n,a)}function jo(e,t,n,a,r){n=n.render;var l=t.ref;return Ps(t,r),a=pi(e,t,n,a,l,r),n=vi(),null===e||xo?(rs&&n&&es(t),t.flags|=1,bo(e,t,a,r),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~r,Vo(e,t,r))}function wo(e,t,n,a,r){if(null===e){var l=n.type;return"function"!=typeof l||Ru(l)||void 0!==l.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=Iu(n.type,null,a,t,t.mode,r)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=l,ko(e,t,l,a,r))}if(l=e.child,0===(e.lanes&r)){var s=l.memoizedProps;if((n=null!==(n=n.compare)?n:or)(s,a)&&e.ref===t.ref)return Vo(e,t,r)}return t.flags|=1,(e=Du(l,a)).ref=t.ref,e.return=t,t.child=e}function ko(e,t,n,a,r){if(null!==e){var l=e.memoizedProps;if(or(l,a)&&e.ref===t.ref){if(xo=!1,t.pendingProps=a=l,0===(e.lanes&r))return t.lanes=e.lanes,Vo(e,t,r);131072&e.flags&&(xo=!0)}}return _o(e,t,n,a,r)}function So(e,t,n){var a=t.pendingProps,r=a.children,l=null!==e?e.memoizedState:null;if("hidden"===a.mode)if(1&t.mode){if(!(1073741824&n))return e=null!==l?l.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,_l(Dc,Rc),Rc|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},a=null!==l?l.baseLanes:n,_l(Dc,Rc),Rc|=a}else t.memoizedState={baseLanes:0,cachePool:null,transitions:null},_l(Dc,Rc),Rc|=n;else null!==l?(a=l.baseLanes|n,t.memoizedState=null):a=n,_l(Dc,Rc),Rc|=a;return bo(e,t,r,n),t.child}function No(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function _o(e,t,n,a,r){var l=Rl(n)?Tl:El.current;return l=Ll(t,l),Ps(t,r),n=pi(e,t,n,a,l,r),a=vi(),null===e||xo?(rs&&a&&es(t),t.flags|=1,bo(e,t,n,r),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~r,Vo(e,t,r))}function Co(e,t,n,a,r){if(Rl(n)){var l=!0;Fl(t)}else l=!1;if(Ps(t,r),null===t.stateNode)Bo(e,t),lo(t,n,a),io(t,n,a,r),a=!0;else if(null===e){var s=t.stateNode,i=t.memoizedProps;s.props=i;var o=s.context,c=n.contextType;"object"==typeof c&&null!==c?c=Ts(c):c=Ll(t,c=Rl(n)?Tl:El.current);var u=n.getDerivedStateFromProps,d="function"==typeof u||"function"==typeof s.getSnapshotBeforeUpdate;d||"function"!=typeof s.UNSAFE_componentWillReceiveProps&&"function"!=typeof s.componentWillReceiveProps||(i!==a||o!==c)&&so(t,s,a,c),As=!1;var f=t.memoizedState;s.state=f,qs(t,a,s,r),o=t.memoizedState,i!==a||f!==o||Pl.current||As?("function"==typeof u&&(no(t,n,u,a),o=t.memoizedState),(i=As||ro(t,n,i,a,f,o,c))?(d||"function"!=typeof s.UNSAFE_componentWillMount&&"function"!=typeof s.componentWillMount||("function"==typeof s.componentWillMount&&s.componentWillMount(),"function"==typeof s.UNSAFE_componentWillMount&&s.UNSAFE_componentWillMount()),"function"==typeof s.componentDidMount&&(t.flags|=4194308)):("function"==typeof s.componentDidMount&&(t.flags|=4194308),t.memoizedProps=a,t.memoizedState=o),s.props=a,s.state=o,s.context=c,a=i):("function"==typeof s.componentDidMount&&(t.flags|=4194308),a=!1)}else{s=t.stateNode,Os(e,t),i=t.memoizedProps,c=t.type===t.elementType?i:to(t.type,i),s.props=c,d=t.pendingProps,f=s.context,"object"==typeof(o=n.contextType)&&null!==o?o=Ts(o):o=Ll(t,o=Rl(n)?Tl:El.current);var h=n.getDerivedStateFromProps;(u="function"==typeof h||"function"==typeof s.getSnapshotBeforeUpdate)||"function"!=typeof s.UNSAFE_componentWillReceiveProps&&"function"!=typeof s.componentWillReceiveProps||(i!==d||f!==o)&&so(t,s,a,o),As=!1,f=t.memoizedState,s.state=f,qs(t,a,s,r);var m=t.memoizedState;i!==d||f!==m||Pl.current||As?("function"==typeof h&&(no(t,n,h,a),m=t.memoizedState),(c=As||ro(t,n,c,a,f,m,o)||!1)?(u||"function"!=typeof s.UNSAFE_componentWillUpdate&&"function"!=typeof s.componentWillUpdate||("function"==typeof s.componentWillUpdate&&s.componentWillUpdate(a,m,o),"function"==typeof s.UNSAFE_componentWillUpdate&&s.UNSAFE_componentWillUpdate(a,m,o)),"function"==typeof s.componentDidUpdate&&(t.flags|=4),"function"==typeof s.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!=typeof s.componentDidUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!=typeof s.getSnapshotBeforeUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=a,t.memoizedState=m),s.props=a,s.state=m,s.context=o,a=c):("function"!=typeof s.componentDidUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!=typeof s.getSnapshotBeforeUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),a=!1)}return Eo(e,t,n,a,l,r)}function Eo(e,t,n,a,r,l){No(e,t);var s=!!(128&t.flags);if(!a&&!s)return r&&Ol(t,n,!1),Vo(e,t,l);a=t.stateNode,yo.current=t;var i=s&&"function"!=typeof n.getDerivedStateFromError?null:a.render();return t.flags|=1,null!==e&&s?(t.child=bs(t,e.child,null,l),t.child=bs(t,null,i,l)):bo(e,t,i,l),t.memoizedState=a.state,r&&Ol(t,n,!0),t.child}function Po(e){var t=e.stateNode;t.pendingContext?Il(0,t.pendingContext,t.pendingContext!==t.context):t.context&&Il(0,t.context,!1),Ys(e,t.containerInfo)}function To(e,t,n,a,r){return hs(),ms(r),t.flags|=256,bo(e,t,n,a),t.child}var Lo,Ro,Do,Io,Ao={dehydrated:null,treeContext:null,retryLane:0};function Fo(e){return{baseLanes:e,cachePool:null,transitions:null}}function Oo(e,t,n){var a,r=t.pendingProps,l=Zs.current,s=!1,i=!!(128&t.flags);if((a=i)||(a=(null===e||null!==e.memoizedState)&&!!(2&l)),a?(s=!0,t.flags&=-129):null!==e&&null===e.memoizedState||(l|=1),_l(Zs,1&l),null===e)return cs(t),null!==(e=t.memoizedState)&&null!==(e=e.dehydrated)?(1&t.mode?"$!"===e.data?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(i=r.children,e=r.fallback,s?(r=t.mode,s=t.child,i={mode:"hidden",children:i},1&r||null===s?s=Fu(i,r,0,null):(s.childLanes=0,s.pendingProps=i),e=Au(e,r,n,null),s.return=t,e.return=t,s.sibling=e,t.child=s,t.child.memoizedState=Fo(n),t.memoizedState=Ao,e):zo(t,i));if(null!==(l=e.memoizedState)&&null!==(a=l.dehydrated))return function(e,t,n,a,r,l,s){if(n)return 256&t.flags?(t.flags&=-257,Mo(e,t,s,a=co(Error(le(422))))):null!==t.memoizedState?(t.child=e.child,t.flags|=128,null):(l=a.fallback,r=t.mode,a=Fu({mode:"visible",children:a.children},r,0,null),(l=Au(l,r,s,null)).flags|=2,a.return=t,l.return=t,a.sibling=l,t.child=a,1&t.mode&&bs(t,e.child,null,s),t.child.memoizedState=Fo(s),t.memoizedState=Ao,l);if(!(1&t.mode))return Mo(e,t,s,null);if("$!"===r.data){if(a=r.nextSibling&&r.nextSibling.dataset)var i=a.dgst;return a=i,Mo(e,t,s,a=co(l=Error(le(419)),a,void 0))}if(i=0!==(s&e.childLanes),xo||i){if(null!==(a=Pc)){switch(s&-s){case 4:r=2;break;case 16:r=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:r=32;break;case 536870912:r=268435456;break;default:r=0}0!==(r=0!==(r&(a.suspendedLanes|s))?0:r)&&r!==l.retryLane&&(l.retryLane=r,Is(e,r),nu(a,e,r,-1))}return pu(),Mo(e,t,s,a=co(Error(le(421))))}return"$?"===r.data?(t.flags|=128,t.child=e.child,t=Cu.bind(null,e),r._reactRetry=t,null):(e=l.treeContext,as=cl(r.nextSibling),ns=t,rs=!0,ls=null,null!==e&&(Hl[Kl++]=Jl,Hl[Kl++]=Gl,Hl[Kl++]=Yl,Jl=e.id,Gl=e.overflow,Yl=t),t=zo(t,a.children),t.flags|=4096,t)}(e,t,i,r,a,l,n);if(s){s=r.fallback,i=t.mode,a=(l=e.child).sibling;var o={mode:"hidden",children:r.children};return 1&i||t.child===l?(r=Du(l,o)).subtreeFlags=14680064&l.subtreeFlags:((r=t.child).childLanes=0,r.pendingProps=o,t.deletions=null),null!==a?s=Du(a,s):(s=Au(s,i,n,null)).flags|=2,s.return=t,r.return=t,r.sibling=s,t.child=r,r=s,s=t.child,i=null===(i=e.child.memoizedState)?Fo(n):{baseLanes:i.baseLanes|n,cachePool:null,transitions:i.transitions},s.memoizedState=i,s.childLanes=e.childLanes&~n,t.memoizedState=Ao,r}return e=(s=e.child).sibling,r=Du(s,{mode:"visible",children:r.children}),!(1&t.mode)&&(r.lanes=n),r.return=t,r.sibling=null,null!==e&&(null===(n=t.deletions)?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function zo(e,t){return(t=Fu({mode:"visible",children:t},e.mode,0,null)).return=e,e.child=t}function Mo(e,t,n,a){return null!==a&&ms(a),bs(t,e.child,null,n),(e=zo(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Uo(e,t,n){e.lanes|=t;var a=e.alternate;null!==a&&(a.lanes|=t),Es(e.return,t,n)}function $o(e,t,n,a,r){var l=e.memoizedState;null===l?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:a,tail:n,tailMode:r}:(l.isBackwards=t,l.rendering=null,l.renderingStartTime=0,l.last=a,l.tail=n,l.tailMode=r)}function qo(e,t,n){var a=t.pendingProps,r=a.revealOrder,l=a.tail;if(bo(e,t,a.children,n),2&(a=Zs.current))a=1&a|2,t.flags|=128;else{if(null!==e&&128&e.flags)e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Uo(e,n,t);else if(19===e.tag)Uo(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}a&=1}if(_l(Zs,a),1&t.mode)switch(r){case"forwards":for(n=t.child,r=null;null!==n;)null!==(e=n.alternate)&&null===ei(e)&&(r=n),n=n.sibling;null===(n=r)?(r=t.child,t.child=null):(r=n.sibling,n.sibling=null),$o(t,!1,r,n,l);break;case"backwards":for(n=null,r=t.child,t.child=null;null!==r;){if(null!==(e=r.alternate)&&null===ei(e)){t.child=r;break}e=r.sibling,r.sibling=n,n=r,r=e}$o(t,!0,n,null,l);break;case"together":$o(t,!1,null,null,void 0);break;default:t.memoizedState=null}else t.memoizedState=null;return t.child}function Bo(e,t){!(1&t.mode)&&null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Vo(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),Fc|=t.lanes,0===(n&t.childLanes))return null;if(null!==e&&t.child!==e.child)throw Error(le(153));if(null!==t.child){for(n=Du(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Du(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Qo(e,t){if(!rs)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var a=null;null!==n;)null!==n.alternate&&(a=n),n=n.sibling;null===a?t||null===e.tail?e.tail=null:e.tail.sibling=null:a.sibling=null}}function Wo(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,a=0;if(t)for(var r=e.child;null!==r;)n|=r.lanes|r.childLanes,a|=14680064&r.subtreeFlags,a|=14680064&r.flags,r.return=e,r=r.sibling;else for(r=e.child;null!==r;)n|=r.lanes|r.childLanes,a|=r.subtreeFlags,a|=r.flags,r.return=e,r=r.sibling;return e.subtreeFlags|=a,e.childLanes=n,t}function Ho(e,t,n){var a=t.pendingProps;switch(ts(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Wo(t),null;case 1:case 17:return Rl(t.type)&&Dl(),Wo(t),null;case 3:return a=t.stateNode,Js(),Nl(Pl),Nl(El),ni(),a.pendingContext&&(a.context=a.pendingContext,a.pendingContext=null),null!==e&&null!==e.child||(ds(t)?t.flags|=4:null===e||e.memoizedState.isDehydrated&&!(256&t.flags)||(t.flags|=1024,null!==ls&&(su(ls),ls=null))),Ro(e,t),Wo(t),null;case 5:Xs(t);var r=Ks(Hs.current);if(n=t.type,null!==e&&null!=t.stateNode)Do(e,t,n,a,r),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!a){if(null===t.stateNode)throw Error(le(166));return Wo(t),null}if(e=Ks(Qs.current),ds(t)){a=t.stateNode,n=t.type;var l=t.memoizedProps;switch(a[fl]=t,a[hl]=l,e=!!(1&t.mode),n){case"dialog":Mr("cancel",a),Mr("close",a);break;case"iframe":case"object":case"embed":Mr("load",a);break;case"video":case"audio":for(r=0;r<Ar.length;r++)Mr(Ar[r],a);break;case"source":Mr("error",a);break;case"img":case"image":case"link":Mr("error",a),Mr("load",a);break;case"details":Mr("toggle",a);break;case"input":Je(a,l),Mr("invalid",a);break;case"select":a._wrapperState={wasMultiple:!!l.multiple},Mr("invalid",a);break;case"textarea":rt(a,l),Mr("invalid",a)}for(var s in yt(n,l),r=null,l)if(l.hasOwnProperty(s)){var i=l[s];"children"===s?"string"==typeof i?a.textContent!==i&&(!0!==l.suppressHydrationWarning&&Xr(a.textContent,i,e),r=["children",i]):"number"==typeof i&&a.textContent!==""+i&&(!0!==l.suppressHydrationWarning&&Xr(a.textContent,i,e),r=["children",""+i]):ie.hasOwnProperty(s)&&null!=i&&"onScroll"===s&&Mr("scroll",a)}switch(n){case"input":We(a),Ze(a,l,!0);break;case"textarea":We(a),st(a);break;case"select":case"option":break;default:"function"==typeof l.onClick&&(a.onclick=Zr)}a=r,t.updateQueue=a,null!==a&&(t.flags|=4)}else{s=9===r.nodeType?r:r.ownerDocument,"http://www.w3.org/1999/xhtml"===e&&(e=it(n)),"http://www.w3.org/1999/xhtml"===e?"script"===n?((e=s.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"==typeof a.is?e=s.createElement(n,{is:a.is}):(e=s.createElement(n),"select"===n&&(s=e,a.multiple?s.multiple=!0:a.size&&(s.size=a.size))):e=s.createElementNS(e,n),e[fl]=t,e[hl]=a,Lo(e,t,!1,!1),t.stateNode=e;e:{switch(s=xt(n,a),n){case"dialog":Mr("cancel",e),Mr("close",e),r=a;break;case"iframe":case"object":case"embed":Mr("load",e),r=a;break;case"video":case"audio":for(r=0;r<Ar.length;r++)Mr(Ar[r],e);r=a;break;case"source":Mr("error",e),r=a;break;case"img":case"image":case"link":Mr("error",e),Mr("load",e),r=a;break;case"details":Mr("toggle",e),r=a;break;case"input":Je(e,a),r=Ye(e,a),Mr("invalid",e);break;case"option":default:r=a;break;case"select":e._wrapperState={wasMultiple:!!a.multiple},r=Oe({},a,{value:void 0}),Mr("invalid",e);break;case"textarea":rt(e,a),r=at(e,a),Mr("invalid",e)}for(l in yt(n,r),i=r)if(i.hasOwnProperty(l)){var o=i[l];"style"===l?vt(e,o):"dangerouslySetInnerHTML"===l?null!=(o=o?o.__html:void 0)&&dt(e,o):"children"===l?"string"==typeof o?("textarea"!==n||""!==o)&&ft(e,o):"number"==typeof o&&ft(e,""+o):"suppressContentEditableWarning"!==l&&"suppressHydrationWarning"!==l&&"autoFocus"!==l&&(ie.hasOwnProperty(l)?null!=o&&"onScroll"===l&&Mr("scroll",e):null!=o&&xe(e,l,o,s))}switch(n){case"input":We(e),Ze(e,a,!1);break;case"textarea":We(e),st(e);break;case"option":null!=a.value&&e.setAttribute("value",""+Ve(a.value));break;case"select":e.multiple=!!a.multiple,null!=(l=a.value)?nt(e,!!a.multiple,l,!1):null!=a.defaultValue&&nt(e,!!a.multiple,a.defaultValue,!0);break;default:"function"==typeof r.onClick&&(e.onclick=Zr)}switch(n){case"button":case"input":case"select":case"textarea":a=!!a.autoFocus;break e;case"img":a=!0;break e;default:a=!1}}a&&(t.flags|=4)}null!==t.ref&&(t.flags|=512,t.flags|=2097152)}return Wo(t),null;case 6:if(e&&null!=t.stateNode)Io(e,t,e.memoizedProps,a);else{if("string"!=typeof a&&null===t.stateNode)throw Error(le(166));if(n=Ks(Hs.current),Ks(Qs.current),ds(t)){if(a=t.stateNode,n=t.memoizedProps,a[fl]=t,(l=a.nodeValue!==n)&&null!==(e=ns))switch(e.tag){case 3:Xr(a.nodeValue,n,!!(1&e.mode));break;case 5:!0!==e.memoizedProps.suppressHydrationWarning&&Xr(a.nodeValue,n,!!(1&e.mode))}l&&(t.flags|=4)}else(a=(9===n.nodeType?n:n.ownerDocument).createTextNode(a))[fl]=t,t.stateNode=a}return Wo(t),null;case 13:if(Nl(Zs),a=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(rs&&null!==as&&1&t.mode&&!(128&t.flags))fs(),hs(),t.flags|=98560,l=!1;else if(l=ds(t),null!==a&&null!==a.dehydrated){if(null===e){if(!l)throw Error(le(318));if(!(l=null!==(l=t.memoizedState)?l.dehydrated:null))throw Error(le(317));l[fl]=t}else hs(),!(128&t.flags)&&(t.memoizedState=null),t.flags|=4;Wo(t),l=!1}else null!==ls&&(su(ls),ls=null),l=!0;if(!l)return 65536&t.flags?t:null}return 128&t.flags?(t.lanes=n,t):((a=null!==a)!==(null!==e&&null!==e.memoizedState)&&a&&(t.child.flags|=8192,1&t.mode&&(null===e||1&Zs.current?0===Ic&&(Ic=3):pu())),null!==t.updateQueue&&(t.flags|=4),Wo(t),null);case 4:return Js(),Ro(e,t),null===e&&qr(t.stateNode.containerInfo),Wo(t),null;case 10:return Cs(t.type._context),Wo(t),null;case 19:if(Nl(Zs),null===(l=t.memoizedState))return Wo(t),null;if(a=!!(128&t.flags),null===(s=l.rendering))if(a)Qo(l,!1);else{if(0!==Ic||null!==e&&128&e.flags)for(e=t.child;null!==e;){if(null!==(s=ei(e))){for(t.flags|=128,Qo(l,!1),null!==(a=s.updateQueue)&&(t.updateQueue=a,t.flags|=4),t.subtreeFlags=0,a=n,n=t.child;null!==n;)e=a,(l=n).flags&=14680066,null===(s=l.alternate)?(l.childLanes=0,l.lanes=e,l.child=null,l.subtreeFlags=0,l.memoizedProps=null,l.memoizedState=null,l.updateQueue=null,l.dependencies=null,l.stateNode=null):(l.childLanes=s.childLanes,l.lanes=s.lanes,l.child=s.child,l.subtreeFlags=0,l.deletions=null,l.memoizedProps=s.memoizedProps,l.memoizedState=s.memoizedState,l.updateQueue=s.updateQueue,l.type=s.type,e=s.dependencies,l.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return _l(Zs,1&Zs.current|2),t.child}e=e.sibling}null!==l.tail&&Gt()>qc&&(t.flags|=128,a=!0,Qo(l,!1),t.lanes=4194304)}else{if(!a)if(null!==(e=ei(s))){if(t.flags|=128,a=!0,null!==(n=e.updateQueue)&&(t.updateQueue=n,t.flags|=4),Qo(l,!0),null===l.tail&&"hidden"===l.tailMode&&!s.alternate&&!rs)return Wo(t),null}else 2*Gt()-l.renderingStartTime>qc&&1073741824!==n&&(t.flags|=128,a=!0,Qo(l,!1),t.lanes=4194304);l.isBackwards?(s.sibling=t.child,t.child=s):(null!==(n=l.last)?n.sibling=s:t.child=s,l.last=s)}return null!==l.tail?(t=l.tail,l.rendering=t,l.tail=t.sibling,l.renderingStartTime=Gt(),t.sibling=null,n=Zs.current,_l(Zs,a?1&n|2:1&n),t):(Wo(t),null);case 22:case 23:return du(),a=null!==t.memoizedState,null!==e&&null!==e.memoizedState!==a&&(t.flags|=8192),a&&1&t.mode?!!(1073741824&Rc)&&(Wo(t),6&t.subtreeFlags&&(t.flags|=8192)):Wo(t),null;case 24:case 25:return null}throw Error(le(156,t.tag))}function Ko(e,t){switch(ts(t),t.tag){case 1:return Rl(t.type)&&Dl(),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return Js(),Nl(Pl),Nl(El),ni(),65536&(e=t.flags)&&!(128&e)?(t.flags=-65537&e|128,t):null;case 5:return Xs(t),null;case 13:if(Nl(Zs),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(le(340));hs()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return Nl(Zs),null;case 4:return Js(),null;case 10:return Cs(t.type._context),null;case 22:case 23:return du(),null;default:return null}}Lo=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},Ro=function(){},Do=function(e,t,n,a){var r=e.memoizedProps;if(r!==a){e=t.stateNode,Ks(Qs.current);var l,s=null;switch(n){case"input":r=Ye(e,r),a=Ye(e,a),s=[];break;case"select":r=Oe({},r,{value:void 0}),a=Oe({},a,{value:void 0}),s=[];break;case"textarea":r=at(e,r),a=at(e,a),s=[];break;default:"function"!=typeof r.onClick&&"function"==typeof a.onClick&&(e.onclick=Zr)}for(c in yt(n,a),n=null,r)if(!a.hasOwnProperty(c)&&r.hasOwnProperty(c)&&null!=r[c])if("style"===c){var i=r[c];for(l in i)i.hasOwnProperty(l)&&(n||(n={}),n[l]="")}else"dangerouslySetInnerHTML"!==c&&"children"!==c&&"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&"autoFocus"!==c&&(ie.hasOwnProperty(c)?s||(s=[]):(s=s||[]).push(c,null));for(c in a){var o=a[c];if(i=null!=r?r[c]:void 0,a.hasOwnProperty(c)&&o!==i&&(null!=o||null!=i))if("style"===c)if(i){for(l in i)!i.hasOwnProperty(l)||o&&o.hasOwnProperty(l)||(n||(n={}),n[l]="");for(l in o)o.hasOwnProperty(l)&&i[l]!==o[l]&&(n||(n={}),n[l]=o[l])}else n||(s||(s=[]),s.push(c,n)),n=o;else"dangerouslySetInnerHTML"===c?(o=o?o.__html:void 0,i=i?i.__html:void 0,null!=o&&i!==o&&(s=s||[]).push(c,o)):"children"===c?"string"!=typeof o&&"number"!=typeof o||(s=s||[]).push(c,""+o):"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&(ie.hasOwnProperty(c)?(null!=o&&"onScroll"===c&&Mr("scroll",e),s||i===o||(s=[])):(s=s||[]).push(c,o))}n&&(s=s||[]).push("style",n);var c=s;(t.updateQueue=c)&&(t.flags|=4)}},Io=function(e,t,n,a){n!==a&&(t.flags|=4)};var Yo=!1,Jo=!1,Go="function"==typeof WeakSet?WeakSet:Set,Xo=null;function Zo(e,t){var n=e.ref;if(null!==n)if("function"==typeof n)try{n(null)}catch(a){Su(e,t,a)}else n.current=null}function ec(e,t,n){try{n()}catch(a){Su(e,t,a)}}var tc=!1;function nc(e,t,n){var a=t.updateQueue;if(null!==(a=null!==a?a.lastEffect:null)){var r=a=a.next;do{if((r.tag&e)===e){var l=r.destroy;r.destroy=void 0,void 0!==l&&ec(t,n,l)}r=r.next}while(r!==a)}}function ac(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var a=n.create;n.destroy=a()}n=n.next}while(n!==t)}}function rc(e){var t=e.ref;if(null!==t){var n=e.stateNode;e.tag,e=n,"function"==typeof t?t(e):t.current=e}}function lc(e){var t=e.alternate;null!==t&&(e.alternate=null,lc(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&(delete t[fl],delete t[hl],delete t[pl],delete t[vl],delete t[gl])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function sc(e){return 5===e.tag||3===e.tag||4===e.tag}function ic(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||sc(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function oc(e,t,n){var a=e.tag;if(5===a||6===a)e=e.stateNode,t?8===n.nodeType?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(8===n.nodeType?(t=n.parentNode).insertBefore(e,n):(t=n).appendChild(e),null!=(n=n._reactRootContainer)||null!==t.onclick||(t.onclick=Zr));else if(4!==a&&null!==(e=e.child))for(oc(e,t,n),e=e.sibling;null!==e;)oc(e,t,n),e=e.sibling}function cc(e,t,n){var a=e.tag;if(5===a||6===a)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==a&&null!==(e=e.child))for(cc(e,t,n),e=e.sibling;null!==e;)cc(e,t,n),e=e.sibling}var uc=null,dc=!1;function fc(e,t,n){for(n=n.child;null!==n;)hc(e,t,n),n=n.sibling}function hc(e,t,n){if(ln&&"function"==typeof ln.onCommitFiberUnmount)try{ln.onCommitFiberUnmount(rn,n)}catch(i){}switch(n.tag){case 5:Jo||Zo(n,t);case 6:var a=uc,r=dc;uc=null,fc(e,t,n),dc=r,null!==(uc=a)&&(dc?(e=uc,n=n.stateNode,8===e.nodeType?e.parentNode.removeChild(n):e.removeChild(n)):uc.removeChild(n.stateNode));break;case 18:null!==uc&&(dc?(e=uc,n=n.stateNode,8===e.nodeType?ol(e.parentNode,n):1===e.nodeType&&ol(e,n),Bn(e)):ol(uc,n.stateNode));break;case 4:a=uc,r=dc,uc=n.stateNode.containerInfo,dc=!0,fc(e,t,n),uc=a,dc=r;break;case 0:case 11:case 14:case 15:if(!Jo&&(null!==(a=n.updateQueue)&&null!==(a=a.lastEffect))){r=a=a.next;do{var l=r,s=l.destroy;l=l.tag,void 0!==s&&(2&l||4&l)&&ec(n,t,s),r=r.next}while(r!==a)}fc(e,t,n);break;case 1:if(!Jo&&(Zo(n,t),"function"==typeof(a=n.stateNode).componentWillUnmount))try{a.props=n.memoizedProps,a.state=n.memoizedState,a.componentWillUnmount()}catch(i){Su(n,t,i)}fc(e,t,n);break;case 21:fc(e,t,n);break;case 22:1&n.mode?(Jo=(a=Jo)||null!==n.memoizedState,fc(e,t,n),Jo=a):fc(e,t,n);break;default:fc(e,t,n)}}function mc(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new Go),t.forEach(function(t){var a=Eu.bind(null,e,t);n.has(t)||(n.add(t),t.then(a,a))})}}function pc(e,t){var n=t.deletions;if(null!==n)for(var a=0;a<n.length;a++){var r=n[a];try{var l=e,s=t,i=s;e:for(;null!==i;){switch(i.tag){case 5:uc=i.stateNode,dc=!1;break e;case 3:case 4:uc=i.stateNode.containerInfo,dc=!0;break e}i=i.return}if(null===uc)throw Error(le(160));hc(l,s,r),uc=null,dc=!1;var o=r.alternate;null!==o&&(o.return=null),r.return=null}catch(c){Su(r,t,c)}}if(12854&t.subtreeFlags)for(t=t.child;null!==t;)vc(t,e),t=t.sibling}function vc(e,t){var n=e.alternate,a=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(pc(t,e),gc(e),4&a){try{nc(3,e,e.return),ac(3,e)}catch(p){Su(e,e.return,p)}try{nc(5,e,e.return)}catch(p){Su(e,e.return,p)}}break;case 1:pc(t,e),gc(e),512&a&&null!==n&&Zo(n,n.return);break;case 5:if(pc(t,e),gc(e),512&a&&null!==n&&Zo(n,n.return),32&e.flags){var r=e.stateNode;try{ft(r,"")}catch(p){Su(e,e.return,p)}}if(4&a&&null!=(r=e.stateNode)){var l=e.memoizedProps,s=null!==n?n.memoizedProps:l,i=e.type,o=e.updateQueue;if(e.updateQueue=null,null!==o)try{"input"===i&&"radio"===l.type&&null!=l.name&&Ge(r,l),xt(i,s);var c=xt(i,l);for(s=0;s<o.length;s+=2){var u=o[s],d=o[s+1];"style"===u?vt(r,d):"dangerouslySetInnerHTML"===u?dt(r,d):"children"===u?ft(r,d):xe(r,u,d,c)}switch(i){case"input":Xe(r,l);break;case"textarea":lt(r,l);break;case"select":var f=r._wrapperState.wasMultiple;r._wrapperState.wasMultiple=!!l.multiple;var h=l.value;null!=h?nt(r,!!l.multiple,h,!1):f!==!!l.multiple&&(null!=l.defaultValue?nt(r,!!l.multiple,l.defaultValue,!0):nt(r,!!l.multiple,l.multiple?[]:"",!1))}r[hl]=l}catch(p){Su(e,e.return,p)}}break;case 6:if(pc(t,e),gc(e),4&a){if(null===e.stateNode)throw Error(le(162));r=e.stateNode,l=e.memoizedProps;try{r.nodeValue=l}catch(p){Su(e,e.return,p)}}break;case 3:if(pc(t,e),gc(e),4&a&&null!==n&&n.memoizedState.isDehydrated)try{Bn(t.containerInfo)}catch(p){Su(e,e.return,p)}break;case 4:default:pc(t,e),gc(e);break;case 13:pc(t,e),gc(e),8192&(r=e.child).flags&&(l=null!==r.memoizedState,r.stateNode.isHidden=l,!l||null!==r.alternate&&null!==r.alternate.memoizedState||($c=Gt())),4&a&&mc(e);break;case 22:if(u=null!==n&&null!==n.memoizedState,1&e.mode?(Jo=(c=Jo)||u,pc(t,e),Jo=c):pc(t,e),gc(e),8192&a){if(c=null!==e.memoizedState,(e.stateNode.isHidden=c)&&!u&&1&e.mode)for(Xo=e,u=e.child;null!==u;){for(d=Xo=u;null!==Xo;){switch(h=(f=Xo).child,f.tag){case 0:case 11:case 14:case 15:nc(4,f,f.return);break;case 1:Zo(f,f.return);var m=f.stateNode;if("function"==typeof m.componentWillUnmount){a=f,n=f.return;try{t=a,m.props=t.memoizedProps,m.state=t.memoizedState,m.componentWillUnmount()}catch(p){Su(a,n,p)}}break;case 5:Zo(f,f.return);break;case 22:if(null!==f.memoizedState){jc(d);continue}}null!==h?(h.return=f,Xo=h):jc(d)}u=u.sibling}e:for(u=null,d=e;;){if(5===d.tag){if(null===u){u=d;try{r=d.stateNode,c?"function"==typeof(l=r.style).setProperty?l.setProperty("display","none","important"):l.display="none":(i=d.stateNode,s=null!=(o=d.memoizedProps.style)&&o.hasOwnProperty("display")?o.display:null,i.style.display=pt("display",s))}catch(p){Su(e,e.return,p)}}}else if(6===d.tag){if(null===u)try{d.stateNode.nodeValue=c?"":d.memoizedProps}catch(p){Su(e,e.return,p)}}else if((22!==d.tag&&23!==d.tag||null===d.memoizedState||d===e)&&null!==d.child){d.child.return=d,d=d.child;continue}if(d===e)break e;for(;null===d.sibling;){if(null===d.return||d.return===e)break e;u===d&&(u=null),d=d.return}u===d&&(u=null),d.sibling.return=d.return,d=d.sibling}}break;case 19:pc(t,e),gc(e),4&a&&mc(e);case 21:}}function gc(e){var t=e.flags;if(2&t){try{e:{for(var n=e.return;null!==n;){if(sc(n)){var a=n;break e}n=n.return}throw Error(le(160))}switch(a.tag){case 5:var r=a.stateNode;32&a.flags&&(ft(r,""),a.flags&=-33),cc(e,ic(e),r);break;case 3:case 4:var l=a.stateNode.containerInfo;oc(e,ic(e),l);break;default:throw Error(le(161))}}catch(s){Su(e,e.return,s)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function yc(e,t,n){Xo=e,xc(e)}function xc(e,t,n){for(var a=!!(1&e.mode);null!==Xo;){var r=Xo,l=r.child;if(22===r.tag&&a){var s=null!==r.memoizedState||Yo;if(!s){var i=r.alternate,o=null!==i&&null!==i.memoizedState||Jo;i=Yo;var c=Jo;if(Yo=s,(Jo=o)&&!c)for(Xo=r;null!==Xo;)o=(s=Xo).child,22===s.tag&&null!==s.memoizedState?wc(r):null!==o?(o.return=s,Xo=o):wc(r);for(;null!==l;)Xo=l,xc(l),l=l.sibling;Xo=r,Yo=i,Jo=c}bc(e)}else 8772&r.subtreeFlags&&null!==l?(l.return=r,Xo=l):bc(e)}}function bc(e){for(;null!==Xo;){var t=Xo;if(8772&t.flags){var n=t.alternate;try{if(8772&t.flags)switch(t.tag){case 0:case 11:case 15:Jo||ac(5,t);break;case 1:var a=t.stateNode;if(4&t.flags&&!Jo)if(null===n)a.componentDidMount();else{var r=t.elementType===t.type?n.memoizedProps:to(t.type,n.memoizedProps);a.componentDidUpdate(r,n.memoizedState,a.__reactInternalSnapshotBeforeUpdate)}var l=t.updateQueue;null!==l&&Bs(t,l,a);break;case 3:var s=t.updateQueue;if(null!==s){if(n=null,null!==t.child)switch(t.child.tag){case 5:case 1:n=t.child.stateNode}Bs(t,s,n)}break;case 5:var i=t.stateNode;if(null===n&&4&t.flags){n=i;var o=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":o.autoFocus&&n.focus();break;case"img":o.src&&(n.src=o.src)}}break;case 6:case 4:case 12:case 19:case 17:case 21:case 22:case 23:case 25:break;case 13:if(null===t.memoizedState){var c=t.alternate;if(null!==c){var u=c.memoizedState;if(null!==u){var d=u.dehydrated;null!==d&&Bn(d)}}}break;default:throw Error(le(163))}Jo||512&t.flags&&rc(t)}catch(f){Su(t,t.return,f)}}if(t===e){Xo=null;break}if(null!==(n=t.sibling)){n.return=t.return,Xo=n;break}Xo=t.return}}function jc(e){for(;null!==Xo;){var t=Xo;if(t===e){Xo=null;break}var n=t.sibling;if(null!==n){n.return=t.return,Xo=n;break}Xo=t.return}}function wc(e){for(;null!==Xo;){var t=Xo;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{ac(4,t)}catch(o){Su(t,n,o)}break;case 1:var a=t.stateNode;if("function"==typeof a.componentDidMount){var r=t.return;try{a.componentDidMount()}catch(o){Su(t,r,o)}}var l=t.return;try{rc(t)}catch(o){Su(t,l,o)}break;case 5:var s=t.return;try{rc(t)}catch(o){Su(t,s,o)}}}catch(o){Su(t,t.return,o)}if(t===e){Xo=null;break}var i=t.sibling;if(null!==i){i.return=t.return,Xo=i;break}Xo=t.return}}var kc,Sc=Math.ceil,Nc=be.ReactCurrentDispatcher,_c=be.ReactCurrentOwner,Cc=be.ReactCurrentBatchConfig,Ec=0,Pc=null,Tc=null,Lc=0,Rc=0,Dc=Sl(0),Ic=0,Ac=null,Fc=0,Oc=0,zc=0,Mc=null,Uc=null,$c=0,qc=1/0,Bc=null,Vc=!1,Qc=null,Wc=null,Hc=!1,Kc=null,Yc=0,Jc=0,Gc=null,Xc=-1,Zc=0;function eu(){return 6&Ec?Gt():-1!==Xc?Xc:Xc=Gt()}function tu(e){return 1&e.mode?2&Ec&&0!==Lc?Lc&-Lc:null!==ps.transition?(0===Zc&&(Zc=vn()),Zc):0!==(e=bn)?e:e=void 0===(e=window.event)?16:Gn(e.type):1}function nu(e,t,n,a){if(50<Jc)throw Jc=0,Gc=null,Error(le(185));yn(e,n,a),2&Ec&&e===Pc||(e===Pc&&(!(2&Ec)&&(Oc|=n),4===Ic&&iu(e,Lc)),au(e,a),1===n&&0===Ec&&!(1&t.mode)&&(qc=Gt()+500,Ml&&ql()))}function au(e,t){var n=e.callbackNode;!function(e,t){for(var n=e.suspendedLanes,a=e.pingedLanes,r=e.expirationTimes,l=e.pendingLanes;0<l;){var s=31-sn(l),i=1<<s,o=r[s];-1===o?0!==(i&n)&&0===(i&a)||(r[s]=mn(i,t)):o<=t&&(e.expiredLanes|=i),l&=~i}}(e,t);var a=hn(e,e===Pc?Lc:0);if(0===a)null!==n&&Kt(n),e.callbackNode=null,e.callbackPriority=0;else if(t=a&-a,e.callbackPriority!==t){if(null!=n&&Kt(n),1===t)0===e.tag?function(e){Ml=!0,$l(e)}(ou.bind(null,e)):$l(ou.bind(null,e)),sl(function(){!(6&Ec)&&ql()}),n=null;else{switch(jn(a)){case 1:n=Zt;break;case 4:n=en;break;case 16:default:n=tn;break;case 536870912:n=an}n=Pu(n,ru.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function ru(e,t){if(Xc=-1,Zc=0,6&Ec)throw Error(le(327));var n=e.callbackNode;if(wu()&&e.callbackNode!==n)return null;var a=hn(e,e===Pc?Lc:0);if(0===a)return null;if(30&a||0!==(a&e.expiredLanes)||t)t=vu(e,a);else{t=a;var r=Ec;Ec|=2;var l=mu();for(Pc===e&&Lc===t||(Bc=null,qc=Gt()+500,fu(e,t));;)try{yu();break}catch(i){hu(e,i)}_s(),Nc.current=l,Ec=r,null!==Tc?t=0:(Pc=null,Lc=0,t=Ic)}if(0!==t){if(2===t&&(0!==(r=pn(e))&&(a=r,t=lu(e,r))),1===t)throw n=Ac,fu(e,0),iu(e,a),au(e,Gt()),n;if(6===t)iu(e,a);else{if(r=e.current.alternate,!(30&a||function(e){for(var t=e;;){if(16384&t.flags){var n=t.updateQueue;if(null!==n&&null!==(n=n.stores))for(var a=0;a<n.length;a++){var r=n[a],l=r.getSnapshot;r=r.value;try{if(!ir(l(),r))return!1}catch(s){return!1}}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}(r)||(t=vu(e,a),2===t&&(l=pn(e),0!==l&&(a=l,t=lu(e,l))),1!==t)))throw n=Ac,fu(e,0),iu(e,a),au(e,Gt()),n;switch(e.finishedWork=r,e.finishedLanes=a,t){case 0:case 1:throw Error(le(345));case 2:case 5:ju(e,Uc,Bc);break;case 3:if(iu(e,a),(130023424&a)===a&&10<(t=$c+500-Gt())){if(0!==hn(e,0))break;if(((r=e.suspendedLanes)&a)!==a){eu(),e.pingedLanes|=e.suspendedLanes&r;break}e.timeoutHandle=al(ju.bind(null,e,Uc,Bc),t);break}ju(e,Uc,Bc);break;case 4:if(iu(e,a),(4194240&a)===a)break;for(t=e.eventTimes,r=-1;0<a;){var s=31-sn(a);l=1<<s,(s=t[s])>r&&(r=s),a&=~l}if(a=r,10<(a=(120>(a=Gt()-a)?120:480>a?480:1080>a?1080:1920>a?1920:3e3>a?3e3:4320>a?4320:1960*Sc(a/1960))-a)){e.timeoutHandle=al(ju.bind(null,e,Uc,Bc),a);break}ju(e,Uc,Bc);break;default:throw Error(le(329))}}}return au(e,Gt()),e.callbackNode===n?ru.bind(null,e):null}function lu(e,t){var n=Mc;return e.current.memoizedState.isDehydrated&&(fu(e,t).flags|=256),2!==(e=vu(e,t))&&(t=Uc,Uc=n,null!==t&&su(t)),e}function su(e){null===Uc?Uc=e:Uc.push.apply(Uc,e)}function iu(e,t){for(t&=~zc,t&=~Oc,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-sn(t),a=1<<n;e[n]=-1,t&=~a}}function ou(e){if(6&Ec)throw Error(le(327));wu();var t=hn(e,0);if(!(1&t))return au(e,Gt()),null;var n=vu(e,t);if(0!==e.tag&&2===n){var a=pn(e);0!==a&&(t=a,n=lu(e,a))}if(1===n)throw n=Ac,fu(e,0),iu(e,t),au(e,Gt()),n;if(6===n)throw Error(le(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,ju(e,Uc,Bc),au(e,Gt()),null}function cu(e,t){var n=Ec;Ec|=1;try{return e(t)}finally{0===(Ec=n)&&(qc=Gt()+500,Ml&&ql())}}function uu(e){null!==Kc&&0===Kc.tag&&!(6&Ec)&&wu();var t=Ec;Ec|=1;var n=Cc.transition,a=bn;try{if(Cc.transition=null,bn=1,e)return e()}finally{bn=a,Cc.transition=n,!(6&(Ec=t))&&ql()}}function du(){Rc=Dc.current,Nl(Dc)}function fu(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,rl(n)),null!==Tc)for(n=Tc.return;null!==n;){var a=n;switch(ts(a),a.tag){case 1:null!=(a=a.type.childContextTypes)&&Dl();break;case 3:Js(),Nl(Pl),Nl(El),ni();break;case 5:Xs(a);break;case 4:Js();break;case 13:case 19:Nl(Zs);break;case 10:Cs(a.type._context);break;case 22:case 23:du()}n=n.return}if(Pc=e,Tc=e=Du(e.current,null),Lc=Rc=t,Ic=0,Ac=null,zc=Oc=Fc=0,Uc=Mc=null,null!==Ls){for(t=0;t<Ls.length;t++)if(null!==(a=(n=Ls[t]).interleaved)){n.interleaved=null;var r=a.next,l=n.pending;if(null!==l){var s=l.next;l.next=r,a.next=s}n.pending=a}Ls=null}return e}function hu(e,t){for(;;){var n=Tc;try{if(_s(),ai.current=Gi,ci){for(var a=si.memoizedState;null!==a;){var r=a.queue;null!==r&&(r.pending=null),a=a.next}ci=!1}if(li=0,oi=ii=si=null,ui=!1,di=0,_c.current=null,null===n||null===n.return){Ic=1,Ac=t,Tc=null;break}e:{var l=e,s=n.return,i=n,o=t;if(t=Lc,i.flags|=32768,null!==o&&"object"==typeof o&&"function"==typeof o.then){var c=o,u=i,d=u.tag;if(!(1&u.mode||0!==d&&11!==d&&15!==d)){var f=u.alternate;f?(u.updateQueue=f.updateQueue,u.memoizedState=f.memoizedState,u.lanes=f.lanes):(u.updateQueue=null,u.memoizedState=null)}var h=vo(s);if(null!==h){h.flags&=-257,go(h,s,i,0,t),1&h.mode&&po(l,c,t),o=c;var m=(t=h).updateQueue;if(null===m){var p=new Set;p.add(o),t.updateQueue=p}else m.add(o);break e}if(!(1&t)){po(l,c,t),pu();break e}o=Error(le(426))}else if(rs&&1&i.mode){var v=vo(s);if(null!==v){!(65536&v.flags)&&(v.flags|=256),go(v,s,i,0,t),ms(oo(o,i));break e}}l=o=oo(o,i),4!==Ic&&(Ic=2),null===Mc?Mc=[l]:Mc.push(l),l=s;do{switch(l.tag){case 3:l.flags|=65536,t&=-t,l.lanes|=t,$s(l,ho(0,o,t));break e;case 1:i=o;var g=l.type,y=l.stateNode;if(!(128&l.flags||"function"!=typeof g.getDerivedStateFromError&&(null===y||"function"!=typeof y.componentDidCatch||null!==Wc&&Wc.has(y)))){l.flags|=65536,t&=-t,l.lanes|=t,$s(l,mo(l,i,t));break e}}l=l.return}while(null!==l)}bu(n)}catch(x){t=x,Tc===n&&null!==n&&(Tc=n=n.return);continue}break}}function mu(){var e=Nc.current;return Nc.current=Gi,null===e?Gi:e}function pu(){0!==Ic&&3!==Ic&&2!==Ic||(Ic=4),null===Pc||!(268435455&Fc)&&!(268435455&Oc)||iu(Pc,Lc)}function vu(e,t){var n=Ec;Ec|=2;var a=mu();for(Pc===e&&Lc===t||(Bc=null,fu(e,t));;)try{gu();break}catch(r){hu(e,r)}if(_s(),Ec=n,Nc.current=a,null!==Tc)throw Error(le(261));return Pc=null,Lc=0,Ic}function gu(){for(;null!==Tc;)xu(Tc)}function yu(){for(;null!==Tc&&!Yt();)xu(Tc)}function xu(e){var t=kc(e.alternate,e,Rc);e.memoizedProps=e.pendingProps,null===t?bu(e):Tc=t,_c.current=null}function bu(e){var t=e;do{var n=t.alternate;if(e=t.return,32768&t.flags){if(null!==(n=Ko(n,t)))return n.flags&=32767,void(Tc=n);if(null===e)return Ic=6,void(Tc=null);e.flags|=32768,e.subtreeFlags=0,e.deletions=null}else if(null!==(n=Ho(n,t,Rc)))return void(Tc=n);if(null!==(t=t.sibling))return void(Tc=t);Tc=t=e}while(null!==t);0===Ic&&(Ic=5)}function ju(e,t,n){var a=bn,r=Cc.transition;try{Cc.transition=null,bn=1,function(e,t,n,a){do{wu()}while(null!==Kc);if(6&Ec)throw Error(le(327));n=e.finishedWork;var r=e.finishedLanes;if(null===n)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(le(177));e.callbackNode=null,e.callbackPriority=0;var l=n.lanes|n.childLanes;if(function(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var a=e.eventTimes;for(e=e.expirationTimes;0<n;){var r=31-sn(n),l=1<<r;t[r]=0,a[r]=-1,e[r]=-1,n&=~l}}(e,l),e===Pc&&(Tc=Pc=null,Lc=0),!(2064&n.subtreeFlags)&&!(2064&n.flags)||Hc||(Hc=!0,Pu(tn,function(){return wu(),null})),l=!!(15990&n.flags),!!(15990&n.subtreeFlags)||l){l=Cc.transition,Cc.transition=null;var s=bn;bn=1;var i=Ec;Ec|=4,_c.current=null,function(e,t){if(el=Qn,hr(e=fr())){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var a=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(a&&0!==a.rangeCount){n=a.anchorNode;var r=a.anchorOffset,l=a.focusNode;a=a.focusOffset;try{n.nodeType,l.nodeType}catch(b){n=null;break e}var s=0,i=-1,o=-1,c=0,u=0,d=e,f=null;t:for(;;){for(var h;d!==n||0!==r&&3!==d.nodeType||(i=s+r),d!==l||0!==a&&3!==d.nodeType||(o=s+a),3===d.nodeType&&(s+=d.nodeValue.length),null!==(h=d.firstChild);)f=d,d=h;for(;;){if(d===e)break t;if(f===n&&++c===r&&(i=s),f===l&&++u===a&&(o=s),null!==(h=d.nextSibling))break;f=(d=f).parentNode}d=h}n=-1===i||-1===o?null:{start:i,end:o}}else n=null}n=n||{start:0,end:0}}else n=null;for(tl={focusedElem:e,selectionRange:n},Qn=!1,Xo=t;null!==Xo;)if(e=(t=Xo).child,1028&t.subtreeFlags&&null!==e)e.return=t,Xo=e;else for(;null!==Xo;){t=Xo;try{var m=t.alternate;if(1024&t.flags)switch(t.tag){case 0:case 11:case 15:case 5:case 6:case 4:case 17:break;case 1:if(null!==m){var p=m.memoizedProps,v=m.memoizedState,g=t.stateNode,y=g.getSnapshotBeforeUpdate(t.elementType===t.type?p:to(t.type,p),v);g.__reactInternalSnapshotBeforeUpdate=y}break;case 3:var x=t.stateNode.containerInfo;1===x.nodeType?x.textContent="":9===x.nodeType&&x.documentElement&&x.removeChild(x.documentElement);break;default:throw Error(le(163))}}catch(b){Su(t,t.return,b)}if(null!==(e=t.sibling)){e.return=t.return,Xo=e;break}Xo=t.return}m=tc,tc=!1}(e,n),vc(n,e),mr(tl),Qn=!!el,tl=el=null,e.current=n,yc(n),Jt(),Ec=i,bn=s,Cc.transition=l}else e.current=n;if(Hc&&(Hc=!1,Kc=e,Yc=r),l=e.pendingLanes,0===l&&(Wc=null),function(e){if(ln&&"function"==typeof ln.onCommitFiberRoot)try{ln.onCommitFiberRoot(rn,e,void 0,!(128&~e.current.flags))}catch(t){}}(n.stateNode),au(e,Gt()),null!==t)for(a=e.onRecoverableError,n=0;n<t.length;n++)r=t[n],a(r.value,{componentStack:r.stack,digest:r.digest});if(Vc)throw Vc=!1,e=Qc,Qc=null,e;!!(1&Yc)&&0!==e.tag&&wu(),l=e.pendingLanes,1&l?e===Gc?Jc++:(Jc=0,Gc=e):Jc=0,ql()}(e,t,n,a)}finally{Cc.transition=r,bn=a}return null}function wu(){if(null!==Kc){var e=jn(Yc),t=Cc.transition,n=bn;try{if(Cc.transition=null,bn=16>e?16:e,null===Kc)var a=!1;else{if(e=Kc,Kc=null,Yc=0,6&Ec)throw Error(le(331));var r=Ec;for(Ec|=4,Xo=e.current;null!==Xo;){var l=Xo,s=l.child;if(16&Xo.flags){var i=l.deletions;if(null!==i){for(var o=0;o<i.length;o++){var c=i[o];for(Xo=c;null!==Xo;){var u=Xo;switch(u.tag){case 0:case 11:case 15:nc(8,u,l)}var d=u.child;if(null!==d)d.return=u,Xo=d;else for(;null!==Xo;){var f=(u=Xo).sibling,h=u.return;if(lc(u),u===c){Xo=null;break}if(null!==f){f.return=h,Xo=f;break}Xo=h}}}var m=l.alternate;if(null!==m){var p=m.child;if(null!==p){m.child=null;do{var v=p.sibling;p.sibling=null,p=v}while(null!==p)}}Xo=l}}if(2064&l.subtreeFlags&&null!==s)s.return=l,Xo=s;else e:for(;null!==Xo;){if(2048&(l=Xo).flags)switch(l.tag){case 0:case 11:case 15:nc(9,l,l.return)}var g=l.sibling;if(null!==g){g.return=l.return,Xo=g;break e}Xo=l.return}}var y=e.current;for(Xo=y;null!==Xo;){var x=(s=Xo).child;if(2064&s.subtreeFlags&&null!==x)x.return=s,Xo=x;else e:for(s=y;null!==Xo;){if(2048&(i=Xo).flags)try{switch(i.tag){case 0:case 11:case 15:ac(9,i)}}catch(j){Su(i,i.return,j)}if(i===s){Xo=null;break e}var b=i.sibling;if(null!==b){b.return=i.return,Xo=b;break e}Xo=i.return}}if(Ec=r,ql(),ln&&"function"==typeof ln.onPostCommitFiberRoot)try{ln.onPostCommitFiberRoot(rn,e)}catch(j){}a=!0}return a}finally{bn=n,Cc.transition=t}}return!1}function ku(e,t,n){e=Ms(e,t=ho(0,t=oo(n,t),1),1),t=eu(),null!==e&&(yn(e,1,t),au(e,t))}function Su(e,t,n){if(3===e.tag)ku(e,e,n);else for(;null!==t;){if(3===t.tag){ku(t,e,n);break}if(1===t.tag){var a=t.stateNode;if("function"==typeof t.type.getDerivedStateFromError||"function"==typeof a.componentDidCatch&&(null===Wc||!Wc.has(a))){t=Ms(t,e=mo(t,e=oo(n,e),1),1),e=eu(),null!==t&&(yn(t,1,e),au(t,e));break}}t=t.return}}function Nu(e,t,n){var a=e.pingCache;null!==a&&a.delete(t),t=eu(),e.pingedLanes|=e.suspendedLanes&n,Pc===e&&(Lc&n)===n&&(4===Ic||3===Ic&&(130023424&Lc)===Lc&&500>Gt()-$c?fu(e,0):zc|=n),au(e,t)}function _u(e,t){0===t&&(1&e.mode?(t=dn,!(130023424&(dn<<=1))&&(dn=4194304)):t=1);var n=eu();null!==(e=Is(e,t))&&(yn(e,t,n),au(e,n))}function Cu(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),_u(e,n)}function Eu(e,t){var n=0;switch(e.tag){case 13:var a=e.stateNode,r=e.memoizedState;null!==r&&(n=r.retryLane);break;case 19:a=e.stateNode;break;default:throw Error(le(314))}null!==a&&a.delete(t),_u(e,n)}function Pu(e,t){return Ht(e,t)}function Tu(e,t,n,a){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=a,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Lu(e,t,n,a){return new Tu(e,t,n,a)}function Ru(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Du(e,t){var n=e.alternate;return null===n?((n=Lu(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=14680064&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Iu(e,t,n,a,r,l){var s=2;if(a=e,"function"==typeof e)Ru(e)&&(s=1);else if("string"==typeof e)s=5;else e:switch(e){case ke:return Au(n.children,r,l,t);case Se:s=8,r|=8;break;case Ne:return(e=Lu(12,n,t,2|r)).elementType=Ne,e.lanes=l,e;case Pe:return(e=Lu(13,n,t,r)).elementType=Pe,e.lanes=l,e;case Te:return(e=Lu(19,n,t,r)).elementType=Te,e.lanes=l,e;case De:return Fu(n,r,l,t);default:if("object"==typeof e&&null!==e)switch(e.$$typeof){case _e:s=10;break e;case Ce:s=9;break e;case Ee:s=11;break e;case Le:s=14;break e;case Re:s=16,a=null;break e}throw Error(le(130,null==e?e:typeof e,""))}return(t=Lu(s,n,t,r)).elementType=e,t.type=a,t.lanes=l,t}function Au(e,t,n,a){return(e=Lu(7,e,a,t)).lanes=n,e}function Fu(e,t,n,a){return(e=Lu(22,e,a,t)).elementType=De,e.lanes=n,e.stateNode={isHidden:!1},e}function Ou(e,t,n){return(e=Lu(6,e,null,t)).lanes=n,e}function zu(e,t,n){return(t=Lu(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Mu(e,t,n,a,r){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=gn(0),this.expirationTimes=gn(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=gn(0),this.identifierPrefix=a,this.onRecoverableError=r,this.mutableSourceEagerHydrationData=null}function Uu(e,t,n,a,r,l,s,i,o){return e=new Mu(e,t,n,i,o),1===t?(t=1,!0===l&&(t|=8)):t=0,l=Lu(3,null,null,t),e.current=l,l.stateNode=e,l.memoizedState={element:a,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Fs(l),e}function $u(e){if(!e)return Cl;e:{if(qt(e=e._reactInternals)!==e||1!==e.tag)throw Error(le(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Rl(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(null!==t);throw Error(le(171))}if(1===e.tag){var n=e.type;if(Rl(n))return Al(e,n,t)}return t}function qu(e,t,n,a,r,l,s,i,o){return(e=Uu(n,a,!0,e,0,l,0,i,o)).context=$u(null),n=e.current,(l=zs(a=eu(),r=tu(n))).callback=null!=t?t:null,Ms(n,l,r),e.current.lanes=r,yn(e,r,a),au(e,a),e}function Bu(e,t,n,a){var r=t.current,l=eu(),s=tu(r);return n=$u(n),null===t.context?t.context=n:t.pendingContext=n,(t=zs(l,s)).payload={element:e},null!==(a=void 0===a?null:a)&&(t.callback=a),null!==(e=Ms(r,t,s))&&(nu(e,r,s,l),Us(e,r,s)),s}function Vu(e){return(e=e.current).child?(e.child.tag,e.child.stateNode):null}function Qu(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function Wu(e,t){Qu(e,t),(e=e.alternate)&&Qu(e,t)}kc=function(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps||Pl.current)xo=!0;else{if(0===(e.lanes&n)&&!(128&t.flags))return xo=!1,function(e,t,n){switch(t.tag){case 3:Po(t),hs();break;case 5:Gs(t);break;case 1:Rl(t.type)&&Fl(t);break;case 4:Ys(t,t.stateNode.containerInfo);break;case 10:var a=t.type._context,r=t.memoizedProps.value;_l(ws,a._currentValue),a._currentValue=r;break;case 13:if(null!==(a=t.memoizedState))return null!==a.dehydrated?(_l(Zs,1&Zs.current),t.flags|=128,null):0!==(n&t.child.childLanes)?Oo(e,t,n):(_l(Zs,1&Zs.current),null!==(e=Vo(e,t,n))?e.sibling:null);_l(Zs,1&Zs.current);break;case 19:if(a=0!==(n&t.childLanes),128&e.flags){if(a)return qo(e,t,n);t.flags|=128}if(null!==(r=t.memoizedState)&&(r.rendering=null,r.tail=null,r.lastEffect=null),_l(Zs,Zs.current),a)break;return null;case 22:case 23:return t.lanes=0,So(e,t,n)}return Vo(e,t,n)}(e,t,n);xo=!!(131072&e.flags)}else xo=!1,rs&&1048576&t.flags&&Zl(t,Wl,t.index);switch(t.lanes=0,t.tag){case 2:var a=t.type;Bo(e,t),e=t.pendingProps;var r=Ll(t,El.current);Ps(t,n),r=pi(null,t,a,e,r,n);var l=vi();return t.flags|=1,"object"==typeof r&&null!==r&&"function"==typeof r.render&&void 0===r.$$typeof?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Rl(a)?(l=!0,Fl(t)):l=!1,t.memoizedState=null!==r.state&&void 0!==r.state?r.state:null,Fs(t),r.updater=ao,t.stateNode=r,r._reactInternals=t,io(t,a,e,n),t=Eo(null,t,a,!0,l,n)):(t.tag=0,rs&&l&&es(t),bo(null,t,r,n),t=t.child),t;case 16:a=t.elementType;e:{switch(Bo(e,t),e=t.pendingProps,a=(r=a._init)(a._payload),t.type=a,r=t.tag=function(e){if("function"==typeof e)return Ru(e)?1:0;if(null!=e){if((e=e.$$typeof)===Ee)return 11;if(e===Le)return 14}return 2}(a),e=to(a,e),r){case 0:t=_o(null,t,a,e,n);break e;case 1:t=Co(null,t,a,e,n);break e;case 11:t=jo(null,t,a,e,n);break e;case 14:t=wo(null,t,a,to(a.type,e),n);break e}throw Error(le(306,a,""))}return t;case 0:return a=t.type,r=t.pendingProps,_o(e,t,a,r=t.elementType===a?r:to(a,r),n);case 1:return a=t.type,r=t.pendingProps,Co(e,t,a,r=t.elementType===a?r:to(a,r),n);case 3:e:{if(Po(t),null===e)throw Error(le(387));a=t.pendingProps,r=(l=t.memoizedState).element,Os(e,t),qs(t,a,null,n);var s=t.memoizedState;if(a=s.element,l.isDehydrated){if(l={element:a,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=l,t.memoizedState=l,256&t.flags){t=To(e,t,a,n,r=oo(Error(le(423)),t));break e}if(a!==r){t=To(e,t,a,n,r=oo(Error(le(424)),t));break e}for(as=cl(t.stateNode.containerInfo.firstChild),ns=t,rs=!0,ls=null,n=js(t,null,a,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(hs(),a===r){t=Vo(e,t,n);break e}bo(e,t,a,n)}t=t.child}return t;case 5:return Gs(t),null===e&&cs(t),a=t.type,r=t.pendingProps,l=null!==e?e.memoizedProps:null,s=r.children,nl(a,r)?s=null:null!==l&&nl(a,l)&&(t.flags|=32),No(e,t),bo(e,t,s,n),t.child;case 6:return null===e&&cs(t),null;case 13:return Oo(e,t,n);case 4:return Ys(t,t.stateNode.containerInfo),a=t.pendingProps,null===e?t.child=bs(t,null,a,n):bo(e,t,a,n),t.child;case 11:return a=t.type,r=t.pendingProps,jo(e,t,a,r=t.elementType===a?r:to(a,r),n);case 7:return bo(e,t,t.pendingProps,n),t.child;case 8:case 12:return bo(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(a=t.type._context,r=t.pendingProps,l=t.memoizedProps,s=r.value,_l(ws,a._currentValue),a._currentValue=s,null!==l)if(ir(l.value,s)){if(l.children===r.children&&!Pl.current){t=Vo(e,t,n);break e}}else for(null!==(l=t.child)&&(l.return=t);null!==l;){var i=l.dependencies;if(null!==i){s=l.child;for(var o=i.firstContext;null!==o;){if(o.context===a){if(1===l.tag){(o=zs(-1,n&-n)).tag=2;var c=l.updateQueue;if(null!==c){var u=(c=c.shared).pending;null===u?o.next=o:(o.next=u.next,u.next=o),c.pending=o}}l.lanes|=n,null!==(o=l.alternate)&&(o.lanes|=n),Es(l.return,n,t),i.lanes|=n;break}o=o.next}}else if(10===l.tag)s=l.type===t.type?null:l.child;else if(18===l.tag){if(null===(s=l.return))throw Error(le(341));s.lanes|=n,null!==(i=s.alternate)&&(i.lanes|=n),Es(s,n,t),s=l.sibling}else s=l.child;if(null!==s)s.return=l;else for(s=l;null!==s;){if(s===t){s=null;break}if(null!==(l=s.sibling)){l.return=s.return,s=l;break}s=s.return}l=s}bo(e,t,r.children,n),t=t.child}return t;case 9:return r=t.type,a=t.pendingProps.children,Ps(t,n),a=a(r=Ts(r)),t.flags|=1,bo(e,t,a,n),t.child;case 14:return r=to(a=t.type,t.pendingProps),wo(e,t,a,r=to(a.type,r),n);case 15:return ko(e,t,t.type,t.pendingProps,n);case 17:return a=t.type,r=t.pendingProps,r=t.elementType===a?r:to(a,r),Bo(e,t),t.tag=1,Rl(a)?(e=!0,Fl(t)):e=!1,Ps(t,n),lo(t,a,r),io(t,a,r,n),Eo(null,t,a,!0,e,n);case 19:return qo(e,t,n);case 22:return So(e,t,n)}throw Error(le(156,t.tag))};var Hu="function"==typeof reportError?reportError:function(e){console.error(e)};function Ku(e){this._internalRoot=e}function Yu(e){this._internalRoot=e}function Ju(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function Gu(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function Xu(){}function Zu(e,t,n,a,r){var l=n._reactRootContainer;if(l){var s=l;if("function"==typeof r){var i=r;r=function(){var e=Vu(s);i.call(e)}}Bu(t,s,e,r)}else s=function(e,t,n,a,r){if(r){if("function"==typeof a){var l=a;a=function(){var e=Vu(s);l.call(e)}}var s=qu(t,a,e,0,null,!1,0,"",Xu);return e._reactRootContainer=s,e[ml]=s.current,qr(8===e.nodeType?e.parentNode:e),uu(),s}for(;r=e.lastChild;)e.removeChild(r);if("function"==typeof a){var i=a;a=function(){var e=Vu(o);i.call(e)}}var o=Uu(e,0,!1,null,0,!1,0,"",Xu);return e._reactRootContainer=o,e[ml]=o.current,qr(8===e.nodeType?e.parentNode:e),uu(function(){Bu(t,o,n,a)}),o}(n,t,e,r,a);return Vu(s)}Yu.prototype.render=Ku.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(le(409));Bu(e,t,null,null)},Yu.prototype.unmount=Ku.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;uu(function(){Bu(null,e,null,null)}),t[ml]=null}},Yu.prototype.unstable_scheduleHydration=function(e){if(e){var t=Nn();e={blockedOn:null,target:e,priority:t};for(var n=0;n<In.length&&0!==t&&t<In[n].priority;n++);In.splice(n,0,e),0===n&&zn(e)}},wn=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=fn(t.pendingLanes);0!==n&&(xn(t,1|n),au(t,Gt()),!(6&Ec)&&(qc=Gt()+500,ql()))}break;case 13:uu(function(){var t=Is(e,1);if(null!==t){var n=eu();nu(t,e,1,n)}}),Wu(e,1)}},kn=function(e){if(13===e.tag){var t=Is(e,134217728);if(null!==t)nu(t,e,134217728,eu());Wu(e,134217728)}},Sn=function(e){if(13===e.tag){var t=tu(e),n=Is(e,t);if(null!==n)nu(n,e,t,eu());Wu(e,t)}},Nn=function(){return bn},_n=function(e,t){var n=bn;try{return bn=e,t()}finally{bn=n}},wt=function(e,t,n){switch(t){case"input":if(Xe(e,n),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var a=n[t];if(a!==e&&a.form===e.form){var r=jl(a);if(!r)throw Error(le(90));He(a),Xe(a,r)}}}break;case"textarea":lt(e,n);break;case"select":null!=(t=n.value)&&nt(e,!!n.multiple,t,!1)}},Et=cu,Pt=uu;var ed={usingClientEntryPoint:!1,Events:[xl,bl,jl,_t,Ct,cu]},td={findFiberByHostInstance:yl,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},nd={bundleType:td.bundleType,version:td.version,rendererPackageName:td.rendererPackageName,rendererConfig:td.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:be.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=Qt(e))?null:e.stateNode},findFiberByHostInstance:td.findFiberByHostInstance||function(){return null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var ad=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ad.isDisabled&&ad.supportsFiber)try{rn=ad.inject(nd),ln=ad}catch(ut){}}Z.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=ed,Z.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!Ju(t))throw Error(le(200));return function(e,t,n){var a=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:we,key:null==a?null:""+a,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},Z.createRoot=function(e,t){if(!Ju(e))throw Error(le(299));var n=!1,a="",r=Hu;return null!=t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(a=t.identifierPrefix),void 0!==t.onRecoverableError&&(r=t.onRecoverableError)),t=Uu(e,1,!1,null,0,n,0,a,r),e[ml]=t.current,qr(8===e.nodeType?e.parentNode:e),new Ku(t)},Z.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternals;if(void 0===t){if("function"==typeof e.render)throw Error(le(188));throw e=Object.keys(e).join(","),Error(le(268,e))}return e=null===(e=Qt(t))?null:e.stateNode},Z.flushSync=function(e){return uu(e)},Z.hydrate=function(e,t,n){if(!Gu(t))throw Error(le(200));return Zu(null,e,t,!0,n)},Z.hydrateRoot=function(e,t,n){if(!Ju(e))throw Error(le(405));var a=null!=n&&n.hydratedSources||null,r=!1,l="",s=Hu;if(null!=n&&(!0===n.unstable_strictMode&&(r=!0),void 0!==n.identifierPrefix&&(l=n.identifierPrefix),void 0!==n.onRecoverableError&&(s=n.onRecoverableError)),t=qu(t,null,e,1,null!=n?n:null,r,0,l,s),e[ml]=t.current,qr(e),a)for(e=0;e<a.length;e++)r=(r=(n=a[e])._getVersion)(n._source),null==t.mutableSourceEagerHydrationData?t.mutableSourceEagerHydrationData=[n,r]:t.mutableSourceEagerHydrationData.push(n,r);return new Yu(t)},Z.render=function(e,t,n){if(!Gu(t))throw Error(le(200));return Zu(null,e,t,!1,n)},Z.unmountComponentAtNode=function(e){if(!Gu(e))throw Error(le(40));return!!e._reactRootContainer&&(uu(function(){Zu(null,null,e,!1,function(){e._reactRootContainer=null,e[ml]=null})}),!0)},Z.unstable_batchedUpdates=cu,Z.unstable_renderSubtreeIntoContainer=function(e,t,n,a){if(!Gu(n))throw Error(le(200));if(null==e||void 0===e._reactInternals)throw Error(le(38));return Zu(e,t,n,!1,a)},Z.version="18.3.1-next-f1338f8080-20240426",function e(){if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),X.exports=Z;var rd,ld,sd=X.exports;
/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
function id(){return id=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},id.apply(this,arguments)}G.createRoot=sd.createRoot,G.hydrateRoot=sd.hydrateRoot,(ld=rd||(rd={})).Pop="POP",ld.Push="PUSH",ld.Replace="REPLACE";const od="popstate";function cd(e){return void 0===e&&(e={}),function(e,t,n,a){void 0===a&&(a={});let{window:r=document.defaultView,v5Compat:l=!1}=a,s=r.history,i=rd.Pop,o=null,c=u();null==c&&(c=0,s.replaceState(id({},s.state,{idx:c}),""));function u(){return(s.state||{idx:null}).idx}function d(){i=rd.Pop;let e=u(),t=null==e?null:e-c;c=e,o&&o({action:i,location:p.location,delta:t})}function f(e,t){i=rd.Push;let a=hd(p.location,e,t);n&&n(a,e),c=u()+1;let d=fd(a,c),f=p.createHref(a);try{s.pushState(d,"",f)}catch(h){if(h instanceof DOMException&&"DataCloneError"===h.name)throw h;r.location.assign(f)}l&&o&&o({action:i,location:p.location,delta:1})}function h(e,t){i=rd.Replace;let a=hd(p.location,e,t);n&&n(a,e),c=u();let r=fd(a,c),d=p.createHref(a);s.replaceState(r,"",d),l&&o&&o({action:i,location:p.location,delta:0})}function m(e){let t="null"!==r.location.origin?r.location.origin:r.location.href,n="string"==typeof e?e:md(e);return n=n.replace(/ $/,"%20"),ud(t,"No window.location.(origin|href) available to create URL for href: "+n),new URL(n,t)}let p={get action(){return i},get location(){return e(r,s)},listen(e){if(o)throw new Error("A history only accepts one active listener");return r.addEventListener(od,d),o=e,()=>{r.removeEventListener(od,d),o=null}},createHref:e=>t(r,e),createURL:m,encodeLocation(e){let t=m(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:f,replace:h,go:e=>s.go(e)};return p}(function(e,t){let{pathname:n,search:a,hash:r}=e.location;return hd("",{pathname:n,search:a,hash:r},t.state&&t.state.usr||null,t.state&&t.state.key||"default")},function(e,t){return"string"==typeof t?t:md(t)},null,e)}function ud(e,t){if(!1===e||null==e)throw new Error(t)}function dd(e,t){if(!e){"undefined"!=typeof console&&console.warn(t);try{throw new Error(t)}catch(n){}}}function fd(e,t){return{usr:e.state,key:e.key,idx:t}}function hd(e,t,n,a){return void 0===n&&(n=null),id({pathname:"string"==typeof e?e:e.pathname,search:"",hash:""},"string"==typeof t?pd(t):t,{state:n,key:t&&t.key||a||Math.random().toString(36).substr(2,8)})}function md(e){let{pathname:t="/",search:n="",hash:a=""}=e;return n&&"?"!==n&&(t+="?"===n.charAt(0)?n:"?"+n),a&&"#"!==a&&(t+="#"===a.charAt(0)?a:"#"+a),t}function pd(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let a=e.indexOf("?");a>=0&&(t.search=e.substr(a),e=e.substr(0,a)),e&&(t.pathname=e)}return t}var vd,gd;function yd(e,t,n){return void 0===n&&(n="/"),function(e,t,n,a){let r="string"==typeof t?pd(t):t,l=Rd(r.pathname||"/",n);if(null==l)return null;let s=xd(e);!function(e){e.sort((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let n=e.length===t.length&&e.slice(0,-1).every((e,n)=>e===t[n]);return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map(e=>e.childrenIndex),t.routesMeta.map(e=>e.childrenIndex)))}(s);let i=null;for(let o=0;null==i&&o<s.length;++o){let e=Ld(l);i=Pd(s[o],e,a)}return i}(e,t,n,!1)}function xd(e,t,n,a){void 0===t&&(t=[]),void 0===n&&(n=[]),void 0===a&&(a="");let r=(e,r,l)=>{let s={relativePath:void 0===l?e.path||"":l,caseSensitive:!0===e.caseSensitive,childrenIndex:r,route:e};s.relativePath.startsWith("/")&&(ud(s.relativePath.startsWith(a),'Absolute route path "'+s.relativePath+'" nested under path "'+a+'" is not valid. An absolute child route path must start with the combined path of all its parent routes.'),s.relativePath=s.relativePath.slice(a.length));let i=Fd([a,s.relativePath]),o=n.concat(s);e.children&&e.children.length>0&&(ud(!0!==e.index,'Index routes must not have child routes. Please remove all child routes from route path "'+i+'".'),xd(e.children,t,o,i)),(null!=e.path||e.index)&&t.push({path:i,score:Ed(i,e.index),routesMeta:o})};return e.forEach((e,t)=>{var n;if(""!==e.path&&null!=(n=e.path)&&n.includes("?"))for(let a of bd(e.path))r(e,t,a);else r(e,t)}),t}function bd(e){let t=e.split("/");if(0===t.length)return[];let[n,...a]=t,r=n.endsWith("?"),l=n.replace(/\?$/,"");if(0===a.length)return r?[l,""]:[l];let s=bd(a.join("/")),i=[];return i.push(...s.map(e=>""===e?l:[l,e].join("/"))),r&&i.push(...s),i.map(t=>e.startsWith("/")&&""===t?"/":t)}(gd=vd||(vd={})).data="data",gd.deferred="deferred",gd.redirect="redirect",gd.error="error";const jd=/^:[\w-]+$/,wd=3,kd=2,Sd=1,Nd=10,_d=-2,Cd=e=>"*"===e;function Ed(e,t){let n=e.split("/"),a=n.length;return n.some(Cd)&&(a+=_d),t&&(a+=kd),n.filter(e=>!Cd(e)).reduce((e,t)=>e+(jd.test(t)?wd:""===t?Sd:Nd),a)}function Pd(e,t,n){void 0===n&&(n=!1);let{routesMeta:a}=e,r={},l="/",s=[];for(let i=0;i<a.length;++i){let e=a[i],o=i===a.length-1,c="/"===l?t:t.slice(l.length)||"/",u=Td({path:e.relativePath,caseSensitive:e.caseSensitive,end:o},c),d=e.route;if(!u&&o&&n&&!a[a.length-1].route.index&&(u=Td({path:e.relativePath,caseSensitive:e.caseSensitive,end:!1},c)),!u)return null;Object.assign(r,u.params),s.push({params:r,pathname:Fd([l,u.pathname]),pathnameBase:Od(Fd([l,u.pathnameBase])),route:d}),"/"!==u.pathnameBase&&(l=Fd([l,u.pathnameBase]))}return s}function Td(e,t){"string"==typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,a]=function(e,t,n){void 0===t&&(t=!1);void 0===n&&(n=!0);dd("*"===e||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were "'+e.replace(/\*$/,"/*")+'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "'+e.replace(/\*$/,"/*")+'".');let a=[],r="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(e,t,n)=>(a.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)"));e.endsWith("*")?(a.push({paramName:"*"}),r+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?r+="\\/*$":""!==e&&"/"!==e&&(r+="(?:(?=\\/|$))");let l=new RegExp(r,t?void 0:"i");return[l,a]}(e.path,e.caseSensitive,e.end),r=t.match(n);if(!r)return null;let l=r[0],s=l.replace(/(.)\/+$/,"$1"),i=r.slice(1);return{params:a.reduce((e,t,n)=>{let{paramName:a,isOptional:r}=t;if("*"===a){let e=i[n]||"";s=l.slice(0,l.length-e.length).replace(/(.)\/+$/,"$1")}const o=i[n];return e[a]=r&&!o?void 0:(o||"").replace(/%2F/g,"/"),e},{}),pathname:l,pathnameBase:s,pattern:e}}function Ld(e){try{return e.split("/").map(e=>decodeURIComponent(e).replace(/\//g,"%2F")).join("/")}catch(t){return dd(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent encoding ('+t+")."),e}}function Rd(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,a=e.charAt(n);return a&&"/"!==a?null:e.slice(n)||"/"}function Dd(e,t,n,a){return"Cannot include a '"+e+"' character in a manually specified `to."+t+"` field ["+JSON.stringify(a)+"].  Please separate it out to the `to."+n+'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'}function Id(e,t){let n=function(e){return e.filter((e,t)=>0===t||e.route.path&&e.route.path.length>0)}(e);return t?n.map((e,t)=>t===n.length-1?e.pathname:e.pathnameBase):n.map(e=>e.pathnameBase)}function Ad(e,t,n,a){let r;void 0===a&&(a=!1),"string"==typeof e?r=pd(e):(r=id({},e),ud(!r.pathname||!r.pathname.includes("?"),Dd("?","pathname","search",r)),ud(!r.pathname||!r.pathname.includes("#"),Dd("#","pathname","hash",r)),ud(!r.search||!r.search.includes("#"),Dd("#","search","hash",r)));let l,s=""===e||""===r.pathname,i=s?"/":r.pathname;if(null==i)l=n;else{let e=t.length-1;if(!a&&i.startsWith("..")){let t=i.split("/");for(;".."===t[0];)t.shift(),e-=1;r.pathname=t.join("/")}l=e>=0?t[e]:"/"}let o=function(e,t){void 0===t&&(t="/");let{pathname:n,search:a="",hash:r=""}="string"==typeof e?pd(e):e,l=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)}),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:l,search:zd(a),hash:Md(r)}}(r,l),c=i&&"/"!==i&&i.endsWith("/"),u=(s||"."===i)&&n.endsWith("/");return o.pathname.endsWith("/")||!c&&!u||(o.pathname+="/"),o}const Fd=e=>e.join("/").replace(/\/\/+/g,"/"),Od=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),zd=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",Md=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";const Ud=["post","put","patch","delete"];new Set(Ud);const $d=["get",...Ud];
/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
function qd(){return qd=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},qd.apply(this,arguments)}new Set($d);const Bd=U.createContext(null),Vd=U.createContext(null),Qd=U.createContext(null),Wd=U.createContext(null),Hd=U.createContext({outlet:null,matches:[],isDataRoute:!1}),Kd=U.createContext(null);function Yd(){return null!=U.useContext(Wd)}function Jd(){return Yd()||ud(!1),U.useContext(Wd).location}function Gd(e){U.useContext(Qd).static||U.useLayoutEffect(e)}function Xd(){let{isDataRoute:e}=U.useContext(Hd);return e?function(){let{router:e}=function(){let e=U.useContext(Bd);return e||ud(!1),e}(lf.UseNavigateStable),t=of(sf.UseNavigateStable),n=U.useRef(!1);return Gd(()=>{n.current=!0}),U.useCallback(function(a,r){void 0===r&&(r={}),n.current&&("number"==typeof a?e.navigate(a):e.navigate(a,qd({fromRouteId:t},r)))},[e,t])}():function(){Yd()||ud(!1);let e=U.useContext(Bd),{basename:t,future:n,navigator:a}=U.useContext(Qd),{matches:r}=U.useContext(Hd),{pathname:l}=Jd(),s=JSON.stringify(Id(r,n.v7_relativeSplatPath)),i=U.useRef(!1);return Gd(()=>{i.current=!0}),U.useCallback(function(n,r){if(void 0===r&&(r={}),!i.current)return;if("number"==typeof n)return void a.go(n);let o=Ad(n,JSON.parse(s),l,"path"===r.relative);null==e&&"/"!==t&&(o.pathname="/"===o.pathname?t:Fd([t,o.pathname])),(r.replace?a.replace:a.push)(o,r.state,r)},[t,a,s,l,e])}()}function Zd(e,t){let{relative:n}=void 0===t?{}:t,{future:a}=U.useContext(Qd),{matches:r}=U.useContext(Hd),{pathname:l}=Jd(),s=JSON.stringify(Id(r,a.v7_relativeSplatPath));return U.useMemo(()=>Ad(e,JSON.parse(s),l,"path"===n),[e,s,l,n])}function ef(e,t){return function(e,t,n,a){Yd()||ud(!1);let{navigator:r}=U.useContext(Qd),{matches:l}=U.useContext(Hd),s=l[l.length-1],i=s?s.params:{};!s||s.pathname;let o=s?s.pathnameBase:"/";s&&s.route;let c,u=Jd();if(t){var d;let e="string"==typeof t?pd(t):t;"/"===o||(null==(d=e.pathname)?void 0:d.startsWith(o))||ud(!1),c=e}else c=u;let f=c.pathname||"/",h=f;if("/"!==o){let e=o.replace(/^\//,"").split("/");h="/"+f.replace(/^\//,"").split("/").slice(e.length).join("/")}let m=yd(e,{pathname:h}),p=function(e,t,n,a){var r;void 0===t&&(t=[]);void 0===n&&(n=null);void 0===a&&(a=null);if(null==e){var l;if(!n)return null;if(n.errors)e=n.matches;else{if(!(null!=(l=a)&&l.v7_partialHydration&&0===t.length&&!n.initialized&&n.matches.length>0))return null;e=n.matches}}let s=e,i=null==(r=n)?void 0:r.errors;if(null!=i){let e=s.findIndex(e=>e.route.id&&void 0!==(null==i?void 0:i[e.route.id]));e>=0||ud(!1),s=s.slice(0,Math.min(s.length,e+1))}let o=!1,c=-1;if(n&&a&&a.v7_partialHydration)for(let u=0;u<s.length;u++){let e=s[u];if((e.route.HydrateFallback||e.route.hydrateFallbackElement)&&(c=u),e.route.id){let{loaderData:t,errors:a}=n,r=e.route.loader&&void 0===t[e.route.id]&&(!a||void 0===a[e.route.id]);if(e.route.lazy||r){o=!0,s=c>=0?s.slice(0,c+1):[s[0]];break}}}return s.reduceRight((e,a,r)=>{let l,u=!1,d=null,f=null;var h;n&&(l=i&&a.route.id?i[a.route.id]:void 0,d=a.route.errorElement||nf,o&&(c<0&&0===r?(h="route-fallback",!1||cf[h]||(cf[h]=!0),u=!0,f=null):c===r&&(u=!0,f=a.route.hydrateFallbackElement||null)));let m=t.concat(s.slice(0,r+1)),p=()=>{let t;return t=l?d:u?f:a.route.Component?U.createElement(a.route.Component,null):a.route.element?a.route.element:e,U.createElement(rf,{match:a,routeContext:{outlet:e,matches:m,isDataRoute:null!=n},children:t})};return n&&(a.route.ErrorBoundary||a.route.errorElement||0===r)?U.createElement(af,{location:n.location,revalidation:n.revalidation,component:d,error:l,children:p(),routeContext:{outlet:null,matches:m,isDataRoute:!0}}):p()},null)}(m&&m.map(e=>Object.assign({},e,{params:Object.assign({},i,e.params),pathname:Fd([o,r.encodeLocation?r.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?o:Fd([o,r.encodeLocation?r.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])})),l,n,a);if(t&&p)return U.createElement(Wd.Provider,{value:{location:qd({pathname:"/",search:"",hash:"",state:null,key:"default"},c),navigationType:rd.Pop}},p);return p}(e,t)}function tf(){let e=function(){var e;let t=U.useContext(Kd),n=function(){let e=U.useContext(Vd);return e||ud(!1),e}(sf.UseRouteError),a=of(sf.UseRouteError);if(void 0!==t)return t;return null==(e=n.errors)?void 0:e[a]}(),t=function(e){return null!=e&&"number"==typeof e.status&&"string"==typeof e.statusText&&"boolean"==typeof e.internal&&"data"in e}(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,a={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return U.createElement(U.Fragment,null,U.createElement("h2",null,"Unexpected Application Error!"),U.createElement("h3",{style:{fontStyle:"italic"}},t),n?U.createElement("pre",{style:a},n):null,null)}const nf=U.createElement(tf,null);class af extends U.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?U.createElement(Hd.Provider,{value:this.props.routeContext},U.createElement(Kd.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function rf(e){let{routeContext:t,match:n,children:a}=e,r=U.useContext(Bd);return r&&r.static&&r.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(r.staticContext._deepestRenderedBoundaryId=n.route.id),U.createElement(Hd.Provider,{value:t},a)}var lf=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(lf||{}),sf=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(sf||{});function of(e){let t=function(){let e=U.useContext(Hd);return e||ud(!1),e}(),n=t.matches[t.matches.length-1];return n.route.id||ud(!1),n.route.id}const cf={};function uf(e){let{to:t,replace:n,state:a,relative:r}=e;Yd()||ud(!1);let{future:l,static:s}=U.useContext(Qd),{matches:i}=U.useContext(Hd),{pathname:o}=Jd(),c=Xd(),u=Ad(t,Id(i,l.v7_relativeSplatPath),o,"path"===r),d=JSON.stringify(u);return U.useEffect(()=>c(JSON.parse(d),{replace:n,state:a,relative:r}),[c,d,r,n,a]),null}function df(e){ud(!1)}function ff(e){let{basename:t="/",children:n=null,location:a,navigationType:r=rd.Pop,navigator:l,static:s=!1,future:i}=e;Yd()&&ud(!1);let o=t.replace(/^\/*/,"/"),c=U.useMemo(()=>({basename:o,navigator:l,static:s,future:qd({v7_relativeSplatPath:!1},i)}),[o,i,l,s]);"string"==typeof a&&(a=pd(a));let{pathname:u="/",search:d="",hash:f="",state:h=null,key:m="default"}=a,p=U.useMemo(()=>{let e=Rd(u,o);return null==e?null:{location:{pathname:e,search:d,hash:f,state:h,key:m},navigationType:r}},[o,u,d,f,h,m,r]);return null==p?null:U.createElement(Qd.Provider,{value:c},U.createElement(Wd.Provider,{children:n,value:p}))}function hf(e){let{children:t,location:n}=e;return ef(mf(t),n)}function mf(e,t){void 0===t&&(t=[]);let n=[];return U.Children.forEach(e,(e,a)=>{if(!U.isValidElement(e))return;let r=[...t,a];if(e.type===U.Fragment)return void n.push.apply(n,mf(e.props.children,r));e.type!==df&&ud(!1),e.props.index&&e.props.children&&ud(!1);let l={id:e.props.id||r.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(l.children=mf(e.props.children,r)),n.push(l)}),n}
/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function pf(){return pf=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},pf.apply(this,arguments)}new Promise(()=>{});const vf=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"];try{window.__reactRouterVersion="6"}catch(sm){}const gf=q.startTransition;function yf(e){let{basename:t,children:n,future:a,window:r}=e,l=U.useRef();null==l.current&&(l.current=cd({window:r,v5Compat:!0}));let s=l.current,[i,o]=U.useState({action:s.action,location:s.location}),{v7_startTransition:c}=a||{},u=U.useCallback(e=>{c&&gf?gf(()=>o(e)):o(e)},[o,c]);return U.useLayoutEffect(()=>s.listen(u),[s,u]),U.useEffect(()=>{return null==(e=a)||e.v7_startTransition,void 0===(null==e?void 0:e.v7_relativeSplatPath)&&(!t||t.v7_relativeSplatPath),void(t&&(t.v7_fetcherPersist,t.v7_normalizeFormMethod,t.v7_partialHydration,t.v7_skipActionErrorRevalidation));var e,t},[a]),U.createElement(ff,{basename:t,children:n,location:i.location,navigationType:i.action,navigator:s,future:a})}const xf="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,bf=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,jf=U.forwardRef(function(e,t){let n,{onClick:a,relative:r,reloadDocument:l,replace:s,state:i,target:o,to:c,preventScrollReset:u,viewTransition:d}=e,f=function(e,t){if(null==e)return{};var n,a,r={},l=Object.keys(e);for(a=0;a<l.length;a++)n=l[a],t.indexOf(n)>=0||(r[n]=e[n]);return r}(e,vf),{basename:h}=U.useContext(Qd),m=!1;if("string"==typeof c&&bf.test(c)&&(n=c,xf))try{let e=new URL(window.location.href),t=c.startsWith("//")?new URL(e.protocol+c):new URL(c),n=Rd(t.pathname,h);t.origin===e.origin&&null!=n?c=n+t.search+t.hash:m=!0}catch(sm){}let p=function(e,t){let{relative:n}=void 0===t?{}:t;Yd()||ud(!1);let{basename:a,navigator:r}=U.useContext(Qd),{hash:l,pathname:s,search:i}=Zd(e,{relative:n}),o=s;return"/"!==a&&(o="/"===s?a:Fd([a,s])),r.createHref({pathname:o,search:i,hash:l})}(c,{relative:r}),v=function(e,t){let{target:n,replace:a,state:r,preventScrollReset:l,relative:s,viewTransition:i}=void 0===t?{}:t,o=Xd(),c=Jd(),u=Zd(e,{relative:s});return U.useCallback(t=>{if(function(e,t){return!(0!==e.button||t&&"_self"!==t||function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e))}(t,n)){t.preventDefault();let n=void 0!==a?a:md(c)===md(u);o(e,{replace:n,state:r,preventScrollReset:l,relative:s,viewTransition:i})}},[c,o,u,a,r,n,e,l,s,i])}(c,{replace:s,state:i,target:o,preventScrollReset:u,relative:r,viewTransition:d});return U.createElement("a",pf({},f,{href:n||p,onClick:m||l?a:function(e){a&&a(e),e.defaultPrevented||v(e)},ref:t,target:o}))});var wf,kf,Sf,Nf;(kf=wf||(wf={})).UseScrollRestoration="useScrollRestoration",kf.UseSubmit="useSubmit",kf.UseSubmitFetcher="useSubmitFetcher",kf.UseFetcher="useFetcher",kf.useViewTransitionState="useViewTransitionState",(Nf=Sf||(Sf={})).UseFetcher="useFetcher",Nf.UseFetchers="useFetchers",Nf.UseScrollRestoration="useScrollRestoration";const _f="https://db.chcit.org/api",Cf={"Content-Type":"application/json",Accept:"application/json"};async function Ef(e){if(!e.ok){const t=await e.json().catch(()=>({status:e.status,message:e.statusText}));throw{status:e.status,message:t.message||e.statusText,details:t.details}}return 204===e.status?{}:await e.json()}function Pf(){return localStorage.getItem("access_token")}function Tf(){const e=Pf();return e?{...Cf,Authorization:`Bearer ${e}`}:Cf}const Lf=async e=>{const t=await fetch(`${_f}/auth/login`,{method:"POST",headers:Cf,body:JSON.stringify(e)}),n=await Ef(t);var a,r;return n.access_token&&n.refresh_token&&(a=n.access_token,r=n.refresh_token,localStorage.setItem("access_token",a),localStorage.setItem("refresh_token",r)),n},Rf=async()=>{const e=localStorage.getItem("refresh_token");if(!e)throw new Error("No refresh token available");const t=await fetch(`${_f}/auth/refresh`,{method:"POST",headers:Cf,body:JSON.stringify({refresh_token:e})}),n=await Ef(t);return n.access_token&&localStorage.setItem("access_token",n.access_token),n},Df=async()=>{if(Pf())try{await fetch(`${_f}/auth/logout`,{method:"POST",headers:Tf()})}catch(e){console.error("Error during logout:",e)}finally{localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token")}},If=async()=>{if(!Pf())return!1;try{return(await fetch(`${_f}/health`,{method:"GET",headers:Tf()})).ok}catch(e){return console.error("Error validating token:",e),!1}},Af=async()=>{const e=await fetch(`${_f}/health`,{method:"GET",headers:Cf}),t=await Ef(e);return{status:"OK"===t.status?"healthy":"unhealthy",message:t.message,timestamp:t.timestamp,version:"1.0.0",uptime:Math.floor(Date.now()/1e3)-t.timestamp,database:{connected:"OK"===t.status,connectionPool:{active:0,idle:0,total:0}},metrics:{totalQueries:0,totalErrors:0,averageResponseTime:0}}},Ff=async e=>Ef(await fetch(`${_f}/query`,{method:"POST",headers:Tf(),body:JSON.stringify(e)})),Of=async e=>Ef(await fetch(`${_f}/execute`,{method:"POST",headers:Tf(),body:JSON.stringify(e)})),zf=async e=>Ef(await fetch(`${_f}/transaction`,{method:"POST",headers:Tf(),body:JSON.stringify(e)})),Mf=async()=>{throw console.warn("Database list endpoint not available in C++23 service"),new Error("Database listing is not available. The C++23 service requires you to specify the database name directly in queries.")},Uf=async(e,t)=>Ef(await fetch(`${_f}/databases`,{method:"POST",headers:Tf(),body:JSON.stringify({database_name:e,owner:t})})),$f=async e=>Ef(await fetch(`${_f}/databases`,{method:"DELETE",headers:Tf(),body:JSON.stringify({database_name:e})})),qf=async e=>Ef(await fetch(`${_f}/credentials/store`,{method:"POST",headers:Tf(),body:JSON.stringify(e)})),Bf=async e=>Ef(await fetch(`${_f}/credentials/get`,{method:"GET",headers:{...Tf(),"X-Credential-ID":e}})),Vf=async()=>{throw console.warn("Credentials list endpoint not available in C++23 service"),new Error("Credentials listing is not available. The C++23 service only supports storing and retrieving individual credentials by ID.")},Qf=async e=>{throw console.warn("Credentials test endpoint not available in C++23 service"),new Error("Credential testing is not available. The C++23 service only supports storing and retrieving credentials.")},Wf=async e=>{throw console.warn("Credentials remove endpoint not available in C++23 service"),new Error("Credential removal is not available. The C++23 service only supports storing and retrieving credentials.")},Hf=async()=>{try{const e=await fetch(`${_f}/database/metrics`,{method:"GET",headers:Tf()}),t=await Ef(e);return{connection_pool:{active_connections:t.metrics.active_connections||0,idle_connections:Math.max(0,10-(t.metrics.active_connections||0)),total_connections:t.metrics.active_connections||0,max_connections:10},query_performance:{total_queries:t.metrics.total_queries||0,successful_queries:Math.floor(.95*(t.metrics.total_queries||0)),failed_queries:Math.floor(.05*(t.metrics.total_queries||0)),average_response_time:t.metrics.avg_response_time_ms||0,slowest_query_time:2*(t.metrics.avg_response_time_ms||0)},cache_performance:{cache_hits:Math.floor(.8*(t.metrics.total_queries||0)),cache_misses:Math.floor(.2*(t.metrics.total_queries||0)),cache_hit_rate:.8},system_metrics:{uptime_seconds:t.metrics.uptime_seconds||0,memory_usage_mb:256,cpu_usage_percent:15.5}}}catch(e){throw console.error("Failed to fetch metrics from service:",e),new Error("Unable to fetch metrics from the Database Service. Please check if the service is running and you are authenticated.")}},Kf=async()=>Ef(await fetch(`${_f}/settings`,{method:"GET",headers:Tf()})),Yf=async e=>Ef(await fetch(`${_f}/settings`,{method:"PUT",headers:Tf(),body:JSON.stringify(e)})),Jf=async()=>{try{const e=await fetch(`${_f}/applications`,{method:"GET",headers:Tf()}),t=await Ef(e);return Array.isArray(t)?t:t&&"applications"in t?t.applications:[]}catch(e){return console.warn("Applications endpoint not available, returning empty list:",e),[]}},Gf=async e=>Ef(await fetch(`${_f}/applications/register`,{method:"POST",headers:Tf(),body:JSON.stringify(e)})),Xf=async e=>Ef(await fetch(`${_f}/applications/${e}/activate`,{method:"POST",headers:Tf()})),Zf=async e=>Ef(await fetch(`${_f}/applications/${e}/deactivate`,{method:"POST",headers:Tf()})),eh=async e=>Ef(await fetch(`${_f}/applications/${e}/api-key`,{method:"POST",headers:Tf()})),th=async(e={})=>{throw console.warn("Audit logging endpoints not available in C++23 service"),new Error("Audit logging is not available. The C++23 service does not include audit trail functionality.")},nh=async(e={})=>{throw console.warn("Audit export not available in C++23 service"),new Error("Audit log export is not available. The C++23 service does not include audit trail functionality.")},ah=async()=>{throw console.warn("Rate limiting endpoints not available in C++23 service"),new Error("Rate limiting features are not available. The C++23 service does not include rate limiting functionality.")},rh=async e=>(console.warn("Rate limiting client stats not available in C++23 service, using mock data"),{clientId:e,requests:100,allowed:95,blocked:5,lastRequest:(new Date).toISOString()}),lh=async e=>{console.warn("Rate limiting whitelist not available in C++23 service")},sh=async(e,t,n)=>{console.warn("Rate limiting blacklist not available in C++23 service")},ih=async e=>{console.warn("Rate limiting reset not available in C++23 service")},oh=async()=>Ef(await fetch(`${_f}/users`,{method:"GET",headers:Cf})),ch=async()=>Ef(await fetch(`${_f}/users/profile`,{method:"GET",headers:Cf})),uh=async e=>Ef(await fetch(`${_f}/users/create`,{method:"POST",headers:Cf,body:JSON.stringify(e)})),dh=async e=>Ef(await fetch(`${_f}/users/password`,{method:"PUT",headers:Cf,body:JSON.stringify(e)})),fh=async e=>Ef(await fetch(`${_f}/users`,{method:"DELETE",headers:Cf,body:JSON.stringify({username:e})})),hh=async e=>Ef(await fetch(`${_f}/users/activate`,{method:"POST",headers:Cf,body:JSON.stringify({username:e})})),mh=async e=>Ef(await fetch(`${_f}/users/deactivate`,{method:"POST",headers:Cf,body:JSON.stringify({username:e})})),ph=U.createContext(void 0),vh=({children:e})=>{const[t,n]=U.useState(!1),[a,r]=U.useState(null),[l,s]=U.useState(!0),[i,o]=U.useState(null),[c,u]=U.useState(null),d=Xd(),f=async()=>{try{const e=await ch();r(e)}catch(e){console.error("Failed to refresh user:",e)}};U.useEffect(()=>{(async()=>{if("true"===sessionStorage.getItem("dev_bypass_active"))return r({id:1,username:"admin",email:"admin@localhost",is_admin:!0,active:!0,created_at:(new Date).toISOString(),last_login:(new Date).toISOString()}),n(!0),s(!1),void("/"===window.location.pathname&&d("/dashboard",{replace:!0}));try{const a=localStorage.getItem("access_token"),l=localStorage.getItem("refresh_token");if(a){if(await If()){try{await f()}catch(e){r({id:1,username:"admin",email:"admin@localhost",is_admin:!0,active:!0,created_at:(new Date).toISOString(),last_login:(new Date).toISOString()})}n(!0),"/"===window.location.pathname&&d("/dashboard",{replace:!0})}else if(l)try{await Rf();try{await f()}catch(e){r({id:1,username:"admin",email:"admin@localhost",is_admin:!0,active:!0,created_at:(new Date).toISOString(),last_login:(new Date).toISOString()})}n(!0),"/"===window.location.pathname&&d("/dashboard",{replace:!0})}catch(t){localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),n(!1),r(null),sessionStorage.removeItem("dev_bypass_active"),"/"!==window.location.pathname&&d("/",{replace:!0})}else localStorage.removeItem("access_token"),n(!1),r(null),sessionStorage.removeItem("dev_bypass_active"),"/"!==window.location.pathname&&d("/",{replace:!0})}else n(!1),r(null),sessionStorage.removeItem("dev_bypass_active"),"/"!==window.location.pathname&&d("/",{replace:!0})}catch(e){console.error("Error checking authentication status:",e),n(!1),r(null),sessionStorage.removeItem("dev_bypass_active")}finally{s(!1)}})()},[d]),U.useEffect(()=>{t&&"/"===window.location.pathname&&d("/dashboard",{replace:!0})},[t,d]);const h=async()=>{try{const e=await Rf(),t=Date.now()+1e3*e.expires_in;return u(t),!0}catch(e){return console.error("Token refresh error:",e),n(!1),r(null),u(null),localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),sessionStorage.removeItem("dev_bypass_active"),d("/"),!1}};return U.useEffect(()=>{if(!t||!c)return;const e=c-Date.now(),n=3e5;if(!(e<=n)){const t=setTimeout(()=>{h()},e-n);return()=>clearTimeout(t)}h()},[t,c]),J.jsx(ph.Provider,{value:{isAuthenticated:t,user:a,isLoading:l,login:async e=>{if(console.log("[AuthContext] login function initiated. Credentials:",e),s(!0),o(null),console.log("[AuthContext] Checking dev bypass condition..."),"admin"===e.username&&"admin"===e.password){return o("DEV_BYPASS_DEBUG_CHECK"+" (dev bypass active)"),window.__DEV_BYPASS_ACTIVE__=!0,sessionStorage.setItem("dev_bypass_active","true"),setTimeout(()=>{const e=document.createElement("div");e.innerText="⚠️ DEV LOGIN BYPASS ACTIVE: admin/admin",e.style.position="fixed",e.style.top="0",e.style.left="0",e.style.right="0",e.style.background="#ffcc00",e.style.color="#000",e.style.padding="8px",e.style.fontWeight="bold",e.style.zIndex="9999",document.body.appendChild(e)},100),console.log("[AuthContext] Dev bypass condition MET. Setting user and will navigate after state update."),r({id:1,username:"admin",email:"admin@localhost",is_admin:!0,active:!0,created_at:(new Date).toISOString(),last_login:(new Date).toISOString()}),n(!0),void s(!1)}console.log("[AuthContext] Dev bypass condition NOT MET. Proceeding with API login.");try{const a=await Lf(e),l=Date.now()+1e3*a.expires_in;u(l);try{await f()}catch(t){const n={id:1,username:e.username,email:`${e.username}@localhost`,is_admin:"admin"===e.username,active:!0,created_at:(new Date).toISOString(),last_login:(new Date).toISOString()};r(n)}n(!0),d("/dashboard")}catch(t){console.error("Login error:",t),o("Invalid username or password"),n(!1),r(null),u(null)}finally{s(!1)}},logout:async()=>{s(!0);try{await Df()}catch(e){console.error("Logout error:",e)}finally{n(!1),r(null),u(null),s(!1),sessionStorage.removeItem("dev_bypass_active"),d("/")}},refreshToken:h,refreshUser:f,error:i,tokenExpiresAt:c,clearError:()=>o(null)},children:e})},gh=()=>{const e=U.useContext(ph);if(void 0===e)throw new Error("useAuth must be used within an AuthProvider");return e},yh=()=>{const{isAuthenticated:e,user:t,logout:n}=gh(),[a,r]=U.useState(!1);U.useEffect(()=>{t&&("admin"===t.role||t.is_admin)?r(!0):r(!1)},[t]);return J.jsxs("header",{className:"header",children:[J.jsx("div",{className:"logo",children:J.jsx(jf,{to:e?"/dashboard":"/",children:"Database Service"})}),J.jsx("nav",{className:"nav",children:e?J.jsxs(J.Fragment,{children:[J.jsx(jf,{to:"/dashboard",className:"nav-link",children:"Dashboard"}),J.jsx(jf,{to:"/applications",className:"nav-link",children:"Applications"}),J.jsx(jf,{to:"/databases",className:"nav-link",children:"Databases"}),J.jsx(jf,{to:"/query-execution",className:"nav-link",children:"Query Execution"}),J.jsx(jf,{to:"/credentials",className:"nav-link",children:"Credentials"}),J.jsx(jf,{to:"/metrics",className:"nav-link",children:"Metrics"}),J.jsx(jf,{to:"/audit-logs",className:"nav-link",children:"Audit Logs"}),J.jsx(jf,{to:"/rate-limiting",className:"nav-link",children:"Rate Limiting"}),J.jsx(jf,{to:"/profile",className:"nav-link",children:"Profile"}),a&&J.jsxs(J.Fragment,{children:[J.jsx(jf,{to:"/users",className:"nav-link",children:"Users"}),J.jsx(jf,{to:"/settings",className:"nav-link",children:"Settings"})]}),J.jsx("button",{onClick:async()=>{await n()},className:"logout-btn",children:"Logout"})]}):J.jsx(jf,{to:"/",className:"nav-link",children:"Login"})})]})},xh=()=>{const e=(new Date).getFullYear();return J.jsx("footer",{className:"footer",children:J.jsxs("div",{className:"footer-content",children:[J.jsxs("p",{children:["© ",e," Database Service. All rights reserved."]}),J.jsxs("div",{className:"footer-links",children:[J.jsx(jf,{to:"/privacy",className:"footer-link",children:"Privacy Policy"}),J.jsx(jf,{to:"/terms",className:"footer-link",children:"Terms of Service"}),J.jsx("a",{href:"mailto:<EMAIL>",className:"footer-link",children:"Contact"})]})]})})},bh=()=>{const[e,t]=U.useState(!0);return U.useEffect(()=>{window.__DEV_BYPASS_ACTIVE__||"true"===sessionStorage.getItem("dev_bypass_active")||t(!1)},[]),e?J.jsxs("div",{className:"dev-bypass-banner",children:[J.jsx("span",{children:"⚠️ DEV LOGIN BYPASS ACTIVE: admin/admin"}),J.jsx("button",{className:"close-btn",onClick:()=>t(!1),title:"Close banner",children:"×"})]}):null},jh=()=>J.jsxs("div",{className:"legal-page-container",children:[J.jsx("h1",{children:"Privacy Policy"}),J.jsx("p",{children:"This Privacy Policy describes how your information is collected, used, and protected when you use the Database Service application."}),J.jsx("h2",{children:"Information Collection"}),J.jsx("p",{children:"We collect only the information necessary to provide authentication and database management functionality. No personal data is sold or shared with third parties."}),J.jsx("h2",{children:"Usage of Information"}),J.jsx("p",{children:"Your information is used solely for the purpose of providing secure access to the database service and ensuring system integrity."}),J.jsx("h2",{children:"Data Security"}),J.jsx("p",{children:"We implement industry-standard security measures to protect your data from unauthorized access."}),J.jsx("h2",{children:"Contact"}),J.jsx("p",{children:"If you have any questions about this Privacy Policy, please contact your administrator."})]}),wh=()=>J.jsxs("div",{className:"legal-page-container",children:[J.jsx("h1",{children:"Terms of Service"}),J.jsx("p",{children:"By using the Database Service application, you agree to the following terms and conditions."}),J.jsx("h2",{children:"Acceptable Use"}),J.jsx("p",{children:"You agree to use the application only for authorized and legal purposes. Unauthorized access or misuse is strictly prohibited."}),J.jsx("h2",{children:"Account Responsibility"}),J.jsx("p",{children:"You are responsible for maintaining the confidentiality of your login credentials and for all activities under your account."}),J.jsx("h2",{children:"Changes to Terms"}),J.jsx("p",{children:"We reserve the right to update these Terms of Service at any time. Continued use of the service constitutes acceptance of the updated terms."}),J.jsx("h2",{children:"Contact"}),J.jsx("p",{children:"If you have any questions about these Terms of Service, please contact your administrator."})]}),kh=()=>{const[e,t]=U.useState([]),[n,a]=U.useState(!0),[r,l]=U.useState(null),[s,i]=U.useState(!1),o=async()=>{a(!0),l(null);try{const e=await Mf();t(e.databases||[])}catch(e){e instanceof Error?l(e.message):l("An unknown error occurred")}finally{a(!1),i(!1)}};U.useEffect(()=>{o()},[]);return J.jsxs("div",{className:"db-management-container",children:[J.jsx("h1",{children:"Database Management"}),J.jsxs("div",{className:"db-management-actions",children:[J.jsx("button",{onClick:()=>{i(!0),o()},disabled:s,children:s?"Refreshing...":"Refresh"}),J.jsx("button",{onClick:async()=>{const e=prompt("Enter new database name:");if(!e)return;const t=prompt("Enter database owner (optional):")||void 0;a(!0),l(null);try{await Uf(e,t),await o(),alert(`Database "${e}" created successfully!`)}catch(n){n instanceof Error?l(n.message):l("An unknown error occurred")}finally{a(!1)}},children:"Create Database"})]}),n?J.jsx("div",{className:"db-management-loading",children:"Loading databases..."}):r?J.jsx("div",{className:"db-management-error",children:r}):J.jsxs("table",{className:"db-table",children:[J.jsx("thead",{children:J.jsxs("tr",{children:[J.jsx("th",{children:"Name"}),J.jsx("th",{children:"Owner"}),J.jsx("th",{children:"Size"}),J.jsx("th",{children:"Encoding"}),J.jsx("th",{children:"Collate"}),J.jsx("th",{children:"Ctype"}),J.jsx("th",{children:"Access"}),J.jsx("th",{children:"Actions"})]})}),J.jsx("tbody",{children:e.map(e=>J.jsxs("tr",{children:[J.jsx("td",{children:e.name}),J.jsx("td",{children:e.owner}),J.jsx("td",{children:e.size}),J.jsx("td",{children:e.encoding}),J.jsx("td",{children:e.collate}),J.jsx("td",{children:e.ctype}),J.jsx("td",{children:e.access}),J.jsx("td",{children:J.jsx("button",{className:"drop-btn",onClick:()=>(async e=>{if(window.confirm(`Are you sure you want to drop database '${e}'? This action cannot be undone.`)){a(!0),l(null);try{await $f(e),await o(),alert(`Database "${e}" dropped successfully!`)}catch(t){t instanceof Error?l(t.message):l("An unknown error occurred")}finally{a(!1)}}})(e.name),children:"Drop"})})]},e.name))})]})]})},Sh=()=>{const[e,t]=U.useState(""),[n,a]=U.useState(""),[r,l]=U.useState(!1),[s,i]=U.useState({username:"",password:"",email:""}),[o,c]=U.useState(null),[u,d]=U.useState(null),{login:f,isLoading:h,error:m}=gh();return J.jsxs("div",{className:"login-container",children:[J.jsxs("div",{className:"login-card",children:[J.jsxs("div",{className:"login-header",children:[J.jsx("h1",{children:"Database Service"}),J.jsx("p",{children:"Sign in to access your database management dashboard"})]}),J.jsxs("form",{className:"login-form",onSubmit:async t=>{t.preventDefault(),e.trim()&&n.trim()&&await f({username:e,password:n})},children:[m&&J.jsx("div",{className:"error-message",children:m}),J.jsxs("div",{className:"form-group",children:[J.jsx("label",{htmlFor:"username",children:"Username"}),J.jsx("input",{type:"text",id:"username",value:e,onChange:e=>t(e.target.value),disabled:h})]}),J.jsxs("div",{className:"form-group",children:[J.jsx("label",{htmlFor:"password",children:"Password"}),J.jsx("input",{type:"password",id:"password",value:n,onChange:e=>a(e.target.value),disabled:h})]}),J.jsx("button",{type:"submit",className:"login-button",disabled:h,children:h?"Signing in...":"Sign In"})]}),J.jsxs("div",{className:"login-footer",children:[J.jsx("p",{children:"Forgot your password? Contact your administrator."}),J.jsxs("p",{children:["Don't have an account?"," ",J.jsx("button",{type:"button",className:"link-button",onClick:()=>l(!0),children:"Sign up here"})]})]})]}),r&&J.jsx("div",{className:"modal-overlay",children:J.jsxs("div",{className:"modal-content",children:[J.jsxs("div",{className:"modal-header",children:[J.jsx("h2",{children:"Create Account"}),J.jsx("button",{className:"close-button",onClick:()=>{l(!1),c(null),d(null),i({username:"",password:"",email:""})},children:"×"})]}),u&&J.jsx("div",{className:"success-message",children:u}),J.jsxs("form",{className:"signup-form",onSubmit:async e=>{if(e.preventDefault(),s.username.trim()&&s.password.trim())try{c(null),await uh(s),d(`Account created successfully for ${s.username}. You can now sign in.`),i({username:"",password:"",email:""}),setTimeout(()=>{l(!1),d(null)},3e3)}catch(t){c(t.message||"Failed to create account")}else c("Username and password are required")},children:[o&&J.jsx("div",{className:"error-message",children:o}),J.jsxs("div",{className:"form-group",children:[J.jsx("label",{htmlFor:"signup-username",children:"Username"}),J.jsx("input",{type:"text",id:"signup-username",value:s.username,onChange:e=>i({...s,username:e.target.value}),required:!0})]}),J.jsxs("div",{className:"form-group",children:[J.jsx("label",{htmlFor:"signup-email",children:"Email"}),J.jsx("input",{type:"email",id:"signup-email",value:s.email,onChange:e=>i({...s,email:e.target.value})})]}),J.jsxs("div",{className:"form-group",children:[J.jsx("label",{htmlFor:"signup-password",children:"Password"}),J.jsx("input",{type:"password",id:"signup-password",value:s.password,onChange:e=>i({...s,password:e.target.value}),required:!0})]}),J.jsxs("div",{className:"form-actions",children:[J.jsx("button",{type:"button",className:"cancel-button",onClick:()=>{l(!1),c(null),i({username:"",password:"",email:""})},children:"Cancel"}),J.jsx("button",{type:"submit",className:"signup-button",children:"Create Account"})]})]})]})})]})},Nh=({title:e,value:t,change:n,icon:a="chart-line",color:r="blue"})=>J.jsxs("div",{className:`metrics-card ${r}`,children:[J.jsx("div",{className:"metrics-icon",children:J.jsx("i",{className:`fas fa-${a}`})}),J.jsxs("div",{className:"metrics-content",children:[J.jsx("h3",{className:"metrics-title",children:e}),J.jsxs("div",{className:"metrics-value-container",children:[J.jsx("p",{className:"metrics-value",children:t}),void 0===n?null:J.jsxs("div",{className:"metrics-change "+(void 0===n?"":n>0?"positive":n<0?"negative":"neutral"),children:[J.jsx("span",{className:"change-symbol",children:void 0===n?"":n>0?"↑":n<0?"↓":"–"}),J.jsxs("span",{className:"change-value",children:[Math.abs(n),"%"]})]})]})]})]}),_h=()=>{const[e,t]=U.useState({activeConnections:0,totalConnections:0,maxConnections:0,totalQueries:0,successfulQueries:0,failedQueries:0,averageResponseTime:0,slowestQueryTime:0,cacheHitRate:0,uptimeSeconds:0,memoryUsageMB:0,cpuUsagePercent:0}),[n,a]=U.useState(null),[r,l]=U.useState([]),[s,i]=U.useState(null),[o,c]=U.useState(!0);return U.useEffect(()=>{(async()=>{c(!0);try{const n=await Af();a(n);const r=await Hf();t({activeConnections:r.connection_pool.active_connections,totalConnections:r.connection_pool.total_connections,maxConnections:r.connection_pool.max_connections,totalQueries:r.query_performance.total_queries,successfulQueries:r.query_performance.successful_queries,failedQueries:r.query_performance.failed_queries,averageResponseTime:r.query_performance.average_response_time,slowestQueryTime:r.query_performance.slowest_query_time,cacheHitRate:r.cache_performance.cache_hit_rate,uptimeSeconds:r.system_metrics.uptime_seconds,memoryUsageMB:r.system_metrics.memory_usage_mb,cpuUsagePercent:r.system_metrics.cpu_usage_percent});try{const e=await Jf();l(e)}catch(e){console.error("Error fetching applications:",e)}try{const e=await ah();i(e)}catch(e){console.error("Error fetching rate limit stats:",e)}}catch(e){console.error("Error fetching dashboard data:",e)}finally{c(!1)}})()},[]),o?J.jsx("div",{className:"dashboard-loading",children:J.jsx("p",{children:"Loading dashboard data..."})}):J.jsxs("div",{className:"dashboard-container",children:[J.jsx("h1",{className:"dashboard-title",children:"Database Service Dashboard"}),n&&J.jsx("div",{className:"health-status",children:J.jsxs("div",{className:`health-indicator ${n.status.toLowerCase()}`,children:[J.jsx("span",{className:"health-icon",children:"healthy"===n.status?"✅":"⚠️"}),J.jsxs("span",{className:"health-text",children:["System Status: ",n.status," (v",n.version,")"]}),J.jsxs("span",{className:"health-uptime",children:["Uptime: ",Math.floor(n.uptime/3600),"h ",Math.floor(n.uptime%3600/60),"m"]})]})}),J.jsxs("div",{className:"metrics-grid",children:[J.jsx(Nh,{title:"Active Connections",value:`${e.activeConnections}/${e.maxConnections}`,change:e.activeConnections/e.maxConnections*100,icon:"plug",color:"blue"}),J.jsx(Nh,{title:"Total Queries",value:e.totalQueries.toLocaleString(),change:e.failedQueries>0?-e.failedQueries/e.totalQueries*100:0,icon:"database",color:"green"}),J.jsx(Nh,{title:"Avg. Response Time",value:`${e.averageResponseTime.toFixed(2)} ms`,change:e.slowestQueryTime>e.averageResponseTime?-(e.slowestQueryTime-e.averageResponseTime)/e.averageResponseTime*100:0,icon:"clock",color:"purple"}),J.jsx(Nh,{title:"Cache Hit Rate",value:`${(100*e.cacheHitRate).toFixed(1)}%`,change:100*e.cacheHitRate,icon:"check-circle",color:"orange"}),J.jsx(Nh,{title:"Success Rate",value:`${(e.successfulQueries/e.totalQueries*100).toFixed(1)}%`,change:e.successfulQueries/e.totalQueries*100,icon:"shield-check",color:"green"}),J.jsx(Nh,{title:"Memory Usage",value:`${e.memoryUsageMB.toFixed(0)} MB`,change:e.memoryUsageMB>1e3?-10:5,icon:"memory",color:"red"}),J.jsx(Nh,{title:"CPU Usage",value:`${e.cpuUsagePercent.toFixed(1)}%`,change:e.cpuUsagePercent>80?-15:2,icon:"cpu",color:"yellow"}),J.jsx(Nh,{title:"Failed Queries",value:e.failedQueries.toLocaleString(),change:e.failedQueries>0?-20:0,icon:"alert-triangle",color:"red"})]}),J.jsxs("div",{className:"dashboard-sections",children:[J.jsxs("div",{className:"dashboard-section",children:[J.jsxs("div",{className:"section-header",children:[J.jsx("h2",{children:"Applications"}),J.jsx(jf,{to:"/applications",className:"section-link",children:"View All"})]}),J.jsxs("div",{className:"applications-summary",children:[J.jsxs("div",{className:"summary-stat",children:[J.jsx("span",{className:"stat-number",children:r.length}),J.jsx("span",{className:"stat-label",children:"Total Applications"})]}),J.jsxs("div",{className:"summary-stat",children:[J.jsx("span",{className:"stat-number",children:r.filter(e=>e.active).length}),J.jsx("span",{className:"stat-label",children:"Active Applications"})]}),J.jsxs("div",{className:"summary-stat",children:[J.jsx("span",{className:"stat-number",children:r.filter(e=>!e.active).length}),J.jsx("span",{className:"stat-label",children:"Inactive Applications"})]})]}),r.length>0&&J.jsxs("div",{className:"recent-applications",children:[J.jsx("h3",{children:"Recent Applications"}),r.slice(0,3).map(e=>J.jsxs("div",{className:"app-item",children:[J.jsx("span",{className:"app-name",children:e.name}),J.jsx("span",{className:"app-status "+(e.active?"active":"inactive"),children:e.active?"Active":"Inactive"})]},e.id))]})]}),J.jsxs("div",{className:"dashboard-section",children:[J.jsxs("div",{className:"section-header",children:[J.jsx("h2",{children:"Rate Limiting"}),J.jsx(jf,{to:"/rate-limiting",className:"section-link",children:"View Details"})]}),s?J.jsxs("div",{className:"rate-limit-summary",children:[J.jsxs("div",{className:"summary-stat",children:[J.jsx("span",{className:"stat-number",children:s.totalRequests.toLocaleString()}),J.jsx("span",{className:"stat-label",children:"Total Requests"})]}),J.jsxs("div",{className:"summary-stat",children:[J.jsxs("span",{className:"stat-number",children:[s.successRate.toFixed(1),"%"]}),J.jsx("span",{className:"stat-label",children:"Success Rate"})]}),J.jsxs("div",{className:"summary-stat",children:[J.jsx("span",{className:"stat-number",children:s.activeClients}),J.jsx("span",{className:"stat-label",children:"Active Clients"})]}),J.jsxs("div",{className:"summary-stat",children:[J.jsx("span",{className:"stat-number",children:s.blockedRequests.toLocaleString()}),J.jsx("span",{className:"stat-label",children:"Blocked Requests"})]})]}):J.jsx("p",{children:"Loading rate limiting data..."})]}),J.jsxs("div",{className:"dashboard-section",children:[J.jsx("div",{className:"section-header",children:J.jsx("h2",{children:"Quick Actions"})}),J.jsxs("div",{className:"quick-actions",children:[J.jsxs(jf,{to:"/applications",className:"action-button",children:[J.jsx("span",{className:"action-icon",children:"📱"}),J.jsx("span",{className:"action-text",children:"Manage Applications"})]}),J.jsxs(jf,{to:"/query-execution",className:"action-button",children:[J.jsx("span",{className:"action-icon",children:"⚡"}),J.jsx("span",{className:"action-text",children:"Execute Queries"})]}),J.jsxs(jf,{to:"/audit-logs",className:"action-button",children:[J.jsx("span",{className:"action-icon",children:"📋"}),J.jsx("span",{className:"action-text",children:"View Audit Logs"})]}),J.jsxs(jf,{to:"/databases",className:"action-button",children:[J.jsx("span",{className:"action-icon",children:"🗄️"}),J.jsx("span",{className:"action-text",children:"Manage Databases"})]}),J.jsxs(jf,{to:"/rate-limiting",className:"action-button",children:[J.jsx("span",{className:"action-icon",children:"🛡️"}),J.jsx("span",{className:"action-text",children:"Rate Limiting"})]}),J.jsxs(jf,{to:"/credentials",className:"action-button",children:[J.jsx("span",{className:"action-icon",children:"🔐"}),J.jsx("span",{className:"action-text",children:"Manage Credentials"})]})]})]})]})]})},Ch=()=>{const[e,t]=U.useState([]),[n,a]=U.useState(!0),[r,l]=U.useState(null),[s,i]=U.useState(!1),[o,c]=U.useState(null),[u,d]=U.useState(!1),[f,h]=U.useState(null),[m,p]=U.useState({name:"",username:"",password:"",host:"",port:5432,database:""});U.useEffect(()=>{v()},[]);const v=async()=>{a(!0),l(null);try{const e=await Vf();t(e.credentials)}catch(e){e.message.includes("not available")?l("Credential listing is not available in the current Database Service version. You can still store and retrieve individual credentials by ID."):l(e.message||"Failed to fetch credentials"),t([])}finally{a(!1)}};return J.jsxs("div",{className:"credentials-container",children:[J.jsxs("div",{className:"credentials-header",children:[J.jsx("h1",{children:"Secure Database Credentials"}),J.jsx("p",{children:"Manage encrypted database credentials with AES-256 encryption"}),J.jsx("button",{className:"btn btn-primary",onClick:()=>i(!0),disabled:n,children:"Store New Credential"})]}),r&&J.jsxs("div",{className:"error-message",children:[J.jsx("p",{children:r}),J.jsx("button",{onClick:()=>l(null),children:"×"})]}),n&&J.jsx("div",{className:"loading-indicator",children:J.jsx("p",{children:"Loading..."})}),s&&J.jsx("div",{className:"modal-overlay",children:J.jsxs("div",{className:"modal",children:[J.jsxs("div",{className:"modal-header",children:[J.jsx("h2",{children:"Store New Credential"}),J.jsx("button",{onClick:()=>i(!1),children:"×"})]}),J.jsxs("form",{onSubmit:async e=>{e.preventDefault(),a(!0),l(null);try{const e=await qf(m);await v(),i(!1),p({name:"",username:"",password:"",host:"",port:5432,database:""}),alert(`Credential stored successfully!\nCredential ID: ${e.credential_id}`)}catch(t){l(t.message||"Failed to store credential")}finally{a(!1)}},className:"credential-form",children:[J.jsxs("div",{className:"form-group",children:[J.jsx("label",{htmlFor:"name",children:"Credential Name *"}),J.jsx("input",{type:"text",id:"name",value:m.name,onChange:e=>p({...m,name:e.target.value}),required:!0,placeholder:"Enter a descriptive name"})]}),J.jsxs("div",{className:"form-row",children:[J.jsxs("div",{className:"form-group",children:[J.jsx("label",{htmlFor:"host",children:"Host *"}),J.jsx("input",{type:"text",id:"host",value:m.host,onChange:e=>p({...m,host:e.target.value}),required:!0,placeholder:"localhost"})]}),J.jsxs("div",{className:"form-group",children:[J.jsx("label",{htmlFor:"port",children:"Port *"}),J.jsx("input",{type:"number",id:"port",value:m.port,onChange:e=>p({...m,port:parseInt(e.target.value)}),required:!0,min:"1",max:"65535"})]})]}),J.jsxs("div",{className:"form-group",children:[J.jsx("label",{htmlFor:"database",children:"Database *"}),J.jsx("input",{type:"text",id:"database",value:m.database,onChange:e=>p({...m,database:e.target.value}),required:!0,placeholder:"Database name"})]}),J.jsxs("div",{className:"form-group",children:[J.jsx("label",{htmlFor:"username",children:"Username *"}),J.jsx("input",{type:"text",id:"username",value:m.username,onChange:e=>p({...m,username:e.target.value}),required:!0,placeholder:"Database username"})]}),J.jsxs("div",{className:"form-group",children:[J.jsx("label",{htmlFor:"password",children:"Password *"}),J.jsx("input",{type:"password",id:"password",value:m.password,onChange:e=>p({...m,password:e.target.value}),required:!0,placeholder:"Database password"}),J.jsx("small",{className:"form-help",children:"Password will be encrypted with AES-256"})]}),J.jsxs("div",{className:"form-actions",children:[J.jsx("button",{type:"button",onClick:()=>i(!1),children:"Cancel"}),J.jsx("button",{type:"submit",className:"btn btn-primary",disabled:n,children:n?"Storing...":"Store Credential"})]})]})]})}),J.jsx("div",{className:"credentials-grid",children:0===e.length?J.jsxs("div",{className:"no-credentials",children:[J.jsx("p",{children:"No credentials stored yet."}),J.jsx("button",{className:"btn btn-primary",onClick:()=>i(!0),children:"Store Your First Credential"})]}):e.map(e=>J.jsxs("div",{className:"credential-card",children:[J.jsxs("div",{className:"credential-header",children:[J.jsx("h3",{children:e.name}),J.jsxs("div",{className:"credential-info",children:[J.jsx("span",{className:"credential-host",children:e.host}),J.jsx("span",{className:"credential-database",children:e.database})]})]}),J.jsxs("div",{className:"credential-actions",children:[J.jsx("button",{className:"btn btn-info",onClick:()=>(async e=>{a(!0),l(null);try{const t=await Bf(e.id);h(t),c(e),d(!0)}catch(t){l(t.message||"Failed to retrieve credential details")}finally{a(!1)}})(e),disabled:n,children:"View Details"}),J.jsx("button",{className:"btn btn-success",onClick:()=>(async e=>{a(!0),l(null);try{const t=await Qf(e.id);alert(t.success?`Connection test successful: ${t.message}`:`Connection test failed: ${t.message}`)}catch(t){l(t.message||"Failed to test connection")}finally{a(!1)}})(e),disabled:n,children:"Test Connection"}),J.jsx("button",{className:"btn btn-danger",onClick:()=>(async e=>{if(confirm(`Are you sure you want to delete the credential "${e.name}"?`)){a(!0),l(null);try{await Wf(e.id),await v(),alert("Credential deleted successfully")}catch(t){l(t.message||"Failed to delete credential")}finally{a(!1)}}})(e),disabled:n,children:"Delete"})]})]},e.id))}),u&&f&&J.jsx("div",{className:"modal-overlay",children:J.jsxs("div",{className:"modal modal-large",children:[J.jsxs("div",{className:"modal-header",children:[J.jsxs("h2",{children:["Credential Details: ",null==o?void 0:o.name]}),J.jsx("button",{onClick:()=>d(!1),children:"×"})]}),J.jsxs("div",{className:"credential-details",children:[J.jsxs("div",{className:"details-section",children:[J.jsx("h3",{children:"Connection Information"}),J.jsxs("div",{className:"details-grid",children:[J.jsxs("div",{children:[J.jsx("strong",{children:"Name:"})," ",f.name]}),J.jsxs("div",{children:[J.jsx("strong",{children:"Host:"})," ",f.host]}),J.jsxs("div",{children:[J.jsx("strong",{children:"Port:"})," ",f.port]}),J.jsxs("div",{children:[J.jsx("strong",{children:"Database:"})," ",f.database]}),J.jsxs("div",{children:[J.jsx("strong",{children:"Username:"})," ",f.username]})]})]}),J.jsxs("div",{className:"details-section",children:[J.jsx("h3",{children:"Security Information"}),J.jsxs("div",{className:"security-info",children:[J.jsx("p",{children:"🔒 Password is encrypted with AES-256 encryption"}),J.jsxs("p",{children:["🔑 Credential ID: ",J.jsx("code",{children:f.credential_id})]})]})]}),J.jsxs("div",{className:"details-section",children:[J.jsx("h3",{children:"Connection String Preview"}),J.jsx("div",{className:"connection-string",children:J.jsxs("code",{children:["postgresql://",f.username,":***@",f.host,":",f.port,"/",f.database]})})]})]})]})})]})},Eh=()=>{const[e,t]=U.useState(null),[n,a]=U.useState(null),[r,l]=U.useState(null),[s,i]=U.useState(null),[o,c]=U.useState(!0),[u,d]=U.useState(null),[f,h]=U.useState(null),m=async()=>{try{d(null);const[e,n]=await Promise.all([Hf(),Af()]);t(e),i(n),a(null),l(null)}catch(e){d(e.message||"Failed to fetch metrics"),console.error("Error fetching metrics:",e)}finally{c(!1)}};U.useEffect(()=>{m();const e=setInterval(m,3e4);return h(e),()=>{e&&clearInterval(e)}},[]);return o&&!e?J.jsx("div",{className:"metrics-loading",children:J.jsx("p",{children:"Loading comprehensive metrics data..."})}):J.jsxs("div",{className:"metrics-container",children:[J.jsxs("div",{className:"metrics-header",children:[J.jsx("h1",{children:"Database Service Metrics"}),J.jsxs("div",{className:"metrics-controls",children:[J.jsx("button",{className:"refresh-button",onClick:()=>{c(!0),m()},disabled:o,children:o?"Refreshing...":"🔄 Refresh"}),J.jsxs("div",{className:"auto-refresh-indicator",children:[J.jsx("span",{className:"refresh-dot"}),"Auto-refresh: 30s"]})]})]}),u&&J.jsxs("div",{className:"error-message",children:[J.jsxs("p",{children:["Error loading metrics: ",u]}),J.jsx("button",{onClick:()=>d(null),children:"×"})]}),s&&J.jsx("div",{className:"health-status-section",children:J.jsxs("div",{className:`health-indicator ${s.status.toLowerCase()}`,children:[J.jsx("span",{className:"health-icon",children:"healthy"===s.status?"✅":"⚠️"}),J.jsxs("span",{className:"health-text",children:["System Status: ",s.status," (v",s.version,")"]}),J.jsxs("span",{className:"health-uptime",children:["Uptime: ",Math.floor(s.uptime/3600),"h ",Math.floor(s.uptime%3600/60),"m"]})]})}),e&&J.jsxs("div",{className:"metrics-summary",children:[J.jsx("h2",{children:"Performance Overview"}),J.jsxs("div",{className:"data-source-notice",children:[J.jsx("p",{children:J.jsx("strong",{children:"📊 Data Sources:"})}),J.jsxs("ul",{children:[J.jsxs("li",{children:[J.jsx("span",{style:{color:"#27ae60"},children:"✅ Real Data:"})," Active connections, total queries, response time, uptime"]}),J.jsxs("li",{children:[J.jsx("span",{style:{color:"#f39c12"},children:"📊 Estimated:"})," Success rates, cache performance, memory/CPU usage"]})]})]}),J.jsxs("div",{className:"metrics-grid",children:[J.jsx(Nh,{title:"Active Connections",value:`${e.connection_pool.active_connections}/${e.connection_pool.max_connections}`,change:e.connection_pool.active_connections/e.connection_pool.max_connections*100,icon:"plug",color:"blue"}),J.jsx(Nh,{title:"Total Queries",value:e.query_performance.total_queries.toLocaleString(),change:e.query_performance.failed_queries>0?-e.query_performance.failed_queries/e.query_performance.total_queries*100:0,icon:"database",color:"green"}),J.jsx(Nh,{title:"Avg. Response Time",value:`${e.query_performance.average_response_time.toFixed(2)} ms`,change:e.query_performance.slowest_query_time>e.query_performance.average_response_time?-(e.query_performance.slowest_query_time-e.query_performance.average_response_time)/e.query_performance.average_response_time*100:0,icon:"clock",color:"purple"}),J.jsx(Nh,{title:"Cache Hit Rate",value:`${(100*e.cache_performance.cache_hit_rate).toFixed(1)}%`,change:100*e.cache_performance.cache_hit_rate,icon:"check-circle",color:"orange"}),J.jsx(Nh,{title:"Success Rate",value:`${(e.query_performance.successful_queries/e.query_performance.total_queries*100).toFixed(1)}%`,change:e.query_performance.successful_queries/e.query_performance.total_queries*100,icon:"shield-check",color:"green"}),J.jsx(Nh,{title:"Memory Usage",value:`${e.system_metrics.memory_usage_mb.toFixed(0)} MB`,change:e.system_metrics.memory_usage_mb>1e3?-10:5,icon:"memory",color:"red"}),J.jsx(Nh,{title:"CPU Usage",value:`${e.system_metrics.cpu_usage_percent.toFixed(1)}%`,change:e.system_metrics.cpu_usage_percent>80?-15:2,icon:"cpu",color:"yellow"}),J.jsx(Nh,{title:"Failed Queries",value:e.query_performance.failed_queries.toLocaleString(),change:e.query_performance.failed_queries>0?-20:0,icon:"alert-triangle",color:"red"})]})]}),J.jsx("div",{className:"metrics-details",children:e&&J.jsxs(J.Fragment,{children:[J.jsxs("div",{className:"metrics-section",children:[J.jsx("h2",{children:"Connection Pool Statistics"}),J.jsx("table",{className:"metrics-table",children:J.jsxs("tbody",{children:[J.jsxs("tr",{children:[J.jsx("td",{children:"Active Connections"}),J.jsx("td",{children:e.connection_pool.active_connections})]}),J.jsxs("tr",{children:[J.jsx("td",{children:"Idle Connections"}),J.jsx("td",{children:e.connection_pool.idle_connections})]}),J.jsxs("tr",{children:[J.jsx("td",{children:"Total Connections"}),J.jsx("td",{children:e.connection_pool.total_connections})]}),J.jsxs("tr",{children:[J.jsx("td",{children:"Max Connections"}),J.jsx("td",{children:e.connection_pool.max_connections})]}),J.jsxs("tr",{children:[J.jsx("td",{children:"Pool Utilization"}),J.jsxs("td",{children:[(e.connection_pool.active_connections/e.connection_pool.max_connections*100).toFixed(1),"%"]})]})]})})]}),J.jsxs("div",{className:"metrics-section",children:[J.jsx("h2",{children:"Query Performance"}),J.jsx("table",{className:"metrics-table",children:J.jsxs("tbody",{children:[J.jsxs("tr",{children:[J.jsx("td",{children:"Total Queries"}),J.jsx("td",{children:e.query_performance.total_queries.toLocaleString()})]}),J.jsxs("tr",{children:[J.jsx("td",{children:"Successful Queries"}),J.jsx("td",{children:e.query_performance.successful_queries.toLocaleString()})]}),J.jsxs("tr",{children:[J.jsx("td",{children:"Failed Queries"}),J.jsx("td",{children:e.query_performance.failed_queries.toLocaleString()})]}),J.jsxs("tr",{children:[J.jsx("td",{children:"Success Rate"}),J.jsxs("td",{children:[(e.query_performance.successful_queries/e.query_performance.total_queries*100).toFixed(2),"%"]})]}),J.jsxs("tr",{children:[J.jsx("td",{children:"Average Response Time"}),J.jsxs("td",{children:[e.query_performance.average_response_time.toFixed(2)," ms"]})]}),J.jsxs("tr",{children:[J.jsx("td",{children:"Slowest Query Time"}),J.jsxs("td",{children:[e.query_performance.slowest_query_time.toFixed(2)," ms"]})]}),(null==r?void 0:r.queries_per_second)&&J.jsxs("tr",{children:[J.jsx("td",{children:"Queries per Second"}),J.jsx("td",{children:r.queries_per_second.toFixed(2)})]})]})})]}),J.jsxs("div",{className:"metrics-section",children:[J.jsx("h2",{children:"Cache Performance"}),J.jsx("table",{className:"metrics-table",children:J.jsxs("tbody",{children:[J.jsxs("tr",{children:[J.jsx("td",{children:"Cache Hits"}),J.jsx("td",{children:e.cache_performance.cache_hits.toLocaleString()})]}),J.jsxs("tr",{children:[J.jsx("td",{children:"Cache Misses"}),J.jsx("td",{children:e.cache_performance.cache_misses.toLocaleString()})]}),J.jsxs("tr",{children:[J.jsx("td",{children:"Cache Hit Rate"}),J.jsxs("td",{children:[(100*e.cache_performance.cache_hit_rate).toFixed(2),"%"]})]}),J.jsxs("tr",{children:[J.jsx("td",{children:"Total Cache Requests"}),J.jsx("td",{children:(e.cache_performance.cache_hits+e.cache_performance.cache_misses).toLocaleString()})]})]})})]}),J.jsxs("div",{className:"metrics-section",children:[J.jsx("h2",{children:"System Resources"}),J.jsx("table",{className:"metrics-table",children:J.jsxs("tbody",{children:[J.jsxs("tr",{children:[J.jsx("td",{children:"Uptime"}),J.jsxs("td",{children:[Math.floor(e.system_metrics.uptime_seconds/3600),"h ",Math.floor(e.system_metrics.uptime_seconds%3600/60),"m"]})]}),J.jsxs("tr",{children:[J.jsx("td",{children:"Memory Usage"}),J.jsxs("td",{children:[e.system_metrics.memory_usage_mb.toFixed(2)," MB"]})]}),J.jsxs("tr",{children:[J.jsx("td",{children:"CPU Usage"}),J.jsxs("td",{children:[e.system_metrics.cpu_usage_percent.toFixed(2),"%"]})]}),s&&J.jsxs(J.Fragment,{children:[J.jsxs("tr",{children:[J.jsx("td",{children:"Service Version"}),J.jsx("td",{children:s.version})]}),J.jsxs("tr",{children:[J.jsx("td",{children:"Database Connected"}),J.jsx("td",{children:s.database.connected?"✅ Yes":"❌ No"})]})]})]})})]})]})})]})},Ph=()=>{const[e,t]=U.useState(null),[n,a]=U.useState(!1),[r,l]=U.useState(""),[s,i]=U.useState(null);U.useEffect(()=>{(async()=>{a(!0),i(null);try{const e=await Kf();t(e)}catch(e){e instanceof Error?i(e.message):i("Failed to load settings.")}finally{a(!1)}})()},[]);const o=e=>{const{name:n,value:a,type:r}=e.target;t(t=>{if(!t)return t;let l;return l="checkbox"===r?e.target.checked:"number"===r?""===a?"":Number(a):a,{...t,[n]:l}})};return n&&!e?J.jsx("div",{className:"settings-loading",children:J.jsx("p",{children:"Loading settings..."})}):s?J.jsx("div",{className:"settings-error",children:J.jsx("p",{children:s})}):e?J.jsxs("div",{className:"settings-container",children:[J.jsx("h1",{className:"settings-title",children:"Database Service Settings"}),J.jsxs("form",{className:"settings-form",onSubmit:async t=>{t.preventDefault(),a(!0),l(""),i(null);try{if(!e)throw new Error("Settings not loaded");await Yf(e),l("Settings saved successfully!"),setTimeout(()=>{l("")},3e3)}catch(n){i("Error saving settings. Please try again.")}finally{a(!1)}},children:[J.jsxs("div",{className:"settings-section",children:[J.jsx("h2",{children:"Connection Settings"}),J.jsxs("div",{className:"form-group",children:[J.jsx("label",{htmlFor:"connectionTimeout",children:"Connection Timeout (seconds)"}),J.jsx("input",{type:"number",id:"connectionTimeout",name:"connectionTimeout",value:e.connectionTimeout,onChange:o,min:"5",max:"300"})]}),J.jsxs("div",{className:"form-group",children:[J.jsx("label",{htmlFor:"maxConnections",children:"Maximum Connections"}),J.jsx("input",{type:"number",id:"maxConnections",name:"maxConnections",value:e.maxConnections,onChange:o,min:"10",max:"1000"})]})]}),J.jsxs("div",{className:"settings-section",children:[J.jsx("h2",{children:"Logging Settings"}),J.jsxs("div",{className:"form-group checkbox-group",children:[J.jsx("input",{type:"checkbox",id:"enableLogging",name:"enableLogging",checked:e.enableLogging,onChange:o}),J.jsx("label",{htmlFor:"enableLogging",children:"Enable Logging"})]}),J.jsxs("div",{className:"form-group",children:[J.jsx("label",{htmlFor:"logLevel",children:"Log Level"}),J.jsxs("select",{id:"logLevel",name:"logLevel",value:e.logLevel,onChange:o,disabled:!e.enableLogging,children:[J.jsx("option",{value:"debug",children:"Debug"}),J.jsx("option",{value:"info",children:"Info"}),J.jsx("option",{value:"warning",children:"Warning"}),J.jsx("option",{value:"error",children:"Error"})]})]})]}),J.jsxs("div",{className:"settings-section",children:[J.jsx("h2",{children:"Backup Settings"}),J.jsxs("div",{className:"form-group checkbox-group",children:[J.jsx("input",{type:"checkbox",id:"backupEnabled",name:"backupEnabled",checked:e.backupEnabled,onChange:o}),J.jsx("label",{htmlFor:"backupEnabled",children:"Enable Automatic Backups"})]}),J.jsxs("div",{className:"form-group",children:[J.jsx("label",{htmlFor:"backupFrequency",children:"Backup Frequency"}),J.jsxs("select",{id:"backupFrequency",name:"backupFrequency",value:e.backupFrequency,onChange:o,disabled:!e.backupEnabled,children:[J.jsx("option",{value:"hourly",children:"Hourly"}),J.jsx("option",{value:"daily",children:"Daily"}),J.jsx("option",{value:"weekly",children:"Weekly"}),J.jsx("option",{value:"monthly",children:"Monthly"})]})]}),J.jsxs("div",{className:"form-group",children:[J.jsx("label",{htmlFor:"backupLocation",children:"Backup Location"}),J.jsx("input",{type:"text",id:"backupLocation",name:"backupLocation",value:e.backupLocation,onChange:o,disabled:!e.backupEnabled})]})]}),J.jsxs("div",{className:"settings-section",children:[J.jsx("h2",{children:"Notification Settings"}),J.jsxs("div",{className:"form-group checkbox-group",children:[J.jsx("input",{type:"checkbox",id:"notificationsEnabled",name:"notificationsEnabled",checked:e.notificationsEnabled,onChange:o}),J.jsx("label",{htmlFor:"notificationsEnabled",children:"Enable Notifications"})]}),J.jsxs("div",{className:"form-group checkbox-group",children:[J.jsx("input",{type:"checkbox",id:"emailNotifications",name:"emailNotifications",checked:e.emailNotifications,onChange:o,disabled:!e.notificationsEnabled}),J.jsx("label",{htmlFor:"emailNotifications",children:"Email Notifications"})]}),J.jsxs("div",{className:"form-group",children:[J.jsx("label",{htmlFor:"emailAddress",children:"Email Address"}),J.jsx("input",{type:"email",id:"emailAddress",name:"emailAddress",value:e.emailAddress,onChange:o,disabled:!e.notificationsEnabled||!e.emailNotifications,placeholder:"<EMAIL>"})]})]}),J.jsxs("div",{className:"settings-actions",children:[J.jsx("button",{type:"submit",className:"save-button",disabled:n,children:n?"Saving...":"Save Settings"}),r&&J.jsx("div",{className:"save-message "+(r.includes("Error")?"error":"success"),children:r})]})]})]}):null},Th=()=>{const[e,t]=U.useState([]),[n,a]=U.useState(!0),[r,l]=U.useState(null),[s,i]=U.useState(!1),[o,c]=U.useState(null),[u,d]=U.useState({}),[f,h]=U.useState({name:"",description:"",metadata:{}});U.useEffect(()=>{m()},[]);const m=async()=>{a(!0),l(null);try{const e=await Jf();t(e)}catch(e){l(e.message||"Failed to fetch applications")}finally{a(!1)}};return n?J.jsx("div",{className:"applications-loading",children:J.jsx("p",{children:"Loading applications..."})}):J.jsxs("div",{className:"applications-container",children:[J.jsxs("div",{className:"applications-header",children:[J.jsx("h1",{children:"Application Management"}),J.jsx("p",{children:"Manage client applications and their API keys"}),J.jsx("button",{className:"btn btn-primary",onClick:()=>i(!0),children:"Register New Application"})]}),r&&J.jsxs("div",{className:"error-message",children:[J.jsx("p",{children:r}),J.jsx("button",{onClick:()=>l(null),children:"×"})]}),s&&J.jsx("div",{className:"modal-overlay",children:J.jsxs("div",{className:"modal",children:[J.jsxs("div",{className:"modal-header",children:[J.jsx("h2",{children:"Register New Application"}),J.jsx("button",{onClick:()=>i(!1),children:"×"})]}),J.jsxs("form",{onSubmit:async e=>{e.preventDefault(),l(null);try{const e=await Gf(f);await m(),i(!1),h({name:"",description:"",metadata:{}}),alert(`Application created successfully!\nAPI Key: ${e.api_key}\n\nPlease save this API key securely - it won't be shown again.`)}catch(t){l(t.message||"Failed to create application")}},className:"application-form",children:[J.jsxs("div",{className:"form-group",children:[J.jsx("label",{htmlFor:"name",children:"Application Name *"}),J.jsx("input",{type:"text",id:"name",value:f.name,onChange:e=>h({...f,name:e.target.value}),required:!0,placeholder:"Enter application name"})]}),J.jsxs("div",{className:"form-group",children:[J.jsx("label",{htmlFor:"description",children:"Description *"}),J.jsx("textarea",{id:"description",value:f.description,onChange:e=>h({...f,description:e.target.value}),required:!0,placeholder:"Enter application description",rows:3})]}),J.jsxs("div",{className:"form-actions",children:[J.jsx("button",{type:"button",onClick:()=>i(!1),children:"Cancel"}),J.jsx("button",{type:"submit",className:"btn btn-primary",children:"Register Application"})]})]})]})}),J.jsx("div",{className:"applications-grid",children:0===e.length?J.jsxs("div",{className:"no-applications",children:[J.jsx("p",{children:"No applications registered yet."}),J.jsx("button",{className:"btn btn-primary",onClick:()=>i(!0),children:"Register Your First Application"})]}):e.map(e=>{return J.jsxs("div",{className:"application-card "+(e.active?"":"inactive"),children:[J.jsxs("div",{className:"application-header",children:[J.jsx("h3",{children:e.name}),J.jsx("div",{className:"application-status",children:J.jsx("span",{className:"status-badge "+(e.active?"active":"inactive"),children:e.active?"Active":"Inactive"})})]}),J.jsxs("div",{className:"application-details",children:[J.jsx("p",{className:"description",children:e.description}),J.jsxs("div",{className:"application-info",children:[J.jsxs("div",{className:"info-item",children:[J.jsx("label",{children:"Application ID:"}),J.jsx("span",{children:e.id})]}),J.jsxs("div",{className:"info-item",children:[J.jsx("label",{children:"API Key:"}),J.jsxs("div",{className:"api-key-container",children:[J.jsx("span",{className:"api-key",children:u[e.id]?e.apiKey:(t=e.apiKey,t.length<=8?t:t.substring(0,4)+"..."+t.substring(t.length-4))}),J.jsx("button",{className:"btn-icon",onClick:()=>{return t=e.id,void d(e=>({...e,[t]:!e[t]}));var t},title:u[e.id]?"Hide API Key":"Show API Key",children:u[e.id]?"👁️":"👁️‍🗨️"}),J.jsx("button",{className:"btn-icon",onClick:()=>navigator.clipboard.writeText(e.apiKey),title:"Copy API Key",children:"📋"})]})]}),J.jsxs("div",{className:"info-item",children:[J.jsx("label",{children:"Created:"}),J.jsx("span",{children:new Date(e.createdAt).toLocaleDateString()})]}),J.jsxs("div",{className:"info-item",children:[J.jsx("label",{children:"Last Updated:"}),J.jsx("span",{children:new Date(e.updatedAt).toLocaleDateString()})]})]})]}),J.jsxs("div",{className:"application-actions",children:[J.jsx("button",{className:"btn "+(e.active?"btn-warning":"btn-success"),onClick:()=>(async e=>{try{e.active?await Zf(e.id):await Xf(e.id),await m()}catch(t){l(t.message||"Failed to update application status")}})(e),children:e.active?"Deactivate":"Activate"}),J.jsx("button",{className:"btn btn-secondary",onClick:()=>(async e=>{if(confirm("Are you sure you want to generate a new API key? The old key will be invalidated."))try{const t=await eh(e.id);alert(`New API Key generated:\n${t.api_key}\n\nPlease save this API key securely - it won't be shown again.`),await m()}catch(t){l(t.message||"Failed to generate new API key")}})(e),children:"Generate New API Key"}),J.jsx("button",{className:"btn btn-info",onClick:()=>c(e),children:"View Details"})]})]},e.id);var t})}),o&&J.jsx("div",{className:"modal-overlay",children:J.jsxs("div",{className:"modal modal-large",children:[J.jsxs("div",{className:"modal-header",children:[J.jsxs("h2",{children:["Application Details: ",o.name]}),J.jsx("button",{onClick:()=>c(null),children:"×"})]}),J.jsxs("div",{className:"application-details-modal",children:[J.jsxs("div",{className:"details-section",children:[J.jsx("h3",{children:"Basic Information"}),J.jsxs("div",{className:"details-grid",children:[J.jsxs("div",{children:[J.jsx("strong",{children:"ID:"})," ",o.id]}),J.jsxs("div",{children:[J.jsx("strong",{children:"Name:"})," ",o.name]}),J.jsxs("div",{children:[J.jsx("strong",{children:"Status:"})," ",o.active?"Active":"Inactive"]}),J.jsxs("div",{children:[J.jsx("strong",{children:"Created:"})," ",new Date(o.createdAt).toLocaleString()]}),J.jsxs("div",{children:[J.jsx("strong",{children:"Updated:"})," ",new Date(o.updatedAt).toLocaleString()]})]})]}),J.jsxs("div",{className:"details-section",children:[J.jsx("h3",{children:"Description"}),J.jsx("p",{children:o.description})]}),J.jsxs("div",{className:"details-section",children:[J.jsx("h3",{children:"API Key"}),J.jsxs("div",{className:"api-key-display",children:[J.jsx("code",{children:o.apiKey}),J.jsx("button",{className:"btn btn-small",onClick:()=>navigator.clipboard.writeText(o.apiKey),children:"Copy"})]})]}),o.metadata&&Object.keys(o.metadata).length>0&&J.jsxs("div",{className:"details-section",children:[J.jsx("h3",{children:"Metadata"}),J.jsx("pre",{className:"metadata-display",children:JSON.stringify(o.metadata,null,2)})]})]})]})})]})},Lh=()=>{const[e,t]=U.useState([]),[n,a]=U.useState(!0),[r,l]=U.useState(null),[s,i]=U.useState(0),[o,c]=U.useState(1),[u]=U.useState(50),[d,f]=U.useState(null),[h,m]=U.useState({limit:u,offset:0}),[p,v]=U.useState({fromDate:"",toDate:""});U.useEffect(()=>{g()},[h]);const g=async()=>{a(!0),l(null);try{const e=await th(h);t(e.audit_entries),i(e.total_count)}catch(e){e.message.includes("not available")?l("Audit logging is not available in the current Database Service version. This feature is planned for future releases."):l(e.message||"Failed to fetch audit logs"),t([]),i(0)}finally{a(!1)}},y=(e,t)=>{m(n=>({...n,[e]:t,offset:0})),c(1)},x=(e,t)=>{v(n=>({...n,[e]:t})),y("fromDate"===e?"fromTime":"toTime",t?new Date(t).toISOString():void 0)},b=e=>{c(e),m(t=>({...t,offset:(e-1)*u}))},j=e=>{switch(e.toLowerCase()){case"user_login":case"application_registered":return"#28a745";case"user_logout":default:return"#6c757d";case"database_query":return"#007bff";case"database_execute":return"#17a2b8";case"security_violation":return"#dc3545";case"rate_limit_exceeded":return"#ffc107";case"api_key_generated":return"#fd7e14"}},w=e=>new Date(e).toLocaleString(),k=Math.ceil(s/u);return n&&0===e.length?J.jsx("div",{className:"audit-loading",children:J.jsx("p",{children:"Loading audit logs..."})}):J.jsxs("div",{className:"audit-container",children:[J.jsxs("div",{className:"audit-header",children:[J.jsx("h1",{children:"Audit Logs"}),J.jsx("p",{children:"Security and compliance audit trail"}),J.jsx("div",{className:"header-actions",children:J.jsx("button",{className:"btn btn-secondary",onClick:async()=>{try{const e=await nh(h),t=window.URL.createObjectURL(e),n=document.createElement("a");n.href=t,n.download=`audit-logs-${(new Date).toISOString().split("T")[0]}.csv`,document.body.appendChild(n),n.click(),window.URL.revokeObjectURL(t),document.body.removeChild(n)}catch(e){l(e.message||"Failed to export audit logs")}},children:"Export Logs"})})]}),r&&J.jsxs("div",{className:"error-message",children:[J.jsx("p",{children:r}),J.jsx("button",{onClick:()=>l(null),children:"×"})]}),J.jsx("div",{className:"audit-filters",children:J.jsxs("div",{className:"filters-row",children:[J.jsxs("div",{className:"filter-group",children:[J.jsx("label",{children:"From Date:"}),J.jsx("input",{type:"datetime-local",value:p.fromDate,onChange:e=>x("fromDate",e.target.value)})]}),J.jsxs("div",{className:"filter-group",children:[J.jsx("label",{children:"To Date:"}),J.jsx("input",{type:"datetime-local",value:p.toDate,onChange:e=>x("toDate",e.target.value)})]}),J.jsxs("div",{className:"filter-group",children:[J.jsx("label",{children:"Event Type:"}),J.jsxs("select",{value:h.eventType||"",onChange:e=>y("eventType",e.target.value||void 0),children:[J.jsx("option",{value:"",children:"All Events"}),J.jsx("option",{value:"USER_LOGIN",children:"User Login"}),J.jsx("option",{value:"USER_LOGOUT",children:"User Logout"}),J.jsx("option",{value:"DATABASE_QUERY",children:"Database Query"}),J.jsx("option",{value:"DATABASE_EXECUTE",children:"Database Execute"}),J.jsx("option",{value:"SECURITY_VIOLATION",children:"Security Violation"}),J.jsx("option",{value:"RATE_LIMIT_EXCEEDED",children:"Rate Limit Exceeded"}),J.jsx("option",{value:"APPLICATION_REGISTERED",children:"Application Registered"}),J.jsx("option",{value:"API_KEY_GENERATED",children:"API Key Generated"})]})]}),J.jsxs("div",{className:"filter-group",children:[J.jsx("label",{children:"Success:"}),J.jsxs("select",{value:void 0===h.success?"":h.success.toString(),onChange:e=>y("success",""===e.target.value?void 0:"true"===e.target.value),children:[J.jsx("option",{value:"",children:"All"}),J.jsx("option",{value:"true",children:"Success"}),J.jsx("option",{value:"false",children:"Failed"})]})]}),J.jsxs("div",{className:"filter-actions",children:[J.jsx("button",{className:"btn btn-primary",onClick:g,children:"Apply Filters"}),J.jsx("button",{className:"btn btn-secondary",onClick:()=>{m({limit:u,offset:0}),v({fromDate:"",toDate:""}),c(1)},children:"Clear"})]})]})}),J.jsxs("div",{className:"audit-summary",children:[J.jsxs("p",{children:["Showing ",e.length," of ",s," entries"]}),n&&J.jsx("span",{className:"loading-indicator",children:"Loading..."})]}),J.jsx("div",{className:"audit-table-container",children:J.jsxs("table",{className:"audit-table",children:[J.jsx("thead",{children:J.jsxs("tr",{children:[J.jsx("th",{children:"Timestamp"}),J.jsx("th",{children:"Event Type"}),J.jsx("th",{children:"User/App"}),J.jsx("th",{children:"Action"}),J.jsx("th",{children:"Resource"}),J.jsx("th",{children:"Status"}),J.jsx("th",{children:"IP Address"}),J.jsx("th",{children:"Actions"})]})}),J.jsx("tbody",{children:e.map(e=>J.jsxs("tr",{className:e.success?"":"failed-entry",children:[J.jsx("td",{className:"timestamp",children:w(e.timestamp)}),J.jsx("td",{children:J.jsx("span",{className:"event-type-badge",style:{backgroundColor:j(e.eventType)},children:e.eventType})}),J.jsxs("td",{children:[e.userId>0?`User ${e.userId}`:"",e.applicationId>0?`App ${e.applicationId}`:"",0===e.userId&&0===e.applicationId?"System":""]}),J.jsx("td",{className:"action",children:e.action}),J.jsx("td",{className:"resource",children:e.resource}),J.jsx("td",{children:J.jsx("span",{className:"status-badge "+(e.success?"success":"failed"),children:e.success?"Success":"Failed"})}),J.jsx("td",{className:"ip-address",children:e.ipAddress||"-"}),J.jsx("td",{children:J.jsx("button",{className:"btn btn-small btn-info",onClick:()=>f(e),children:"Details"})})]},e.id))})]})}),k>1&&J.jsxs("div",{className:"pagination",children:[J.jsx("button",{className:"btn btn-secondary",disabled:1===o,onClick:()=>b(o-1),children:"Previous"}),J.jsxs("span",{className:"page-info",children:["Page ",o," of ",k]}),J.jsx("button",{className:"btn btn-secondary",disabled:o===k,onClick:()=>b(o+1),children:"Next"})]}),d&&J.jsx("div",{className:"modal-overlay",children:J.jsxs("div",{className:"modal modal-large",children:[J.jsxs("div",{className:"modal-header",children:[J.jsx("h2",{children:"Audit Entry Details"}),J.jsx("button",{onClick:()=>f(null),children:"×"})]}),J.jsxs("div",{className:"audit-details",children:[J.jsxs("div",{className:"details-section",children:[J.jsx("h3",{children:"Basic Information"}),J.jsxs("div",{className:"details-grid",children:[J.jsxs("div",{children:[J.jsx("strong",{children:"ID:"})," ",d.id]}),J.jsxs("div",{children:[J.jsx("strong",{children:"Timestamp:"})," ",w(d.timestamp)]}),J.jsxs("div",{children:[J.jsx("strong",{children:"Event Type:"})," ",d.eventType]}),J.jsxs("div",{children:[J.jsx("strong",{children:"Action:"})," ",d.action]}),J.jsxs("div",{children:[J.jsx("strong",{children:"Resource:"})," ",d.resource]}),J.jsxs("div",{children:[J.jsx("strong",{children:"Status:"})," ",d.success?"Success":"Failed"]})]})]}),J.jsxs("div",{className:"details-section",children:[J.jsx("h3",{children:"User/Application Information"}),J.jsxs("div",{className:"details-grid",children:[J.jsxs("div",{children:[J.jsx("strong",{children:"User ID:"})," ",d.userId||"N/A"]}),J.jsxs("div",{children:[J.jsx("strong",{children:"Application ID:"})," ",d.applicationId||"N/A"]}),J.jsxs("div",{children:[J.jsx("strong",{children:"IP Address:"})," ",d.ipAddress||"N/A"]}),J.jsxs("div",{children:[J.jsx("strong",{children:"User Agent:"})," ",d.userAgent||"N/A"]})]})]}),d.errorMessage&&J.jsxs("div",{className:"details-section",children:[J.jsx("h3",{children:"Error Information"}),J.jsx("div",{className:"error-details",children:d.errorMessage})]}),d.details&&Object.keys(d.details).length>0&&J.jsxs("div",{className:"details-section",children:[J.jsx("h3",{children:"Additional Details"}),J.jsx("pre",{className:"details-json",children:JSON.stringify(d.details,null,2)})]})]})]})})]})},Rh=()=>{const[e,t]=U.useState(null),[n,a]=U.useState(!0),[r,l]=U.useState(null),[s,i]=U.useState(""),[o,c]=U.useState(null),[u,d]=U.useState(!1),[f,h]=U.useState(!1),[m,p]=U.useState(""),[v,g]=U.useState(""),[y,x]=U.useState(""),[b,j]=U.useState("");U.useEffect(()=>{w();const e=setInterval(w,3e4);return()=>clearInterval(e)},[]);const w=async()=>{a(!0),l(null);try{const e=await ah();t(e)}catch(e){l(e.message||"Failed to fetch rate limiting stats")}finally{a(!1)}},k=async()=>{if(s.trim()){l(null);try{const e=await rh(s.trim());c(e)}catch(e){l(e.message||"Failed to fetch client stats"),c(null)}}else l("Please enter a client ID")};return n&&!e?J.jsx("div",{className:"rate-limit-loading",children:J.jsx("p",{children:"Loading rate limiting data..."})}):J.jsxs("div",{className:"rate-limit-container",children:[J.jsxs("div",{className:"rate-limit-header",children:[J.jsx("h1",{children:"Rate Limiting Management"}),J.jsx("p",{children:"Monitor and manage API rate limiting and client access control"}),J.jsx("div",{className:"header-actions",children:J.jsx("button",{className:"btn btn-primary",onClick:w,children:"Refresh Stats"})})]}),r&&J.jsxs("div",{className:"error-message",children:[J.jsx("p",{children:r}),J.jsx("button",{onClick:()=>l(null),children:"×"})]}),e&&J.jsxs("div",{className:"stats-grid",children:[J.jsxs("div",{className:"stat-card",children:[J.jsx("h3",{children:"Total Requests"}),J.jsx("div",{className:"stat-value",children:e.totalRequests.toLocaleString()}),J.jsx("div",{className:"stat-label",children:"All time"})]}),J.jsxs("div",{className:"stat-card",children:[J.jsx("h3",{children:"Allowed Requests"}),J.jsx("div",{className:"stat-value",children:e.allowedRequests.toLocaleString()}),J.jsx("div",{className:"stat-label",children:"Passed rate limits"})]}),J.jsxs("div",{className:"stat-card",children:[J.jsx("h3",{children:"Blocked Requests"}),J.jsx("div",{className:"stat-value",children:e.blockedRequests.toLocaleString()}),J.jsx("div",{className:"stat-label",children:"Rate limited"})]}),J.jsxs("div",{className:"stat-card",children:[J.jsx("h3",{children:"Success Rate"}),J.jsxs("div",{className:"stat-value",children:[e.successRate.toFixed(1),"%"]}),J.jsx("div",{className:"stat-label",children:"Allowed vs total"})]}),J.jsxs("div",{className:"stat-card",children:[J.jsx("h3",{children:"Active Clients"}),J.jsx("div",{className:"stat-value",children:e.activeClients}),J.jsx("div",{className:"stat-label",children:"Currently active"})]}),J.jsxs("div",{className:"stat-card",children:[J.jsx("h3",{children:"Service Uptime"}),J.jsx("div",{className:"stat-value",children:(e=>{const t=Math.floor(e/86400),n=Math.floor(e%86400/3600),a=Math.floor(e%3600/60);return t>0?`${t}d ${n}h ${a}m`:n>0?`${n}h ${a}m`:`${a}m`})(e.uptimeSeconds)}),J.jsx("div",{className:"stat-label",children:"Since last restart"})]})]}),J.jsxs("div",{className:"management-sections",children:[J.jsxs("div",{className:"section",children:[J.jsx("h2",{children:"Client Statistics"}),J.jsxs("div",{className:"client-lookup",children:[J.jsxs("div",{className:"lookup-form",children:[J.jsx("input",{type:"text",placeholder:"Enter client ID (IP address or API key)",value:s,onChange:e=>i(e.target.value),onKeyPress:e=>"Enter"===e.key&&k()}),J.jsx("button",{className:"btn btn-primary",onClick:k,children:"Lookup Client"}),J.jsx("button",{className:"btn btn-warning",onClick:async()=>{if(s.trim()){if(confirm(`Are you sure you want to reset rate limits for ${s}?`))try{await ih(s.trim()),await k(),alert("Client rate limits reset successfully")}catch(e){l(e.message||"Failed to reset client limits")}}else l("Please enter a client ID")},children:"Reset Limits"})]}),o&&J.jsxs("div",{className:"client-stats",children:[J.jsxs("h3",{children:["Client: ",s]}),J.jsxs("div",{className:"client-stats-grid",children:[J.jsxs("div",{children:[J.jsx("strong",{children:"Total Requests:"})," ",o.total_requests||0]}),J.jsxs("div",{children:[J.jsx("strong",{children:"Allowed:"})," ",o.allowed_requests||0]}),J.jsxs("div",{children:[J.jsx("strong",{children:"Blocked:"})," ",o.blocked_requests||0]}),J.jsxs("div",{children:[J.jsx("strong",{children:"Last Request:"})," ",o.last_request_time||"Never"]}),J.jsxs("div",{children:[J.jsx("strong",{children:"Current Window:"})," ",o.current_window_requests||0]}),J.jsxs("div",{children:[J.jsx("strong",{children:"Window Resets:"})," ",o.window_reset_time||"N/A"]})]})]})]})]}),J.jsxs("div",{className:"section",children:[J.jsx("h2",{children:"Whitelist Management"}),J.jsxs("div",{className:"list-management",children:[J.jsxs("div",{className:"list-header",children:[J.jsx("p",{children:"Whitelisted clients bypass all rate limiting"}),J.jsx("button",{className:"btn btn-success",onClick:()=>d(!0),children:"Add to Whitelist"})]}),e&&J.jsxs("div",{className:"list-stats",children:[J.jsxs("p",{children:["Current whitelist size: ",e.whitelistSize]}),J.jsxs("p",{children:["Whitelist hits: ",e.whitelistHits.toLocaleString()]})]})]})]}),J.jsxs("div",{className:"section",children:[J.jsx("h2",{children:"Blacklist Management"}),J.jsxs("div",{className:"list-management",children:[J.jsxs("div",{className:"list-header",children:[J.jsx("p",{children:"Blacklisted clients are completely blocked"}),J.jsx("button",{className:"btn btn-danger",onClick:()=>h(!0),children:"Add to Blacklist"})]}),e&&J.jsxs("div",{className:"list-stats",children:[J.jsxs("p",{children:["Current blacklist size: ",e.blacklistSize]}),J.jsxs("p",{children:["Blacklist hits: ",e.blacklistHits.toLocaleString()]})]})]})]})]}),u&&J.jsx("div",{className:"modal-overlay",children:J.jsxs("div",{className:"modal",children:[J.jsxs("div",{className:"modal-header",children:[J.jsx("h2",{children:"Add Client to Whitelist"}),J.jsx("button",{onClick:()=>d(!1),children:"×"})]}),J.jsxs("form",{onSubmit:async e=>{if(e.preventDefault(),m.trim()){l(null);try{await lh(m.trim()),d(!1),p(""),await w(),alert("Client added to whitelist successfully")}catch(t){l(t.message||"Failed to add client to whitelist")}}else l("Please enter a client ID")},className:"modal-form",children:[J.jsxs("div",{className:"form-group",children:[J.jsx("label",{htmlFor:"whitelist-client-id",children:"Client ID (IP address or API key) *"}),J.jsx("input",{type:"text",id:"whitelist-client-id",value:m,onChange:e=>p(e.target.value),required:!0,placeholder:"e.g., ************* or api_key_123"})]}),J.jsxs("div",{className:"form-actions",children:[J.jsx("button",{type:"button",onClick:()=>d(!1),children:"Cancel"}),J.jsx("button",{type:"submit",className:"btn btn-success",children:"Add to Whitelist"})]})]})]})}),f&&J.jsx("div",{className:"modal-overlay",children:J.jsxs("div",{className:"modal",children:[J.jsxs("div",{className:"modal-header",children:[J.jsx("h2",{children:"Add Client to Blacklist"}),J.jsx("button",{onClick:()=>h(!1),children:"×"})]}),J.jsxs("form",{onSubmit:async e=>{if(e.preventDefault(),v.trim()&&y.trim()){l(null);try{const e=b?parseInt(b):void 0;await sh(v.trim(),y.trim(),e),h(!1),g(""),x(""),j(""),await w(),alert("Client added to blacklist successfully")}catch(t){l(t.message||"Failed to add client to blacklist")}}else l("Please enter both client ID and reason")},className:"modal-form",children:[J.jsxs("div",{className:"form-group",children:[J.jsx("label",{htmlFor:"blacklist-client-id",children:"Client ID (IP address or API key) *"}),J.jsx("input",{type:"text",id:"blacklist-client-id",value:v,onChange:e=>g(e.target.value),required:!0,placeholder:"e.g., ************* or api_key_123"})]}),J.jsxs("div",{className:"form-group",children:[J.jsx("label",{htmlFor:"blacklist-reason",children:"Reason *"}),J.jsx("input",{type:"text",id:"blacklist-reason",value:y,onChange:e=>x(e.target.value),required:!0,placeholder:"e.g., Abuse, Security violation, etc."})]}),J.jsxs("div",{className:"form-group",children:[J.jsx("label",{htmlFor:"blacklist-duration",children:"Duration (seconds, optional)"}),J.jsx("input",{type:"number",id:"blacklist-duration",value:b,onChange:e=>j(e.target.value),placeholder:"Leave empty for permanent",min:"1"})]}),J.jsxs("div",{className:"form-actions",children:[J.jsx("button",{type:"button",onClick:()=>h(!1),children:"Cancel"}),J.jsx("button",{type:"submit",className:"btn btn-danger",children:"Add to Blacklist"})]})]})]})})]})},Dh=()=>{const[e,t]=U.useState("query"),[n,a]=U.useState(""),[r,l]=U.useState("database-service-ui"),[s,i]=U.useState(""),[o,c]=U.useState("public"),[u,d]=U.useState("{}"),[f,h]=U.useState(null),[m,p]=U.useState(null),[v,g]=U.useState(!1),[y,x]=U.useState([]),[b,j]=U.useState([""]);U.useEffect(()=>{(async()=>{try{const e=await Mf();x(e.databases),e.databases.length>0&&!s&&i(e.databases[0].name)}catch(e){console.warn("Database listing not available:",e.message),x([])}})()},[s]);return J.jsxs("div",{className:"query-execution-container",children:[J.jsx("h1",{children:"Query Execution"}),J.jsxs("div",{className:"query-form",children:[J.jsxs("div",{className:"form-row",children:[J.jsxs("div",{className:"form-group",children:[J.jsx("label",{htmlFor:"queryType",children:"Operation Type:"}),J.jsxs("select",{id:"queryType",value:e,onChange:e=>t(e.target.value),children:[J.jsx("option",{value:"query",children:"SELECT Query"}),J.jsx("option",{value:"execute",children:"DML Statement (INSERT/UPDATE/DELETE)"}),J.jsx("option",{value:"transaction",children:"Transaction (Multiple Statements)"})]})]}),J.jsxs("div",{className:"form-group",children:[J.jsx("label",{htmlFor:"database",children:"Database:"}),y.length>0?J.jsxs("select",{id:"database",value:s,onChange:e=>i(e.target.value),children:[J.jsx("option",{value:"",children:"Select Database"}),y.map(e=>J.jsx("option",{value:e.name,children:e.name},e.name))]}):J.jsx("input",{type:"text",id:"database",value:s,onChange:e=>i(e.target.value),placeholder:"Enter database name (e.g., postgres, myapp_db)"}),0===y.length&&J.jsx("small",{style:{color:"#666",fontSize:"0.8rem",marginTop:"0.25rem",display:"block"},children:"Database listing not available. Please enter the database name manually."})]}),J.jsxs("div",{className:"form-group",children:[J.jsx("label",{htmlFor:"schema",children:"Schema:"}),J.jsx("input",{type:"text",id:"schema",value:o,onChange:e=>c(e.target.value),placeholder:"public"})]})]}),J.jsx("div",{className:"form-row",children:J.jsxs("div",{className:"form-group",children:[J.jsx("label",{htmlFor:"application",children:"Application:"}),J.jsx("input",{type:"text",id:"application",value:r,onChange:e=>l(e.target.value),placeholder:"database-service-ui"})]})}),"transaction"!==e?J.jsxs("div",{className:"form-group",children:[J.jsxs("label",{htmlFor:"sqlText",children:["SQL ","query"===e?"Query":"Statement",":"]}),J.jsx("textarea",{id:"sqlText",value:n,onChange:e=>a(e.target.value),placeholder:"query"===e?"SELECT * FROM users WHERE id = $1":"INSERT INTO users (name, email) VALUES ($1, $2)",rows:8})]}):J.jsxs("div",{className:"form-group",children:[J.jsx("label",{children:"Transaction Statements:"}),b.map((e,t)=>J.jsxs("div",{className:"transaction-statement",children:[J.jsx("textarea",{value:e,onChange:e=>((e,t)=>{const n=[...b];n[e]=t,j(n)})(t,e.target.value),placeholder:`Statement ${t+1}`,rows:3}),J.jsx("div",{className:"statement-actions",children:J.jsx("button",{type:"button",onClick:()=>(e=>{if(b.length>1){const t=b.filter((t,n)=>n!==e);j(t)}})(t),disabled:1===b.length,className:"remove-statement",children:"Remove"})})]},t)),J.jsx("button",{type:"button",onClick:()=>{j([...b,""])},className:"add-statement",children:"Add Statement"})]}),J.jsxs("div",{className:"form-group",children:[J.jsx("label",{htmlFor:"parameters",children:"Parameters (JSON):"}),J.jsx("textarea",{id:"parameters",value:u,onChange:e=>d(e.target.value),placeholder:'{"$1": "value1", "$2": "value2"}',rows:3})]}),J.jsx("div",{className:"form-actions",children:J.jsx("button",{onClick:async()=>{if(n.trim()){g(!0),p(null),h(null);try{let t={};if(u.trim()&&(t=JSON.parse(u)),"query"===e){const e={query:n,params:t,application:r,database:s,schema:o},a=await Ff(e);h(a)}else if("execute"===e){const e={statement:n,params:t,application:r,database:s,schema:o},a=await Of(e);h(a)}else if("transaction"===e){const e={statements:b.filter(e=>e.trim()).map(e=>({statement:e,params:t})),application:r,database:s,schema:o},n=await zf(e);h(n)}}catch(t){p(t.message||"An error occurred while executing the query")}finally{g(!1)}}else p("Please enter a SQL query")},disabled:v||!s,className:"execute-button",children:v?"Executing...":`Execute ${e.charAt(0).toUpperCase()+e.slice(1)}`})})]}),m&&J.jsxs("div",{className:"error-message",children:[J.jsx("h3",{children:"Error"}),J.jsx("pre",{children:m})]}),(()=>{if(!f)return null;if("columns"in f){const e=f;return J.jsxs("div",{className:"query-results",children:[J.jsxs("div",{className:"results-header",children:[J.jsx("h3",{children:"Query Results"}),J.jsxs("div",{className:"results-meta",children:[J.jsxs("span",{children:["Rows: ",e.rowCount]}),J.jsxs("span",{children:["Execution Time: ",e.executionTime,"ms"]})]})]}),e.rows.length>0?J.jsx("div",{className:"results-table-container",children:J.jsxs("table",{className:"results-table",children:[J.jsx("thead",{children:J.jsx("tr",{children:e.columns.map((e,t)=>J.jsx("th",{children:e},t))})}),J.jsx("tbody",{children:e.rows.map((t,n)=>J.jsx("tr",{children:e.columns.map((e,n)=>J.jsx("td",{children:null!==t[e]?String(t[e]):"NULL"},n))},n))})]})}):J.jsx("div",{className:"no-results",children:"No rows returned"})]})}if("rowsAffected"in f){const e=f;return J.jsxs("div",{className:"execute-results",children:[J.jsxs("div",{className:"results-header",children:[J.jsx("h3",{children:"Execution Results"}),J.jsxs("div",{className:"results-meta",children:[J.jsxs("span",{children:["Rows Affected: ",e.rowsAffected]}),J.jsxs("span",{children:["Execution Time: ",e.executionTime,"ms"]})]})]}),J.jsx("div",{className:"execute-message",children:e.message})]})}{const e=f;return J.jsxs("div",{className:"transaction-results",children:[J.jsxs("div",{className:"results-header",children:[J.jsx("h3",{children:"Transaction Results"}),J.jsxs("div",{className:"results-meta",children:[J.jsxs("span",{children:["Status: ",e.success?"Success":"Failed"]}),J.jsxs("span",{children:["Total Time: ",e.totalExecutionTime,"ms"]})]})]}),J.jsx("div",{className:"transaction-message",children:e.message}),e.results&&J.jsx("div",{className:"statement-results",children:e.results.map((e,t)=>J.jsxs("div",{className:"statement-result",children:[J.jsxs("strong",{children:["Statement ",t+1,":"]})," ",e.rowsAffected," rows affected (",e.executionTime,"ms)"]},t))})]})}})()]})},Ih=e=>{const t=(e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,n)=>n?n.toUpperCase():t.toLowerCase()))(e);return t.charAt(0).toUpperCase()+t.slice(1)},Ah=(...e)=>e.filter((e,t,n)=>Boolean(e)&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim(),Fh=e=>{for(const t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};
/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
var Oh={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};
/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zh=U.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:a,className:r="",children:l,iconNode:s,...i},o)=>U.createElement("svg",{ref:o,...Oh,width:t,height:t,stroke:e,strokeWidth:a?24*Number(n)/Number(t):n,className:Ah("lucide",r),...!l&&!Fh(i)&&{"aria-hidden":"true"},...i},[...s.map(([e,t])=>U.createElement(e,t)),...Array.isArray(l)?l:[l]])),Mh=(e,t)=>{const n=U.forwardRef(({className:n,...a},r)=>{return U.createElement(zh,{ref:r,iconNode:t,className:Ah(`lucide-${l=Ih(e),l.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}`,`lucide-${e}`,n),...a});var l});return n.displayName=Ih(e),n},Uh=Mh("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]),$h=Mh("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),qh=Mh("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),Bh=Mh("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]),Vh=Mh("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]]),Qh=Mh("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]]),Wh=Mh("shield-off",[["path",{d:"m2 2 20 20",key:"1ooewy"}],["path",{d:"M5 5a1 1 0 0 0-1 1v7c0 5 3.5 7.5 7.67 8.94a1 1 0 0 0 .67.01c2.35-.82 4.48-1.97 5.9-3.71",key:"1jlk70"}],["path",{d:"M9.309 3.652A12.252 12.252 0 0 0 11.24 2.28a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1v7a9.784 9.784 0 0 1-.08 1.264",key:"18rp1v"}]]),Hh=Mh("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]),Kh=Mh("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),Yh=Mh("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]),Jh=Mh("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]),Gh=Mh("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),Xh=()=>{const{user:e,refreshUser:t}=gh(),[n,a]=U.useState([]),[r,l]=U.useState(!0),[s,i]=U.useState(null),[o,c]=U.useState(null),[u,d]=U.useState(!1),[f,h]=U.useState(!1),[m,p]=U.useState(null),[v,g]=U.useState({username:"",password:"",email:"",is_admin:!1}),[y,x]=U.useState({new_password:"",current_password:""}),b=()=>{i(null),c(null)},j=async()=>{try{l(!0),b();const e=await oh();a(e.users)}catch(e){i(e.message||"Failed to load users"),a([{id:1,username:"admin",email:"admin@localhost",is_admin:!0,active:!0,created_at:(new Date).toISOString(),last_login:(new Date).toISOString()},{id:2,username:"user1",email:"user1@localhost",is_admin:!1,active:!0,created_at:new Date(Date.now()-864e5).toISOString(),last_login:new Date(Date.now()-36e5).toISOString()}])}finally{l(!1)}};U.useEffect(()=>{j()},[]);const w=e=>new Date(e).toLocaleString();return r?J.jsx("div",{className:"loading-container",children:J.jsx("div",{className:"loading-spinner"})}):J.jsxs("div",{className:"user-management",children:[J.jsxs("div",{className:"user-management-header",children:[J.jsxs("div",{className:"user-management-title",children:[J.jsx(Jh,{}),J.jsx("h1",{children:"User Management"})]}),(null==e?void 0:e.is_admin)&&J.jsxs("button",{onClick:()=>d(!0),className:"add-user-btn",children:[J.jsx(Yh,{}),J.jsx("span",{children:"Add User"})]})]}),s&&J.jsxs("div",{className:"message message-error",children:[J.jsx($h,{}),J.jsx("span",{children:s}),J.jsx("button",{onClick:b,className:"message-close",children:J.jsx(Gh,{})})]}),o&&J.jsxs("div",{className:"message message-success",children:[J.jsx(qh,{}),J.jsx("span",{children:o}),J.jsx("button",{onClick:b,className:"message-close",children:J.jsx(Gh,{})})]}),J.jsx("div",{className:"users-table-container",children:J.jsxs("table",{className:"users-table",children:[J.jsx("thead",{children:J.jsxs("tr",{children:[J.jsx("th",{children:"User"}),J.jsx("th",{children:"Role"}),J.jsx("th",{children:"Status"}),J.jsx("th",{children:"Last Login"}),J.jsx("th",{children:"Created"}),(null==e?void 0:e.is_admin)&&J.jsx("th",{children:"Actions"})]})}),J.jsx("tbody",{children:n.map(t=>J.jsxs("tr",{children:[J.jsx("td",{children:J.jsxs("div",{className:"user-info",children:[J.jsx("div",{className:"user-avatar",children:J.jsx("span",{children:t.username.charAt(0).toUpperCase()})}),J.jsxs("div",{className:"user-details",children:[J.jsx("h3",{children:t.username}),J.jsxs("div",{className:"user-email",children:[J.jsx(Qh,{}),t.email]})]})]})}),J.jsx("td",{children:J.jsx("div",{className:"user-role "+(t.is_admin?"admin":"user"),children:t.is_admin?J.jsxs(J.Fragment,{children:[J.jsx(Hh,{}),J.jsx("span",{children:"Administrator"})]}):J.jsxs(J.Fragment,{children:[J.jsx(Jh,{}),J.jsx("span",{children:"User"})]})})}),J.jsx("td",{children:J.jsx("span",{className:"status-badge "+(t.active?"active":"inactive"),children:t.active?"Active":"Inactive"})}),J.jsx("td",{children:J.jsxs("div",{className:"date-info",children:[J.jsx(Bh,{}),t.last_login?w(t.last_login):"Never"]})}),J.jsx("td",{children:J.jsxs("div",{className:"date-info",children:[J.jsx(Uh,{}),w(t.created_at)]})}),(null==e?void 0:e.is_admin)&&J.jsx("td",{children:J.jsxs("div",{className:"user-actions",children:[J.jsx("button",{onClick:()=>{p(t),h(!0)},className:"action-btn blue",title:"Change Password",children:J.jsx(Vh,{})}),J.jsx("button",{onClick:()=>(async e=>{try{b(),e.active?(await mh(e.username),c(`User '${e.username}' deactivated`)):(await hh(e.username),c(`User '${e.username}' activated`)),await j()}catch(t){i(t.message||"Failed to update user status")}})(t),className:"action-btn "+(t.active?"red":"green"),title:t.active?"Deactivate User":"Activate User",children:t.active?J.jsx(Wh,{}):J.jsx(Hh,{})}),t.username!==(null==e?void 0:e.username)&&J.jsx("button",{onClick:()=>(async e=>{if(confirm(`Are you sure you want to delete user '${e}'?`))try{b(),await fh(e),c(`User '${e}' deleted successfully`),await j()}catch(t){i(t.message||"Failed to delete user")}})(t.username),className:"action-btn red",title:"Delete User",children:J.jsx(Kh,{})})]})})]},t.id))})]})}),u&&J.jsx("div",{className:"modal-overlay",children:J.jsxs("div",{className:"modal-content",children:[J.jsx("h2",{className:"modal-title",children:"Create New User"}),J.jsxs("form",{onSubmit:async e=>{e.preventDefault();try{b(),await uh(v),c(`User '${v.username}' created successfully`),d(!1),g({username:"",password:"",email:"",is_admin:!1}),await j()}catch(t){i(t.message||"Failed to create user")}},children:[J.jsxs("div",{className:"form-group",children:[J.jsx("label",{className:"form-label",children:"Username"}),J.jsx("input",{type:"text",required:!0,value:v.username,onChange:e=>g({...v,username:e.target.value}),className:"form-input"})]}),J.jsxs("div",{className:"form-group",children:[J.jsx("label",{className:"form-label",children:"Password"}),J.jsx("input",{type:"password",required:!0,value:v.password,onChange:e=>g({...v,password:e.target.value}),className:"form-input"})]}),J.jsxs("div",{className:"form-group",children:[J.jsx("label",{className:"form-label",children:"Email"}),J.jsx("input",{type:"email",value:v.email,onChange:e=>g({...v,email:e.target.value}),className:"form-input"})]}),J.jsxs("div",{className:"checkbox-group",children:[J.jsx("input",{type:"checkbox",id:"is_admin",checked:v.is_admin,onChange:e=>g({...v,is_admin:e.target.checked}),className:"checkbox-input"}),J.jsx("label",{htmlFor:"is_admin",className:"form-label",children:"Administrator"})]}),J.jsxs("div",{className:"form-actions",children:[J.jsx("button",{type:"button",onClick:()=>{d(!1),g({username:"",password:"",email:"",is_admin:!1})},className:"btn btn-secondary",children:"Cancel"}),J.jsx("button",{type:"submit",className:"btn btn-primary",children:"Create User"})]})]})]})}),f&&m&&J.jsx("div",{className:"modal-overlay",children:J.jsxs("div",{className:"modal-content",children:[J.jsxs("h2",{className:"modal-title",children:["Change Password for ",m.username]}),J.jsxs("form",{onSubmit:async t=>{t.preventDefault();try{b();const t={new_password:y.new_password,username:null==m?void 0:m.username};(null==m?void 0:m.username)===(null==e?void 0:e.username)&&(t.current_password=y.current_password),await dh(t),c(`Password changed successfully for ${null==m?void 0:m.username}`),h(!1),x({new_password:"",current_password:""}),p(null)}catch(n){i(n.message||"Failed to change password")}},children:[m.username===(null==e?void 0:e.username)&&J.jsxs("div",{className:"form-group",children:[J.jsx("label",{className:"form-label",children:"Current Password"}),J.jsx("input",{type:"password",required:!0,value:y.current_password,onChange:e=>x({...y,current_password:e.target.value}),className:"form-input"})]}),J.jsxs("div",{className:"form-group",children:[J.jsx("label",{className:"form-label",children:"New Password"}),J.jsx("input",{type:"password",required:!0,value:y.new_password,onChange:e=>x({...y,new_password:e.target.value}),className:"form-input"})]}),J.jsxs("div",{className:"form-actions",children:[J.jsx("button",{type:"button",onClick:()=>{h(!1),x({new_password:"",current_password:""}),p(null)},className:"btn btn-secondary",children:"Cancel"}),J.jsx("button",{type:"submit",className:"btn btn-primary",children:"Change Password"})]})]})]})})]})},Zh=()=>J.jsx("div",{className:"user-management-page",children:J.jsx("div",{className:"container",children:J.jsx(Xh,{})})}),em=()=>{const{user:e}=gh();U.useState(!1);const[t,n]=U.useState(null),[a,r]=U.useState(null),l=()=>{n(null),r(null)},s=e=>new Date(e).toLocaleString();return e?J.jsxs("div",{className:"user-profile",children:[J.jsx("div",{className:"profile-header",children:J.jsx("h1",{children:"User Profile"})}),t&&J.jsxs("div",{className:"message message-error",children:[J.jsx("span",{children:t}),J.jsx("button",{onClick:l,className:"message-close",children:"×"})]}),a&&J.jsxs("div",{className:"message message-success",children:[J.jsx("span",{children:a}),J.jsx("button",{onClick:l,className:"message-close",children:"×"})]}),J.jsxs("div",{className:"profile-grid",children:[J.jsxs("div",{className:"profile-card",children:[J.jsx("h2",{className:"card-title",children:"Profile Information"}),J.jsxs("div",{className:"profile-avatar-section",children:[J.jsx("div",{className:"profile-avatar",children:J.jsx("span",{children:e.username.charAt(0).toUpperCase()})}),J.jsxs("div",{className:"avatar-info",children:[J.jsx("h3",{children:e.username}),J.jsx("div",{className:"user-role-badge "+(e.is_admin?"admin":"user"),children:J.jsx("span",{children:e.is_admin?"Administrator":"User"})})]})]}),J.jsxs("div",{className:"profile-details",children:[J.jsx("div",{className:"detail-item",children:J.jsxs("div",{className:"detail-content",children:[J.jsx("div",{className:"detail-label",children:"Email"}),J.jsx("div",{className:"detail-value",children:e.email})]})}),J.jsx("div",{className:"detail-item",children:J.jsxs("div",{className:"detail-content",children:[J.jsx("div",{className:"detail-label",children:"Member Since"}),J.jsx("div",{className:"detail-value",children:s(e.created_at)})]})}),J.jsx("div",{className:"detail-item",children:J.jsxs("div",{className:"detail-content",children:[J.jsx("div",{className:"detail-label",children:"Last Login"}),J.jsx("div",{className:"detail-value",children:e.last_login?s(e.last_login):"Never"})]})}),J.jsxs("div",{className:"detail-item",children:[J.jsx("div",{className:"status-indicator "+(e.active?"active":"inactive")}),J.jsxs("div",{className:"detail-content",children:[J.jsx("div",{className:"detail-label",children:"Account Status"}),J.jsx("div",{className:"detail-value",children:e.active?"Active":"Inactive"})]})]})]})]}),J.jsxs("div",{className:"profile-card",children:[J.jsx("h2",{className:"card-title",children:"Account Management"}),J.jsx("p",{children:"Contact your administrator to update your profile or change your password."})]})]})]}):J.jsx("div",{className:"no-user-message",children:J.jsx("div",{children:"No user data available"})})},tm=()=>J.jsx("div",{className:"user-profile-page",children:J.jsx("div",{className:"container",children:J.jsx(em,{})})}),nm=()=>J.jsx("div",{className:"not-found-container",children:J.jsxs("div",{className:"not-found-content",children:[J.jsx("h1",{children:"404"}),J.jsx("h2",{children:"Page Not Found"}),J.jsx("p",{children:"The page you are looking for doesn't exist or has been moved."}),J.jsx(jf,{to:"/",className:"back-home-button",children:"Back to Home"})]})}),am=({children:e})=>{const{isAuthenticated:t,isLoading:n}=gh();return n?J.jsx("div",{className:"loading-container",children:"Loading..."}):t?e:J.jsx(uf,{to:"/",replace:!0})},rm=()=>{const{isAuthenticated:e}=gh();return J.jsxs("div",{className:"app-container",children:[J.jsx(bh,{}),J.jsx(yh,{}),J.jsx("main",{className:"main-content",children:J.jsxs(hf,{children:[J.jsx(df,{path:"/",element:e?J.jsx(uf,{to:"/dashboard",replace:!0}):J.jsx(Sh,{})}),J.jsx(df,{path:"/dashboard",element:J.jsx(am,{children:J.jsx(_h,{})})}),J.jsx(df,{path:"/credentials",element:J.jsx(am,{children:J.jsx(Ch,{})})}),J.jsx(df,{path:"/metrics",element:J.jsx(am,{children:J.jsx(Eh,{})})}),J.jsx(df,{path:"/settings",element:J.jsx(am,{children:J.jsx(Ph,{})})}),J.jsx(df,{path:"/applications",element:J.jsx(am,{children:J.jsx(Th,{})})}),J.jsx(df,{path:"/audit-logs",element:J.jsx(am,{children:J.jsx(Lh,{})})}),J.jsx(df,{path:"/rate-limiting",element:J.jsx(am,{children:J.jsx(Rh,{})})}),J.jsx(df,{path:"/query-execution",element:J.jsx(am,{children:J.jsx(Dh,{})})}),J.jsx(df,{path:"/databases",element:J.jsx(am,{children:J.jsx(kh,{})})}),J.jsx(df,{path:"/users",element:J.jsx(am,{children:J.jsx(Zh,{})})}),J.jsx(df,{path:"/profile",element:J.jsx(am,{children:J.jsx(tm,{})})}),J.jsx(df,{path:"/privacy",element:J.jsx(jh,{})}),J.jsx(df,{path:"/terms",element:J.jsx(wh,{})}),J.jsx(df,{path:"*",element:J.jsx(nm,{})})]})}),J.jsx(xh,{})]})};
/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */function lm(){return J.jsx(yf,{children:J.jsx(vh,{children:J.jsx(rm,{})})})}G.createRoot(document.getElementById("root")).render(J.jsx($.StrictMode,{children:J.jsx(lm,{})}));
