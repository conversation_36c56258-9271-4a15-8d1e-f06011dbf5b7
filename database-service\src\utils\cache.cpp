#include "database-service/utils/cache.hpp"
#include <sstream>
#include <algorithm>
#include <format>

namespace dbservice::utils {

QueryCache::QueryCache(size_t maxSize, std::chrono::milliseconds defaultTtl)
    : cache_(maxSize, defaultTtl) {
}

std::optional<QueryCache::QueryResult> QueryCache::get(const std::string& query, 
                                                       const std::vector<std::string>& params) {
    auto key = makeKey(query, params);
    return cache_.get(key);
}

void QueryCache::put(const std::string& query, 
                    const std::vector<std::string>& params,
                    const QueryResult& result,
                    std::optional<std::chrono::milliseconds> ttl) {
    auto key = makeKey(query, params);
    cache_.put(key, result, ttl);
}

void QueryCache::invalidate(const std::string& pattern) {
    // For simplicity, we'll clear the entire cache if pattern matching is requested
    // In a more sophisticated implementation, you might iterate through keys
    // and remove only matching entries
    if (!pattern.empty()) {
        cache_.clear();
    }
}

nlohmann::json QueryCache::getStats() const {
    auto stats = cache_.getStats();
    stats["type"] = "query_cache";
    return stats;
}

void QueryCache::clear() {
    cache_.clear();
}

std::string QueryCache::makeKey(const std::string& query, const std::vector<std::string>& params) const {
    std::ostringstream oss;
    oss << query;
    
    if (!params.empty()) {
        oss << "|PARAMS:";
        for (size_t i = 0; i < params.size(); ++i) {
            if (i > 0) oss << ",";
            oss << params[i];
        }
    }
    
    return oss.str();
}

} // namespace dbservice::utils
