{"database": {"port": 5432, "host": "localhost", "user": "database_service", "password": "password2311", "name": "database_service"}, "project": {"remote_build_dir": "/home/<USER>/database-service-build", "description": "database-service for development environment", "name": "database-service", "remote_install_dir": "/opt/database-service", "local_source_dir": "D:\\Augment\\project-tracker\\database-service"}, "ssh": {"username": "btaylor-admin", "port": 22, "local_key_path": "C:\\Users\\<USER>\\.ssh\\id_rsa", "host": "git.chcit.org", "key_path": "C:\\Users\\<USER>\\.ssh\\id_rsa"}, "service": {"group": "database-service", "description": "database-service for development environment", "user": "database-service", "name": "database-service"}, "version": {"number": 2, "created": "2025-04-16 20:58:00", "updated": "2025-04-16 20:58:00"}, "dependencies": [{"command": "apt-get install -y g++-14", "description": "GCC 14 compiler with C++23 support (required for modules and coroutines)", "name": "GCC 14 Compiler", "check": "g++-14 --version"}, {"command": "apt-get install -y cmake", "description": "CMake build system (minimum version 3.20.0)", "name": "CMake", "check": "cmake --version"}, {"command": "apt-get install -y build-essential", "description": "Build tools (make, etc.)", "name": "Build Essential", "check": "dpkg -s build-essential"}, {"command": "apt-get install -y libboost-all-dev", "description": "Boost C++ libraries (system, program_options components required)", "name": "Boost Libraries", "check": "dpkg -l | grep libboost"}, {"command": "apt-get install -y libpq-dev", "description": "PostgreSQL client development libraries", "name": "PostgreSQL Client Libraries", "check": "dpkg -s libpq-dev"}, {"command": "apt-get install -y pkg-config", "description": "Helper tool for discovering installed libraries", "name": "pkg-config", "check": "dpkg -s pkg-config"}, {"command": "apt-get install -y libpqxx-dev", "description": "C++ client API for PostgreSQL", "name": "PostgreSQL C++ API", "check": "dpkg -s libpqxx-dev"}, {"command": "apt-get install -y libssl-dev", "description": "OpenSSL development libraries", "name": "OpenSSL Development", "check": "dpkg -s libssl-dev"}, {"command": "apt-get install -y nlohmann-json3-dev", "description": "JSON for Modern C++", "name": "JSON Library", "check": "dpkg -s n<PERSON><PERSON>-json3-dev"}, {"command": "apt-get install -y postgresql postgresql-contrib", "description": "PostgreSQL database server", "postInstall": true, "name": "PostgreSQL Server", "check": "psql --version"}, {"command": "apt-get install -y ninja-build", "description": "High-speed build system required for C++ modules support in CMake", "name": "Ninja Build System", "check": "dpkg -s ninja-build"}, {"command": "apt-get install -y git", "description": "Version control system (for source code management)", "name": "Git", "check": "git --version"}, {"command": "apt-get install -y curl", "description": "Command line tool for transferring data (for API testing)", "name": "<PERSON><PERSON><PERSON>", "check": "curl --version"}, {"command": "apt-get install -y net-tools", "description": "Network utilities (for port checking and diagnostics)", "name": "Net Tools", "check": "netstat --version"}]}