cmake_minimum_required(VERSION 3.20)

# Find GTest
find_package(GTest QUIET)
if(NOT GTest_FOUND)
    message(STATUS "GTest not found, tests will not be built")
    return()
endif()

# Find nlohmann/json (needed for tests)
find_package(nlohmann_json QUIET)

# Add test executable
add_executable(database-service-tests
    main.cpp
    connection_test.cpp
    schema_manager_test.cpp
    security_manager_test.cpp
    api_server_test.cpp
)

# Set include directories for tests
target_include_directories(database-service-tests PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/../include
    ${Boost_INCLUDE_DIRS}
    ${PostgreSQL_INCLUDE_DIRS}
    ${OPENSSL_INCLUDE_DIR}
)

# Require C++23 features for tests
target_compile_features(database-service-tests PRIVATE cxx_std_23)

# Link libraries
target_link_libraries(database-service-tests
    PRIVATE
        GTest::GTest
        GTest::Main
        Boost::system
        Boost::program_options
        ${PostgreSQL_LIBRARIES}
        OpenSSL::Crypto
        OpenSSL::SSL
        Threads::Threads
)

# Add pqxx library
if(pqxx_FOUND)
    target_link_libraries(database-service-tests PRIVATE pqxx::pqxx)
else()
    target_link_libraries(database-service-tests PRIVATE pqxx)
endif()

# Add nlohmann/json library for tests
if(nlohmann_json_FOUND)
    target_link_libraries(database-service-tests PRIVATE nlohmann_json::nlohmann_json)
else()
    # Try to find system nlohmann/json headers
    find_path(NLOHMANN_JSON_INCLUDE_DIR nlohmann/json.hpp)
    if(NLOHMANN_JSON_INCLUDE_DIR)
        target_include_directories(database-service-tests PRIVATE ${NLOHMANN_JSON_INCLUDE_DIR})
    endif()
endif()

# Add tests
add_test(NAME database-service-tests COMMAND database-service-tests)

# Add individual test cases for better reporting
add_test(NAME connection-tests COMMAND database-service-tests --gtest_filter="ConnectionTest*:ConnectionManagerTest*:ErrorCodeTest*:SSLModeTest*")
add_test(NAME schema-tests COMMAND database-service-tests --gtest_filter="SchemaManagerTest*:SchemaFileTest*:SQLParsingTest*")
add_test(NAME security-tests COMMAND database-service-tests --gtest_filter="SecurityManagerTest*:CredentialStoreTest*:JWTTest*:PasswordSecurityTest*:SecurityConfigTest*:SecurityThreadSafetyTest*")
add_test(NAME api-tests COMMAND database-service-tests --gtest_filter="ApiServerTest*:CorsConfigTest*:SSLConfigTest*:JSONResponseTest*:HTTPMethodTest*:URLParsingTest*:AuthHeaderTest*:ContentTypeTest*")
