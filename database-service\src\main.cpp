#include "database-service/database_service.hpp"
#include "database-service/api/api_server.hpp"
#include "database-service/api/route_controller.hpp"
#include "database-service/utils/logger.hpp"
#include "database-service/utils/config_manager.hpp"
#include <iostream>
#include <string>
#include <csignal>
#include <thread>
#include <chrono>
#include <atomic>
#include <expected>
#include <format>
#include <print>

// Global variables
std::shared_ptr<dbservice::DatabaseService> g_databaseService;
std::atomic<bool> g_running(true);

// Signal handler
void signalHandler(int signal) {
    std::cout << "Received signal " << signal << std::endl;
    dbservice::utils::Logger::info(std::format("SIGNAL_HANDLER: Received signal {}", signal));
    g_running = false;
    if (g_databaseService) {
        dbservice::utils::Logger::info("SIGNAL_HANDLER: Stopping service...");
        g_databaseService->stop();
        dbservice::utils::Logger::info("SIGNAL_HANDLER: Service stopped");
    }
}

int main(int argc, char** argv) {
    // First thing - write to stdout to see if we get here
    std::cout << "=== MAIN: Entry point reached ===" << std::endl;
    std::cout.flush();

    std::cout << "MAIN: Application starting..." << std::endl;
    std::cout.flush();

    // Register signal handlers
    std::cout << "MAIN: Registering signal handlers..." << std::endl;
    std::signal(SIGINT, signalHandler);
    std::signal(SIGTERM, signalHandler);
    std::cout << "MAIN: Signal handlers registered" << std::endl;

    // Parse command line arguments
    std::cout << "MAIN: Parsing command line arguments..." << std::endl;
    std::string configFile = "config.json";
    for (int i = 1; i < argc; ++i) {
        std::string arg = argv[i];
        if (arg == "--config" && i + 1 < argc) {
            configFile = argv[++i];
        } else if (arg == "--help") {
            std::cout << "Usage: " << argv[0] << " [--config <config_file>] [--help]" << std::endl;
            return 0;
        }
    }
    std::cout << "MAIN: Command line arguments parsed, config file: " << configFile << std::endl;
    std::cout.flush();

    try {
        std::cout << "MAIN: Entering try block..." << std::endl;
        std::cout.flush();

        // Initialize logger with a safe default path
        std::cout << "MAIN: Initializing logger..." << std::endl;
        std::cout.flush();
        auto logResult = dbservice::utils::Logger::initialize("./database-service.log", "info");
        if (!logResult) {
            std::print(stderr, "Failed to initialize logger: {}\n", logResult.error());
            std::print(stderr, "Continuing without file logging...\n");
        } else {
            std::cout << "MAIN: Logger initialized successfully" << std::endl;
            std::cout.flush();
        }

        std::cout << "MAIN: About to call Logger::info..." << std::endl;
        std::cout.flush();
        dbservice::utils::Logger::info("MAIN: Starting database service...");
        std::cout << "MAIN: First Logger::info call completed" << std::endl;
        std::cout.flush();
        dbservice::utils::Logger::info(std::format("MAIN: Process ID: {}", getpid()));
        dbservice::utils::Logger::info(std::format("MAIN: Configuration file: {}", configFile));
        std::cout << "MAIN: Logger messages sent" << std::endl;
        std::cout.flush();

        // Create database service
        std::cout << "MAIN: Creating database service instance..." << std::endl;
        dbservice::utils::Logger::info("MAIN: Creating database service instance...");
        g_databaseService = std::make_shared<dbservice::DatabaseService>();
        std::cout << "MAIN: Database service instance created" << std::endl;
        dbservice::utils::Logger::info("MAIN: Database service instance created");

        // Load configuration
        std::cout << "MAIN: Loading configuration..." << std::endl;
        dbservice::utils::Logger::info("MAIN: Loading configuration...");
        auto configResult = g_databaseService->loadConfig(configFile);
        if (!configResult) {
            std::print(stderr, "Failed to load configuration: {}\n", configResult.error());
            dbservice::utils::Logger::error(std::format("MAIN: Failed to load configuration: {}", configResult.error()));
            return 1;
        }
        std::cout << "MAIN: Configuration loaded successfully" << std::endl;
        dbservice::utils::Logger::info("MAIN: Configuration loaded successfully");

        // Initialize database service
        std::cout << "MAIN: Initializing database service..." << std::endl;
        dbservice::utils::Logger::info("MAIN: Initializing database service...");
        auto initResult = g_databaseService->initialize();
        if (!initResult) {
            std::print(stderr, "Failed to initialize database service: {}\n", initResult.error());
            dbservice::utils::Logger::error(std::format("MAIN: Failed to initialize database service: {}", initResult.error()));
            return 1;
        }
        std::cout << "MAIN: Database service initialized successfully" << std::endl;
        dbservice::utils::Logger::info("MAIN: Database service initialized successfully");

        // Start database service
        std::cout << "MAIN: Starting database service..." << std::endl;
        dbservice::utils::Logger::info("MAIN: Starting database service...");
        auto startResult = g_databaseService->start();
        if (!startResult) {
            std::print(stderr, "Failed to start database service: {}\n", startResult.error());
            dbservice::utils::Logger::error(std::format("MAIN: Failed to start database service: {}", startResult.error()));
            return 1;
        }
        std::cout << "MAIN: Database service started successfully" << std::endl;

        dbservice::utils::Logger::info("Database service started successfully");

        // Wait for signal to stop
        while (g_running) {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }

        // Stop database service
        dbservice::utils::Logger::info("Stopping database service...");
        g_databaseService->stop();
        dbservice::utils::Logger::info("Database service stopped successfully");

        return 0;
    } catch (const std::exception& e) {
        std::cerr << "Exception: " << e.what() << std::endl;
        dbservice::utils::Logger::critical("Exception: " + std::string(e.what()));
        return 1;
    }
}
