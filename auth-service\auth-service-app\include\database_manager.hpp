﻿#pragma once
#include <string>
#include <memory>
#include <vector>
#include <optional>
#include <chrono>
#include <pqxx/pqxx>

class ConfigManager;

/**
 * @brief Database Manager with PostgreSQL Integration
 *
 * Provides database operations for:
 * - User management (create, authenticate, delete)
 * - JWT token storage and retrieval
 * - Token blacklist management
 * - Database transactions and error handling
 *
 * Step 6 of OAuth 2.0 Implementation
 */
class DatabaseManager {
public:
    /**
     * @brief User data structure
     */
    struct User {
        std::string user_id;  // UUID in database
        std::string username;
        std::string email;
        std::string password_hash;
        std::string salt;
        std::chrono::system_clock::time_point created_at;
        std::chrono::system_clock::time_point updated_at;
        std::chrono::system_clock::time_point last_login;
        bool is_active;
        bool email_verified;
        int failed_login_attempts;
    };

    /**
     * @brief JWT Token data structure
     */
    struct JWTTokenRecord {
        std::string token_id;  // UUID in database
        std::string user_id;   // UUID reference to auth_users
        std::string token_hash;
        std::string token_type; // "access" or "refresh"
        std::string jti;       // JWT ID
        std::chrono::system_clock::time_point expires_at;
        std::chrono::system_clock::time_point created_at;
        bool is_revoked;
    };

public:
    /**
     * @brief Constructor
     * @param config Configuration manager
     */
    explicit DatabaseManager(ConfigManager* config);

    /**
     * @brief Destructor
     */
    ~DatabaseManager();

    /**
     * @brief Initialize database connection
     */
    void initialize();

    /**
     * @brief Test database connection
     * @return true if connection is successful
     */
    bool test_connection();

    // User Management Operations
    /**
     * @brief Create a new user
     * @param username User's username
     * @param email User's email
     * @param password_hash Hashed password
     * @param salt Password salt
     * @return User UUID if successful, empty string if failed
     */
    std::string create_user(const std::string& username, const std::string& email,
                           const std::string& password_hash, const std::string& salt);

    /**
     * @brief Get user by username
     * @param username Username to search for
     * @return User data if found
     */
    std::optional<User> get_user_by_username(const std::string& username);

    /**
     * @brief Get user by ID
     * @param user_id User UUID to search for
     * @return User data if found
     */
    std::optional<User> get_user_by_id(const std::string& user_id);

    /**
     * @brief Update user's last login time
     * @param user_id User UUID
     * @return true if successful
     */
    bool update_user_last_login(const std::string& user_id);

    /**
     * @brief Delete user
     * @param user_id User UUID to delete
     * @return true if successful
     */
    bool delete_user(const std::string& user_id);

    // JWT Token Operations
    /**
     * @brief Store JWT token
     * @param user_id User UUID
     * @param token_hash SHA256 hash of the token
     * @param token_type "access" or "refresh"
     * @param jti JWT ID
     * @param expires_at Token expiration time
     * @return Token UUID if successful, empty string if failed
     */
    std::string store_jwt_token(const std::string& user_id, const std::string& token_hash,
                               const std::string& token_type, const std::string& jti,
                               const std::chrono::system_clock::time_point& expires_at);

    /**
     * @brief Get JWT token record
     * @param token_hash SHA256 hash of the token
     * @return Token record if found
     */
    std::optional<JWTTokenRecord> get_jwt_token(const std::string& token_hash);

    /**
     * @brief Revoke JWT token
     * @param token_hash SHA256 hash of the token
     * @return true if successful
     */
    bool revoke_jwt_token(const std::string& token_hash);

    /**
     * @brief Add token to blacklist
     * @param token_hash SHA256 hash of the token
     * @param expires_at Token expiration time
     * @return true if successful
     */
    bool blacklist_jwt_token(const std::string& token_hash,
                            const std::chrono::system_clock::time_point& expires_at);

    /**
     * @brief Check if token is blacklisted
     * @param token_hash SHA256 hash of the token
     * @return true if blacklisted
     */
    bool is_token_blacklisted(const std::string& token_hash);

    /**
     * @brief Clean up expired tokens
     * @return Number of tokens cleaned up
     */
    int cleanup_expired_tokens();

private:
    /**
     * @brief Get database connection string
     * @return PostgreSQL connection string
     */
    std::string get_connection_string();

    /**
     * @brief Convert pqxx::row to User struct
     * @param row Database row
     * @return User object
     */
    User row_to_user(const pqxx::row& row);

    /**
     * @brief Convert pqxx::row to JWTTokenRecord struct
     * @param row Database row
     * @return JWTTokenRecord object
     */
    JWTTokenRecord row_to_jwt_token(const pqxx::row& row);

private:
    ConfigManager* config_;
    std::unique_ptr<pqxx::connection> connection_;
    bool initialized_;
};
