﻿#pragma once
#include <string>
#include <memory>
#include <vector>
#include <optional>
#include <chrono>
#include <pqxx/pqxx>

class ConfigManager;

/**
 * @brief Database Manager with PostgreSQL Integration
 *
 * Provides database operations for:
 * - User management (create, authenticate, delete)
 * - JWT token storage and retrieval
 * - Token blacklist management
 * - Database transactions and error handling
 *
 * Step 6 of OAuth 2.0 Implementation
 */
class DatabaseManager {
public:
    /**
     * @brief User data structure
     */
    struct User {
        int id;
        std::string username;
        std::string email;
        std::string password_hash;
        std::chrono::system_clock::time_point created_at;
        std::chrono::system_clock::time_point updated_at;
        bool is_active;
    };

    /**
     * @brief JWT Token data structure
     */
    struct JWTTokenRecord {
        int id;
        int user_id;
        std::string token_hash;
        std::string token_type; // "access" or "refresh"
        std::chrono::system_clock::time_point expires_at;
        std::chrono::system_clock::time_point created_at;
        bool is_revoked;
    };

public:
    /**
     * @brief Constructor
     * @param config Configuration manager
     */
    explicit DatabaseManager(ConfigManager* config);

    /**
     * @brief Destructor
     */
    ~DatabaseManager();

    /**
     * @brief Initialize database connection
     */
    void initialize();

    /**
     * @brief Test database connection
     * @return true if connection is successful
     */
    bool test_connection();

    // User Management Operations
    /**
     * @brief Create a new user
     * @param username User's username
     * @param email User's email
     * @param password_hash Hashed password
     * @return User ID if successful, -1 if failed
     */
    int create_user(const std::string& username, const std::string& email, const std::string& password_hash);

    /**
     * @brief Get user by username
     * @param username Username to search for
     * @return User data if found
     */
    std::optional<User> get_user_by_username(const std::string& username);

    /**
     * @brief Get user by ID
     * @param user_id User ID to search for
     * @return User data if found
     */
    std::optional<User> get_user_by_id(int user_id);

    /**
     * @brief Update user's last login time
     * @param user_id User ID
     * @return true if successful
     */
    bool update_user_last_login(int user_id);

    /**
     * @brief Delete user
     * @param user_id User ID to delete
     * @return true if successful
     */
    bool delete_user(int user_id);

    // JWT Token Operations
    /**
     * @brief Store JWT token
     * @param user_id User ID
     * @param token_hash SHA256 hash of the token
     * @param token_type "access" or "refresh"
     * @param expires_at Token expiration time
     * @return Token record ID if successful, -1 if failed
     */
    int store_jwt_token(int user_id, const std::string& token_hash,
                       const std::string& token_type,
                       const std::chrono::system_clock::time_point& expires_at);

    /**
     * @brief Get JWT token record
     * @param token_hash SHA256 hash of the token
     * @return Token record if found
     */
    std::optional<JWTTokenRecord> get_jwt_token(const std::string& token_hash);

    /**
     * @brief Revoke JWT token
     * @param token_hash SHA256 hash of the token
     * @return true if successful
     */
    bool revoke_jwt_token(const std::string& token_hash);

    /**
     * @brief Add token to blacklist
     * @param token_hash SHA256 hash of the token
     * @param expires_at Token expiration time
     * @return true if successful
     */
    bool blacklist_jwt_token(const std::string& token_hash,
                            const std::chrono::system_clock::time_point& expires_at);

    /**
     * @brief Check if token is blacklisted
     * @param token_hash SHA256 hash of the token
     * @return true if blacklisted
     */
    bool is_token_blacklisted(const std::string& token_hash);

    /**
     * @brief Clean up expired tokens
     * @return Number of tokens cleaned up
     */
    int cleanup_expired_tokens();

private:
    /**
     * @brief Get database connection string
     * @return PostgreSQL connection string
     */
    std::string get_connection_string();

    /**
     * @brief Convert pqxx::row to User struct
     * @param row Database row
     * @return User object
     */
    User row_to_user(const pqxx::row& row);

    /**
     * @brief Convert pqxx::row to JWTTokenRecord struct
     * @param row Database row
     * @return JWTTokenRecord object
     */
    JWTTokenRecord row_to_jwt_token(const pqxx::row& row);

private:
    ConfigManager* config_;
    std::unique_ptr<pqxx::connection> connection_;
    bool initialized_;
};
