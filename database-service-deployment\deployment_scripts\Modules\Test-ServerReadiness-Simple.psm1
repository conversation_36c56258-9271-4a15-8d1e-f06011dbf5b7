# Test-ServerReadiness-Simple.psm1 - Simple module for testing server readiness

<#
.SYNOPSIS
    Provides functionality for testing server readiness for auth-service deployment.

.DESCRIPTION
    This module tests SSH connectivity, dependency availability, and server configuration
    for both database-service and auth-service applications.

.NOTES
    File Name      : Test-ServerReadiness-Simple.psm1
    Author         : Auth Service Deployment Team
    Prerequisite   : PowerShell 5.1 or later
    Copyright      : (c) 2025 Augment
#>

function Test-ServerReadiness {
    [CmdletBinding()]
    param()

    Write-Host "=== Testing Server Readiness ===" -ForegroundColor Cyan
    Write-Host ""

    # Check if configuration is loaded
    if ($null -eq $script:Config) {
        Write-Host "❌ Configuration is not loaded. Please load configuration first." -ForegroundColor Red
        return $false
    }

    # Determine service type
    $serviceName = if ($script:Config.project.name -eq "auth-service") { "auth-service" } else { "database-service" }
    $targetHost = $script:Config.ssh.host
    
    Write-Host "Service: $serviceName" -ForegroundColor Yellow
    Write-Host "Target Host: $targetHost" -ForegroundColor Yellow
    Write-Host ""

    # Test 1: SSH Connectivity
    Write-Host "1. Testing SSH connectivity..." -ForegroundColor Yellow
    
    try {
        $sshCommand = "ssh -i `"$($script:Config.ssh.key_path)`" -o StrictHostKeyChecking=no -o ConnectTimeout=10 $($script:Config.ssh.username)@$targetHost `"echo 'SSH connection successful'`""
        $result = Invoke-Expression $sshCommand
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  ✅ SSH connection successful" -ForegroundColor Green
        } else {
            Write-Host "  ❌ SSH connection failed" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "  ❌ SSH connection error: $_" -ForegroundColor Red
        return $false
    }

    # Test 2: Basic System Information
    Write-Host ""
    Write-Host "2. Checking system information..." -ForegroundColor Yellow
    
    try {
        $sysInfoCmd = "ssh -i `"$($script:Config.ssh.key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$targetHost `"uname -a && lsb_release -a 2>/dev/null || cat /etc/os-release`""
        $sysInfo = Invoke-Expression $sysInfoCmd
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  ✅ System information retrieved" -ForegroundColor Green
            Write-Host "  $($sysInfo.Split("`n")[0])" -ForegroundColor Gray
        } else {
            Write-Host "  ❌ Could not retrieve system information" -ForegroundColor Red
        }
    } catch {
        Write-Host "  ❌ System information error: $_" -ForegroundColor Red
    }

    # Test 3: Check Development Tools
    Write-Host ""
    Write-Host "3. Checking development tools..." -ForegroundColor Yellow
    
    $tools = @(
        @{ Name = "GCC"; Command = "g++ --version 2>/dev/null | head -1 || echo 'NOT_INSTALLED'" },
        @{ Name = "CMake"; Command = "cmake --version 2>/dev/null | head -1 || echo 'NOT_INSTALLED'" },
        @{ Name = "PostgreSQL Client"; Command = "psql --version 2>/dev/null || echo 'NOT_INSTALLED'" },
        @{ Name = "Git"; Command = "git --version 2>/dev/null || echo 'NOT_INSTALLED'" }
    )

    foreach ($tool in $tools) {
        try {
            $toolCmd = "ssh -i `"$($script:Config.ssh.key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$targetHost `"$($tool.Command)`""
            $toolResult = Invoke-Expression $toolCmd
            
            if ($LASTEXITCODE -eq 0 -and $toolResult -notlike "*NOT_INSTALLED*") {
                Write-Host "  ✅ $($tool.Name): $($toolResult.Split("`n")[0])" -ForegroundColor Green
            } else {
                Write-Host "  ❌ $($tool.Name): Not installed" -ForegroundColor Red
            }
        } catch {
            Write-Host "  ❌ $($tool.Name): Error checking" -ForegroundColor Red
        }
    }

    # Test 4: Check Auth-Service Specific Dependencies
    if ($serviceName -eq "auth-service") {
        Write-Host ""
        Write-Host "4. Checking auth-service specific dependencies..." -ForegroundColor Yellow
        
        $authDeps = @(
            @{ Name = "Boost Libraries"; Command = "dpkg -l | grep libboost | head -1 || echo 'NOT_INSTALLED'" },
            @{ Name = "OpenSSL Dev"; Command = "dpkg -s libssl-dev 2>/dev/null | grep Status || echo 'NOT_INSTALLED'" },
            @{ Name = "JSON Library"; Command = "dpkg -s nlohmann-json3-dev 2>/dev/null | grep Status || echo 'NOT_INSTALLED'" },
            @{ Name = "PostgreSQL Dev"; Command = "dpkg -s libpq-dev 2>/dev/null | grep Status || echo 'NOT_INSTALLED'" }
        )

        foreach ($dep in $authDeps) {
            try {
                $depCmd = "ssh -i `"$($script:Config.ssh.key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$targetHost `"$($dep.Command)`""
                $depResult = Invoke-Expression $depCmd
                
                if ($LASTEXITCODE -eq 0 -and $depResult -notlike "*NOT_INSTALLED*") {
                    Write-Host "  ✅ $($dep.Name): Available" -ForegroundColor Green
                } else {
                    Write-Host "  ❌ $($dep.Name): Not installed" -ForegroundColor Red
                }
            } catch {
                Write-Host "  ❌ $($dep.Name): Error checking" -ForegroundColor Red
            }
        }
    }

    # Test 5: Check Disk Space and System Resources
    Write-Host ""
    Write-Host "5. Checking system resources..." -ForegroundColor Yellow
    
    try {
        $resourceCmd = "ssh -i `"$($script:Config.ssh.key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$targetHost `"df -h / && free -h`""
        $resourceResult = Invoke-Expression $resourceCmd
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  ✅ System resources checked" -ForegroundColor Green
            $lines = $resourceResult.Split("`n")
            foreach ($line in $lines) {
                if ($line -like "*/*" -or $line -like "*Mem:*") {
                    Write-Host "  $line" -ForegroundColor Gray
                }
            }
        } else {
            Write-Host "  ❌ Could not check system resources" -ForegroundColor Red
        }
    } catch {
        Write-Host "  ❌ System resources error: $_" -ForegroundColor Red
    }

    Write-Host ""
    Write-Host "✅ Server readiness test completed!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Next steps:" -ForegroundColor Cyan
    Write-Host "- If dependencies are missing, run menu option 8 (Install Dependencies)" -ForegroundColor White
    Write-Host "- If all dependencies are available, run menu option 9 (Build Project)" -ForegroundColor White
    
    return $true
}

# Export the function
Export-ModuleMember -Function Test-ServerReadiness
