# SSHManager.psm1 - Improved SSH command handling for database service deployment

<#
.SYNOPSIS
    Provides robust SSH command execution and file transfer capabilities.

.DESCRIPTION
    This module implements secure and reliable SSH command execution and SCP file transfer
    functionality for the database service deployment scripts. It handles SSH key authentication,
    command execution, and file transfers with proper error handling and logging.

.NOTES
    File Name      : SSHManager.psm1
    Author         : Database Service Deployment Team
    Prerequisite   : PowerShell 5.1 or later, SSH client installed
    Copyright      : (c) 2025 Augment
#>

# Import the logger module if not already imported
if (-not (Get-Module -Name "Logger")) {
    try {
        Import-Module -Name "$PSScriptRoot\Logger\Logger.psm1" -Force -ErrorAction Stop
    } catch {
        # Create a fallback Write-Log function if Logger module fails to load
        function Write-Log {
            param(
                [Parameter(Mandatory=$true)]
                [string]$Message,
                [string]$Level = "Info",
                [string]$Component = "SSH"
            )
            if ([string]::IsNullOrEmpty($Message)) {
                $Message = "[Empty message]"
            }
            $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
            $color = switch ($Level) {
                "Error" { "Red" }
                "Warning" { "Yellow" }
                "Success" { "Green" }
                default { "White" }
            }
            Write-Host "[$timestamp] [$Level] [$Component] $Message" -ForegroundColor $color
        }

        Write-Host "Failed to load Logger module, using fallback logging: $_" -ForegroundColor Yellow
    }
}

# Log module loading
# (Removed Debug log for UI cleanliness)
# if (Get-Command -Name Write-Log -ErrorAction SilentlyContinue) {
#     Write-Log -Message "SSHManager.psm1 loaded" -Level "Debug" -Component "SSHManager"
# } else {
#     Write-Host "SSHManager.psm1 loaded (no logging available)" -ForegroundColor Yellow
# }

<#
.SYNOPSIS
    Executes a command on a remote server via SSH.

.DESCRIPTION
    Executes a command on a remote server using SSH with proper error handling and logging.
    Returns an object with the command output, error output, exit code, and success status.

.PARAMETER Command
    The command to execute on the remote server.

.PARAMETER HostName
    The hostname or IP address of the remote server.

.PARAMETER User
    The username to use for SSH authentication.

.PARAMETER Port
    The SSH port to connect to. Default is 22.

.PARAMETER KeyPath
    The path to the SSH private key file.

.PARAMETER Silent
    If specified, suppresses logging of command execution.

.PARAMETER Component
    The component name for logging. Default is "SSH".

.PARAMETER Timeout
    The timeout in seconds for the SSH command execution. Default is 60.

.EXAMPLE
    $result = Invoke-SSHCommand -Command "ls -la" -HostName "example.com" -User "username" -KeyPath "~/.ssh/id_rsa"
    if ($result.Success) {
        Write-Host "Command output: $($result.Output)"
    } else {
        Write-Host "Command failed: $($result.Error)"
    }

.RETURNS
    PSCustomObject with the following properties:
    - Success: Boolean indicating if the command was successful (exit code 0)
    - Output: The standard output of the command
    - Error: The standard error of the command (or error message if exception occurred)
    - ExitCode: The exit code of the command
#>
function Invoke-SSHCommand {
    [CmdletBinding()]
    [OutputType([PSCustomObject])]
    param (
        [Parameter(Mandatory=$true, Position=0)]
        [string]$Command,

        [Parameter(Mandatory=$true)]
        [string]$HostName,

        [Parameter(Mandatory=$true)]
        [string]$User,

        [Parameter(Mandatory=$false)]
        [int]$Port = 22,

        [Parameter(Mandatory=$false)]
        [string]$KeyPath,

        [Parameter(Mandatory=$false)]
        [switch]$Silent,

        [Parameter(Mandatory=$false)]
        [string]$Component = "SSH",

        [Parameter(Mandatory=$false)]
        [int]$Timeout = 60
    )
    
    # Log the function call
    # (Removed Debug log for UI cleanliness)
    # if (-not $Silent) {
    #     Write-Log -Message "Invoke-SSHCommand called with Command: $Command, Host: $HostName, User: $User, Port: $Port, KeyPath: $KeyPath" -Level "Debug" -Component "SSHManager"
    # }

    # Prepare the result object
    $result = [PSCustomObject]@{
        Success = $false
        Output = $null
        Error = $null
        ExitCode = -1
    }

    try {
        # Verify the key file exists if specified
        if (-not [string]::IsNullOrEmpty($KeyPath) -and -not (Test-Path -Path $KeyPath)) {
            $errorMsg = "SSH key file not found at: $KeyPath"
            if (-not $Silent -and -not [string]::IsNullOrEmpty($errorMsg)) {
                Write-Log -Message $errorMsg -Level "Error" -Component $Component
            }
            $result.Error = $errorMsg
            return $result
        }

        # Build the SSH command with arguments as an array to avoid quoting issues
        $sshCommand = "ssh"
        $sshArgs = @()
        
        # Add identity file if specified
        if (-not [string]::IsNullOrEmpty($KeyPath)) {
            $sshArgs += "-i"
            $sshArgs += $KeyPath
        }
        
        # Add port if not default
        if ($Port -ne 22) {
            $sshArgs += "-p"
            $sshArgs += $Port.ToString()
        }
        
        # Add common options
        $sshArgs += "-o"
        $sshArgs += "StrictHostKeyChecking=no"
        $sshArgs += "-o"
        $sshArgs += "UserKnownHostsFile=/dev/null"
        $sshArgs += "-o"
        $sshArgs += "LogLevel=ERROR"
        $sshArgs += "-o"
        $sshArgs += "BatchMode=yes"
        
        # Add destination
        $sshArgs += "${User}@${HostName}"
        
        # Add the command (properly quoted)
        $sshArgs += $Command
        
        # Log the command for debugging
        if (-not $Silent) {
            $cmdString = "$sshCommand $($sshArgs -join ' ')"
            if (-not [string]::IsNullOrEmpty($cmdString)) {
                Write-Log -Message "Executing SSH command: $cmdString" -Level "Info" -Component $Component
            }
        }
        
        # Set up process to capture output
        $pinfo = New-Object System.Diagnostics.ProcessStartInfo
        $pinfo.FileName = $sshCommand
        $pinfo.Arguments = $sshArgs
        $pinfo.RedirectStandardError = $true
        $pinfo.RedirectStandardOutput = $true
        $pinfo.UseShellExecute = $false
        $pinfo.CreateNoWindow = $true
        
        $process = New-Object System.Diagnostics.Process
        $process.StartInfo = $pinfo
        
        # Create string builders to capture output
        $stdout = New-Object System.Text.StringBuilder
        $stderr = New-Object System.Text.StringBuilder
        
        # Set up event handlers for output and error
        $stdoutEvent = Register-ObjectEvent -InputObject $process -EventName OutputDataReceived -Action {
            if ($null -ne $Event.SourceEventArgs.Data) {
                $stdout.AppendLine($Event.SourceEventArgs.Data) | Out-Null
            }
        }
        
        $stderrEvent = Register-ObjectEvent -InputObject $process -EventName ErrorDataReceived -Action {
            if ($null -ne $Event.SourceEventArgs.Data) {
                $stderr.AppendLine($Event.SourceEventArgs.Data) | Out-Null
            }
        }
        
        # Start the process
        $process.Start() | Out-Null
        $process.BeginOutputReadLine()
        $process.BeginErrorReadLine()
        
        # Wait for the process to complete or timeout
        if (-not $process.WaitForExit($Timeout * 1000)) {
            # Process timed out
            try {
                $process.Kill()
            } catch {
                # Process may have completed between checking and attempting to kill
            }
            
            # Clean up event handlers
            Unregister-Event -SourceIdentifier $stdoutEvent.Name
            Unregister-Event -SourceIdentifier $stderrEvent.Name
            
            $errorMsg = "SSH command timed out after $Timeout seconds"
            if (-not $Silent -and -not [string]::IsNullOrEmpty($errorMsg)) {
                Write-Log -Message $errorMsg -Level "Error" -Component $Component
            }
            
            $result.Error = $errorMsg
            return $result
        }
        
        # Clean up event handlers
        Unregister-Event -SourceIdentifier $stdoutEvent.Name
        Unregister-Event -SourceIdentifier $stderrEvent.Name
        
        # Capture output and exit code
        $outputText = $stdout.ToString().Trim()
        $errorText = $stderr.ToString().Trim()
        $exitCode = $process.ExitCode
        
        # Update result object
        $result.Output = $outputText
        $result.Error = $errorText
        $result.ExitCode = $exitCode
        $result.Success = ($exitCode -eq 0)
        
        # Log results
        # (Removed Debug log for UI cleanliness)
        # if (-not $Silent) {
        #     if ($result.Success) {
        #         Write-Log -Message "SSH command executed successfully" -Level "Debug" -Component $Component
        #     } else {
        #         Write-Log -Message "SSH command failed with exit code: $exitCode" -Level "Error" -Component $Component
        #         if (-not [string]::IsNullOrEmpty($errorText)) {
        #             Write-Log -Message "Error output: $errorText" -Level "Error" -Component $Component
        #         }
        #     }
        # }
        
        return $result
    } catch {
        $errorMsg = "Exception in SSH command execution: $_"
        if (-not $Silent -and -not [string]::IsNullOrEmpty($errorMsg)) {
            Write-Log -Message $errorMsg -Level "Error" -Component $Component
        }
        $result.Error = $errorMsg
        return $result
    }
}

<#
.SYNOPSIS
    Tests SSH connection to a remote server.

.DESCRIPTION
    Tests SSH connection to a remote server using the specified credentials.
    Returns a boolean indicating success or failure.

.PARAMETER HostName
    The hostname or IP address of the remote server.

.PARAMETER User
    The username to use for SSH authentication.

.PARAMETER Port
    The SSH port to connect to. Default is 22.

.PARAMETER KeyPath
    The path to the SSH private key file.

.PARAMETER Silent
    If specified, suppresses logging of connection test.

.PARAMETER Component
    The component name for logging. Default is "SSH".

.PARAMETER Timeout
    The timeout in seconds for the SSH connection test. Default is 10.

.EXAMPLE
    $connected = Test-SSHConnection -HostName "example.com" -User "username"
    if ($connected) {
        Write-Host "SSH connection successful"
    } else {
        Write-Host "SSH connection failed"
    }

.RETURNS
    Boolean indicating success or failure.
#>
function Test-SSHConnection {
    [CmdletBinding()]
    [OutputType([bool])]
    param (
        [Parameter(Mandatory=$true)]
        [string]$HostName,

        [Parameter(Mandatory=$true)]
        [string]$User,

        [Parameter(Mandatory=$false)]
        [int]$Port = 22,

        [Parameter(Mandatory=$false)]
        [string]$KeyPath,

        [Parameter(Mandatory=$false)]
        [switch]$Silent,

        [Parameter(Mandatory=$false)]
        [int]$Timeout = 10,

        [Parameter(Mandatory=$false)]
        [string]$Component = "SSH"
    )
    
    if (-not $Silent) {
        $testMsg = "Testing SSH connection to $User@$($HostName):$Port"
        if (-not [string]::IsNullOrEmpty($testMsg)) {
            Write-Log -Message $testMsg -Level "Info" -Component $Component
        }
    }
    
    try {
        # Use a simple echo command to test the connection
        $result = Invoke-SSHCommand -Command "echo 'Connection successful'" -HostName $HostName -User $User -Port $Port -KeyPath $KeyPath -Silent:$Silent -Timeout $Timeout -Component $Component

        # Check the success based on ExitCode first (most reliable)
        # Then try to match the output string as a fallback
        if ($result.Success) {
            if (-not $Silent) {
                Write-Log -Message "SSH connection successful" -Level "Success" -Component $Component
            }
            return $true
        } else {
            if (-not $Silent) {
                Write-Log -Message "SSH connection failed" -Level "Error" -Component $Component
                if (-not [string]::IsNullOrEmpty($result.Error)) {
                    $errorMsg = "Error: $($result.Error)"
                    if (-not [string]::IsNullOrEmpty($errorMsg)) {
                        Write-Log -Message $errorMsg -Level "Error" -Component $Component
                    }
                }
            }
            return $false
        }
    } catch {
        if (-not $Silent) {
            $exceptionMsg = "Exception in SSH connection test: $_"
            if (-not [string]::IsNullOrEmpty($exceptionMsg)) {
                Write-Log -Message $exceptionMsg -Level "Error" -Component $Component
            }
        }
        return $false
    }
}

# Export functions
Export-ModuleMember -Function Invoke-SSHCommand, Test-SSHConnection
