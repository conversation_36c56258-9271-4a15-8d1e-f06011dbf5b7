{{ ... }}
# Get Service Status Module

# Import common module
Import-Module -Name (Join-Path $PSScriptRoot '..\Common.psm1') -Force
# Import Logger module
Import-Module -Name (Join-Path $PSScriptRoot 'Logger/Logger.psm1') -Force

function Get-ServiceStatus {
    Clear-Host
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host "               Service Status                         " -ForegroundColor Blue
    Write-Host "========================================================" -ForegroundColor Blue
    Write-Host 
    
    if ($null -eq $Config -or $null -eq $Config.ssh) {
        Write-Host "SSH configuration is not set up. Please configure SSH settings first." -ForegroundColor Red
        Wait-ForUser
        & "$PSScriptRoot\Set-Environment.ps1"
        return
    }
    
    # Get service name
    $serviceName = $Config.service.name
    
    # Check if the service is installed
    $checkServiceCmd = "systemctl list-unit-files | grep $serviceName.service | wc -l"
    $serviceExists = Invoke-RemoteCommand -Command $checkServiceCmd -Silent
    
    if ($serviceExists -eq 0) {
        Write-Host "Service $serviceName is not installed." -ForegroundColor Red
        Write-Host "Please install the service first." -ForegroundColor Yellow
        Wait-ForUser
        & "$PSScriptRoot\Install-Service.ps1"
        return
    }
    
    try {
        # Get service status
        Write-Host "Checking status of service $serviceName..." -ForegroundColor Yellow

        # Use direct SSH command since Invoke-RemoteCommand might not return output properly
        $sshArgs = @(
            "-i", "C:\Users\<USER>\.ssh\id_rsa",
            "-o", "StrictHostKeyChecking=no",
            "-o", "UserKnownHostsFile=/dev/null",
            "-o", "LogLevel=ERROR",
            "-o", "BatchMode=yes",
            "<EMAIL>",
            "systemctl status $serviceName --no-pager"
        )

        Write-Host
        Write-Host "Service Status:" -ForegroundColor Cyan
        try {
            $statusResult = & ssh @sshArgs 2>&1
            if ($statusResult -and $statusResult.Count -gt 0) {
                Write-Host ($statusResult -join "`n") -ForegroundColor Gray
            } else {
                Write-Host "No status information available." -ForegroundColor Red
            }
        } catch {
            Write-Host "Failed to get service status: $_" -ForegroundColor Red
        }

        # Get service logs
        Write-Host
        Write-Host "Recent Service Logs:" -ForegroundColor Cyan
        $logsArgs = @(
            "-i", "C:\Users\<USER>\.ssh\id_rsa",
            "-o", "StrictHostKeyChecking=no",
            "-o", "UserKnownHostsFile=/dev/null",
            "-o", "LogLevel=ERROR",
            "-o", "BatchMode=yes",
            "<EMAIL>",
            "journalctl -u $serviceName -n 20 --no-pager"
        )

        try {
            $logsResult = & ssh @logsArgs 2>&1
            if ($logsResult -and $logsResult.Count -gt 0) {
                Write-Host ($logsResult -join "`n") -ForegroundColor Gray
            } else {
                Write-Host "No log information available." -ForegroundColor Red
            }
        } catch {
            Write-Host "Failed to get service logs: $_" -ForegroundColor Red
        }

        # Check if PostgreSQL is running
        Write-Host
        Write-Host "PostgreSQL Status:" -ForegroundColor Cyan
        $pgArgs = @(
            "-i", "C:\Users\<USER>\.ssh\id_rsa",
            "-o", "StrictHostKeyChecking=no",
            "-o", "UserKnownHostsFile=/dev/null",
            "-o", "LogLevel=ERROR",
            "-o", "BatchMode=yes",
            "<EMAIL>",
            "systemctl status postgresql --no-pager"
        )

        try {
            $pgResult = & ssh @pgArgs 2>&1
            if ($pgResult -and $pgResult.Count -gt 0) {
                Write-Host ($pgResult -join "`n") -ForegroundColor Gray
            } else {
                Write-Host "No PostgreSQL status information available." -ForegroundColor Red
            }
        } catch {
            Write-Host "Failed to get PostgreSQL status: $_" -ForegroundColor Red
        }

        # Additional service information
        Write-Host
        Write-Host "Service Process Information:" -ForegroundColor Cyan
        $processArgs = @(
            "-i", "C:\Users\<USER>\.ssh\id_rsa",
            "-o", "StrictHostKeyChecking=no",
            "-o", "UserKnownHostsFile=/dev/null",
            "-o", "LogLevel=ERROR",
            "-o", "BatchMode=yes",
            "<EMAIL>",
            "ps aux | grep database-service | grep -v grep"
        )

        try {
            $processResult = & ssh @processArgs 2>&1
            if ($processResult -and $processResult.Count -gt 0 -and $processResult -notlike "*grep*") {
                Write-Host ($processResult -join "`n") -ForegroundColor Gray
            } else {
                Write-Host "No database-service processes found." -ForegroundColor Yellow
            }
        } catch {
            Write-Host "Failed to get process information: $_" -ForegroundColor Red
        }

        # Port status
        Write-Host
        Write-Host "Port 8081 Status:" -ForegroundColor Cyan
        $portArgs = @(
            "-i", "C:\Users\<USER>\.ssh\id_rsa",
            "-o", "StrictHostKeyChecking=no",
            "-o", "UserKnownHostsFile=/dev/null",
            "-o", "LogLevel=ERROR",
            "-o", "BatchMode=yes",
            "<EMAIL>",
            "netstat -tuln | grep :8081"
        )

        try {
            $portResult = & ssh @portArgs 2>&1
            if ($portResult -and $portResult.Count -gt 0) {
                Write-Host ($portResult -join "`n") -ForegroundColor Gray
            } else {
                Write-Host "Port 8081 is not listening." -ForegroundColor Yellow
            }
        } catch {
            Write-Host "Failed to get port information: $_" -ForegroundColor Red
        }

    } catch {
        Write-Host "Error checking service status: $_" -ForegroundColor Red
        Write-Host "Exception details: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    Write-Host 
    Write-Host "Press any key to return to the main menu..."
    Wait-ForUser
    
    Show-MainMenu
}

# Export the main function
Export-ModuleMember -Function Get-ServiceStatus
