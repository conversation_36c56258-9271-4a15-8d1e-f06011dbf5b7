#pragma once
#include <vector>
#include <queue>
#include <memory>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <future>
#include <functional>
#include <stdexcept>
#include <atomic>

namespace dbservice::utils {

/**
 * @class ThreadPool
 * @brief A thread pool implementation for handling concurrent tasks
 */
class ThreadPool {
public:
    /**
     * @brief Constructor
     * @param threads Number of worker threads to create
     */
    explicit ThreadPool(size_t threads);

    /**
     * @brief Destructor - stops all threads and waits for completion
     */
    ~ThreadPool();

    /**
     * @brief Enqueue a task for execution
     * @tparam F Function type
     * @tparam Args Argument types
     * @param f Function to execute
     * @param args Arguments to pass to the function
     * @return Future that will contain the result
     */
    template<class F, class... Args>
    auto enqueue(F&& f, Args&&... args) 
        -> std::future<typename std::result_of<F(Args...)>::type>;

    /**
     * @brief Get the number of worker threads
     * @return Number of threads
     */
    size_t getThreadCount() const;

    /**
     * @brief Get the number of pending tasks
     * @return Number of tasks in queue
     */
    size_t getPendingTaskCount() const;

    /**
     * @brief Get the number of active (running) tasks
     * @return Number of active tasks
     */
    size_t getActiveTaskCount() const;

    /**
     * @brief Check if the thread pool is shutting down
     * @return True if shutting down
     */
    bool isShuttingDown() const;

private:
    // Worker threads
    std::vector<std::thread> workers_;
    
    // Task queue
    std::queue<std::function<void()>> tasks_;
    
    // Synchronization
    mutable std::mutex queueMutex_;
    std::condition_variable condition_;
    
    // State
    std::atomic<bool> stop_;
    std::atomic<size_t> activeTasks_;
};

// Template implementation
template<class F, class... Args>
auto ThreadPool::enqueue(F&& f, Args&&... args) 
    -> std::future<typename std::result_of<F(Args...)>::type> {
    
    using return_type = typename std::result_of<F(Args...)>::type;

    auto task = std::make_shared<std::packaged_task<return_type()>>(
        std::bind(std::forward<F>(f), std::forward<Args>(args)...)
    );

    std::future<return_type> res = task->get_future();
    
    {
        std::unique_lock<std::mutex> lock(queueMutex_);

        // Don't allow enqueueing after stopping the pool
        if (stop_) {
            throw std::runtime_error("enqueue on stopped ThreadPool");
        }

        tasks_.emplace([task]() { (*task)(); });
    }
    
    condition_.notify_one();
    return res;
}

} // namespace dbservice::utils
