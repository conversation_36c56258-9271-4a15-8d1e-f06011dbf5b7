{{ ... }}
# Initialize Database Module

# Import common module
Import-Module -Name (Join-Path $PSScriptRoot '..\Common.psm1') -Force
# Import Logger module
Import-Module -Name (Join-Path $PSScriptRoot 'Logger/Logger.psm1') -Force

function Initialize-Database {
    Clear-Host
    # Enable UI Mode for menu display
    Enable-UIMode
    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message "               Initialize Database                    " -Level "UI" -ForegroundColor Blue
    Write-Log -Message "========================================================" -Level "UI" -ForegroundColor Blue
    Write-Log -Message " " -Level "UI"
    # Disable UI Mode after menu display
    Disable-UIMode
    if ($null -eq $Config -or $null -eq $Config.ssh) {
        Write-Log -Message "SSH configuration is not set up. Please configure SSH settings first." -Level "Error" -Component "Database"
        Wait-ForUser
        & "$PSScriptRoot\Set-Environment.ps1"
        return
    }
    # Check if PostgreSQL is installed and running
    # Try multiple possible PostgreSQL service names
    $pgServiceNames = @("postgresql", "postgresql.service", "postgres", "postgresql-17", "postgresql-16", "postgresql-15")
    $pgRunning = $false
    $activeService = ""

    foreach ($serviceName in $pgServiceNames) {
        $checkPgCmd = "systemctl is-active $serviceName 2>/dev/null"
        $pgStatus = Invoke-RemoteCommand -Command $checkPgCmd -Silent
        if ($pgStatus -eq "active") {
            $pgRunning = $true
            $activeService = $serviceName
            break
        }
    }

    if (-not $pgRunning) {
        Write-Log -Message "PostgreSQL is not running." -Level "Error" -Component "Database"
        Write-Log -Message "Checked services: $($pgServiceNames -join ', ')" -Level "Info" -Component "Database"

        # Try to check if PostgreSQL is running via process check
        $pgProcessCmd = "pgrep -f postgres | wc -l"
        $pgProcessCount = Invoke-RemoteCommand -Command $pgProcessCmd -Silent
        if ([int]$pgProcessCount -gt 0) {
            Write-Log -Message "PostgreSQL processes found ($pgProcessCount), but systemctl service not active." -Level "Warning" -Component "Database"
            Write-Log -Message "Continuing with database initialization..." -Level "Info" -Component "Database"
        } else {
            Write-Log -Message "No PostgreSQL processes found via pgrep. Trying direct connection test..." -Level "Warning" -Component "Database"
            # Try direct PostgreSQL connection test
            $pgDirectTestCmd = "sudo -u postgres psql -c 'SELECT 1;' 2>/dev/null | grep -q '1' && echo 'CONNECTED' || echo 'FAILED'"
            $pgDirectTest = Invoke-RemoteCommand -Command $pgDirectTestCmd -Silent
            if ($pgDirectTest -eq "CONNECTED") {
                Write-Log -Message "PostgreSQL is accessible via direct connection. Continuing with database initialization..." -Level "Info" -Component "Database"
            } else {
                Write-Log -Message "PostgreSQL connection test failed. Attempting to proceed anyway..." -Level "Warning" -Component "Database"
                Write-Log -Message "If database operations fail, please ensure PostgreSQL is installed and running." -Level "Warning" -Component "Database"
            }
        }
    } else {
        Write-Log -Message "PostgreSQL is running (service: $activeService)" -Level "Success" -Component "Database"
    }

    # Test PostgreSQL connectivity
    Write-Log -Message "Testing PostgreSQL connectivity..." -Level "Info" -Component "Database"
    $pgConnectCmd = "sudo -u postgres psql -c 'SELECT version();' 2>&1"
    $pgConnectResult = Invoke-RemoteCommand -Command $pgConnectCmd -Silent
    if ($pgConnectResult -match "PostgreSQL") {
        Write-Log -Message "PostgreSQL connectivity test successful" -Level "Success" -Component "Database"
        # Extract version info
        $versionLine = ($pgConnectResult -split "`n" | Where-Object { $_ -match "PostgreSQL" })[0]
        if ($versionLine) {
            Write-Log -Message "PostgreSQL version: $($versionLine.Trim())" -Level "Info" -Component "Database"
        }
    } else {
        Write-Log -Message "PostgreSQL connectivity test failed: $pgConnectResult" -Level "Error" -Component "Database"
        Write-Log -Message "Please check PostgreSQL installation and permissions." -Level "Warning" -Component "Database"
        Write-Log -Message "Attempting to proceed with database initialization anyway..." -Level "Warning" -Component "Database"
        Wait-ForUser
    }

    # Get database configuration
    $dbName = if ($Config.database.name) { $Config.database.name } else { "database_service" }
    $dbUser = if ($Config.database.user) { $Config.database.user } else { "database_service_user" }
    $dbPassword = $Config.database.password

    Write-Log -Message "Database configuration:" -Level "Info" -Component "Database"
    Write-Log -Message "  Database: $dbName" -Level "Info" -Component "Database"
    Write-Log -Message "  User: $dbUser" -Level "Info" -Component "Database"
    if ([string]::IsNullOrWhiteSpace($dbPassword)) {
        Write-Log -Message "Database password is not set." -Level "Error" -Component "Database"
        $password = Read-Host "Enter database password" -AsSecureString
        $plainPassword = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($password))
        if ([string]::IsNullOrWhiteSpace($plainPassword)) {
            Write-Log -Message "Password cannot be empty." -Level "Error" -Component "Database"
            Wait-ForUser
            Show-MainMenu
            return
        }
        $Config.database.password = $plainPassword
        Save-Configuration
        $dbPassword = $plainPassword
    }
    # Check if the database user exists
    $checkUserCmd = @"
sudo -u postgres psql -tAc "SELECT 1 FROM pg_roles WHERE rolname='$dbUser'" | wc -l
"@
    $userExists = Invoke-RemoteCommand -Command $checkUserCmd -Silent
    if ([string]::IsNullOrWhiteSpace($userExists) -or $userExists.Trim() -eq "0") {
        Write-Log -Message "Creating database user $dbUser..." -Level "Info" -Component "Database"
        $createUserCmd = @"
sudo -u postgres psql -c "CREATE USER $dbUser WITH PASSWORD '$dbPassword'" 2>&1
"@
        $result = Invoke-RemoteCommand -Command $createUserCmd
        if ($result -match "ERROR") {
            Write-Log -Message "Failed to create database user: $result" -Level "Error" -Component "Database"
            Wait-ForUser
            Show-MainMenu
            return
        } else {
            Write-Log -Message "Database user $dbUser created successfully!" -Level "Success" -Component "Database"
        }
    } else {
        Write-Log -Message "Database user $dbUser already exists." -Level "Info" -Component "Database"
        # Update the user's password
        $updatePasswordCmd = @"
sudo -u postgres psql -c "ALTER USER $dbUser WITH PASSWORD '$dbPassword'" 2>&1
"@
        $result = Invoke-RemoteCommand -Command $updatePasswordCmd
        if ($result -match "ERROR") {
            Write-Log -Message "Failed to update user password: $result" -Level "Error" -Component "Database"
        } else {
            Write-Log -Message "Updated password for user $dbUser." -Level "Success" -Component "Database"
        }
    }

    # Check if the database exists
    $checkDbCmd = @"
sudo -u postgres psql -tAc "SELECT 1 FROM pg_database WHERE datname='$dbName'" | wc -l
"@
    $dbExists = Invoke-RemoteCommand -Command $checkDbCmd -Silent
    if ([string]::IsNullOrWhiteSpace($dbExists) -or $dbExists.Trim() -eq "0") {
        Write-Log -Message "Creating database $dbName..." -Level "Info" -Component "Database"
        $createDbCmd = @"
sudo -u postgres psql -c "CREATE DATABASE $dbName OWNER $dbUser" 2>&1
"@
        $result = Invoke-RemoteCommand -Command $createDbCmd
        if ($result -match "ERROR") {
            Write-Log -Message "Failed to create database: $result" -Level "Error" -Component "Database"
            Wait-ForUser
            Show-MainMenu
            return
        } else {
            Write-Log -Message "Database $dbName created successfully!" -Level "Success" -Component "Database"
        }
    } else {
        Write-Log -Message "Database $dbName already exists." -Level "Info" -Component "Database"
    }

    # Initialize database schema
    Write-Log -Message "Initializing database schema..." -Level "Info" -Component "Database"
    $installDir = $Config.service.install_dir
    if ([string]::IsNullOrEmpty($installDir)) {
        $installDir = "/opt/database-service"
    }

    # List of schema files to run in order
    # NOTE: Only Database Service schema should be applied here
    # Other applications (Git Dashboard, Logging) should initialize their own databases separately
    $schemaFiles = @(
        "database_service_schema.sql"
    )

    foreach ($schemaFileName in $schemaFiles) {
        # Try both sql and schemas directories
        $schemaFile = "$installDir/sql/$schemaFileName"
        $schemaFileAlt = "$installDir/schemas/$schemaFileName"
        Write-Log -Message "Running schema file: $schemaFileName" -Level "Info" -Component "Database"

        $runSchemaCmd = @"
if [ -f $schemaFile ]; then
    echo "Running schema file: $schemaFile"
    sudo -u postgres psql -d $dbName -f $schemaFile 2>&1
elif [ -f $schemaFileAlt ]; then
    echo "Running schema file: $schemaFileAlt"
    sudo -u postgres psql -d $dbName -f $schemaFileAlt 2>&1
else
    echo "Schema file not found in either $schemaFile or $schemaFileAlt"
fi
"@
        $result = Invoke-RemoteCommand -Command $runSchemaCmd
        if ($result -match "ERROR") {
            Write-Log -Message "Failed to run ${schemaFileName}: $result" -Level "Error" -Component "Database"
        } else {
            Write-Log -Message "Successfully ran $schemaFileName" -Level "Success" -Component "Database"
        }
    }

    # Grant permissions to database user
    Write-Log -Message "Granting permissions to database user..." -Level "Info" -Component "Database"
    $grantPermissionsCmd = @"
sudo -u postgres psql -d $dbName -c "GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO $dbUser;" 2>&1 &&
sudo -u postgres psql -d $dbName -c "GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO $dbUser;" 2>&1 &&
sudo -u postgres psql -d $dbName -c "ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO $dbUser;" 2>&1 &&
sudo -u postgres psql -d $dbName -c "ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO $dbUser;" 2>&1
"@
    $permResult = Invoke-RemoteCommand -Command $grantPermissionsCmd
    if ($permResult -match "ERROR") {
        Write-Log -Message "Failed to grant permissions: $permResult" -Level "Error" -Component "Database"
    } else {
        Write-Log -Message "Database permissions granted successfully!" -Level "Success" -Component "Database"
    }

    Write-Log -Message "Database initialization completed!" -Level "Success" -Component "Database"
    Wait-ForUser
}

Export-ModuleMember -Function Initialize-Database
