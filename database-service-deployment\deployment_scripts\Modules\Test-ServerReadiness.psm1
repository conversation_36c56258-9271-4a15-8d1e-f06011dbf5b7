# Test-ServerReadiness-AuthService.psm1 - Auth-Service specific server readiness testing

<#
.SYNOPSIS
    Provides functionality for testing server readiness specifically for auth-service deployment.

.DESCRIPTION
    This module tests SSH connectivity, dependency availability, and server configuration
    specifically for the auth-service C++23 application. It does NOT check database-service
    requirements and focuses only on auth-service needs.

.NOTES
    File Name      : Test-ServerReadiness-AuthService.psm1
    Author         : Auth Service Deployment Team
    Prerequisite   : PowerShell 5.1 or later
    Copyright      : (c) 2025 Augment
#>

function Test-ServerReadiness {
    [CmdletBinding()]
    param()

    Write-Host "=== Testing Auth-Service Server Readiness ===" -ForegroundColor Cyan
    Write-Host ""

    # Load auth-service configuration
    if ($null -eq $script:Config) {
        Write-Host "Loading auth-service configuration..." -ForegroundColor Yellow
        
        $authConfigPath = Join-Path -Path $PSScriptRoot -ChildPath "..\config\auth-service-development.json"
        
        if (-not (Test-Path $authConfigPath)) {
            Write-Host "❌ Auth-service configuration not found: $authConfigPath" -ForegroundColor Red
            return $false
        }
        
        try {
            $configContent = Get-Content -Path $authConfigPath -Raw -ErrorAction Stop
            $script:Config = ConvertFrom-Json -InputObject $configContent -ErrorAction Stop
            Write-Host "✅ Auth-service configuration loaded successfully" -ForegroundColor Green
        } catch {
            Write-Host "❌ Failed to load auth-service configuration: $_" -ForegroundColor Red
            return $false
        }
    }

    # Auth-service specific settings
    $serviceName = "auth-service"
    $targetHost = $script:Config.ssh.host
    $installDir = "/opt/auth-service"
    $servicePort = 8082
    
    Write-Host "Service: $serviceName" -ForegroundColor Yellow
    Write-Host "Target Host: $targetHost" -ForegroundColor Yellow
    Write-Host "Install Directory: $installDir" -ForegroundColor Yellow
    Write-Host "Service Port: $servicePort" -ForegroundColor Yellow
    Write-Host ""

    # Test 1: SSH Connectivity
    Write-Host "1. Testing SSH connectivity to $targetHost..." -ForegroundColor Yellow
    
    try {
        $sshCommand = "ssh -i `"$($script:Config.ssh.key_path)`" -o StrictHostKeyChecking=no -o ConnectTimeout=10 $($script:Config.ssh.username)@$targetHost `"echo 'SSH connection successful'`""
        $result = Invoke-Expression $sshCommand
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  ✅ SSH connection successful" -ForegroundColor Green
        } else {
            Write-Host "  ❌ SSH connection failed" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "  ❌ SSH connection error: $_" -ForegroundColor Red
        return $false
    }

    # Test 2: System Information
    Write-Host ""
    Write-Host "2. Checking system information..." -ForegroundColor Yellow
    
    try {
        $sysInfoCmd = "ssh -i `"$($script:Config.ssh.key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$targetHost `"uname -a && lsb_release -a 2>/dev/null || cat /etc/os-release`""
        $sysInfo = Invoke-Expression $sysInfoCmd
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  ✅ System information retrieved" -ForegroundColor Green
            Write-Host "  $($sysInfo.Split("`n")[0])" -ForegroundColor Gray
        } else {
            Write-Host "  ❌ Could not retrieve system information" -ForegroundColor Red
        }
    } catch {
        Write-Host "  ❌ System information error: $_" -ForegroundColor Red
    }

    # Test 3: Auth-Service Required Directories
    Write-Host ""
    Write-Host "3. Checking auth-service directories..." -ForegroundColor Yellow
    
    $authDirs = @(
        "/opt/auth-service",
        "/opt/auth-service/bin",
        "/opt/auth-service/config",
        "/opt/auth-service/logs"
    )
    
    foreach ($dir in $authDirs) {
        try {
            $dirCheckCmd = "ssh -i `"$($script:Config.ssh.key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$targetHost `"test -d $dir && echo 'EXISTS' || echo 'NOT_EXISTS'`""
            $dirResult = Invoke-Expression $dirCheckCmd
            
            if ($LASTEXITCODE -eq 0 -and $dirResult -like "*EXISTS*") {
                Write-Host "  ✅ Directory exists: $dir" -ForegroundColor Green
            } else {
                Write-Host "  ❌ Directory missing: $dir" -ForegroundColor Red
            }
        } catch {
            Write-Host "  ❌ Error checking directory $dir`: $_" -ForegroundColor Red
        }
    }

    # Test 4: C++23 Development Tools
    Write-Host ""
    Write-Host "4. Checking C++23 development tools..." -ForegroundColor Yellow
    
    $devTools = @(
        @{ Name = "GCC 14+"; Command = "g++-14 --version 2>/dev/null | head -1 || echo 'NOT_INSTALLED'" },
        @{ Name = "CMake"; Command = "cmake --version 2>/dev/null | head -1 || echo 'NOT_INSTALLED'" },
        @{ Name = "Make"; Command = "make --version 2>/dev/null | head -1 || echo 'NOT_INSTALLED'" }
    )

    foreach ($tool in $devTools) {
        try {
            $toolCmd = "ssh -i `"$($script:Config.ssh.key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$targetHost `"$($tool.Command)`""
            $toolResult = Invoke-Expression $toolCmd
            
            if ($LASTEXITCODE -eq 0 -and $toolResult -notlike "*NOT_INSTALLED*") {
                Write-Host "  ✅ $($tool.Name): $($toolResult.Split("`n")[0])" -ForegroundColor Green
            } else {
                Write-Host "  ❌ $($tool.Name): Not installed" -ForegroundColor Red
            }
        } catch {
            Write-Host "  ❌ $($tool.Name): Error checking" -ForegroundColor Red
        }
    }

    # Test 5: Auth-Service Specific Libraries
    Write-Host ""
    Write-Host "5. Checking auth-service specific libraries..." -ForegroundColor Yellow
    
    $authLibs = @(
        @{ Name = "Boost Libraries"; Command = "dpkg -l | grep libboost-dev | head -1 || echo 'NOT_INSTALLED'" },
        @{ Name = "OpenSSL Development"; Command = "dpkg -s libssl-dev 2>/dev/null | grep Status || echo 'NOT_INSTALLED'" },
        @{ Name = "JSON Library"; Command = "dpkg -s nlohmann-json3-dev 2>/dev/null | grep Status || echo 'NOT_INSTALLED'" },
        @{ Name = "PostgreSQL Client Dev"; Command = "dpkg -s libpq-dev 2>/dev/null | grep Status || echo 'NOT_INSTALLED'" },
        @{ Name = "PostgreSQL C++ (pqxx)"; Command = "dpkg -s libpqxx-dev 2>/dev/null | grep Status || echo 'NOT_INSTALLED'" }
    )

    foreach ($lib in $authLibs) {
        try {
            $libCmd = "ssh -i `"$($script:Config.ssh.key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$targetHost `"$($lib.Command)`""
            $libResult = Invoke-Expression $libCmd
            
            if ($LASTEXITCODE -eq 0 -and $libResult -notlike "*NOT_INSTALLED*") {
                Write-Host "  ✅ $($lib.Name): Available" -ForegroundColor Green
            } else {
                Write-Host "  ❌ $($lib.Name): Not installed" -ForegroundColor Red
            }
        } catch {
            Write-Host "  ❌ $($lib.Name): Error checking" -ForegroundColor Red
        }
    }

    # Test 6: PostgreSQL Server
    Write-Host ""
    Write-Host "6. Checking PostgreSQL server..." -ForegroundColor Yellow
    
    try {
        $pgStatusCmd = "ssh -i `"$($script:Config.ssh.key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$targetHost `"systemctl is-active postgresql 2>/dev/null || echo 'NOT_RUNNING'`""
        $pgStatus = Invoke-Expression $pgStatusCmd
        
        if ($LASTEXITCODE -eq 0 -and $pgStatus -like "*active*") {
            Write-Host "  ✅ PostgreSQL server is running" -ForegroundColor Green
        } else {
            Write-Host "  ❌ PostgreSQL server is not running" -ForegroundColor Red
        }
        
        # Check PostgreSQL version
        $pgVersionCmd = "ssh -i `"$($script:Config.ssh.key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$targetHost `"psql --version 2>/dev/null || echo 'NOT_INSTALLED'`""
        $pgVersion = Invoke-Expression $pgVersionCmd
        
        if ($LASTEXITCODE -eq 0 -and $pgVersion -notlike "*NOT_INSTALLED*") {
            Write-Host "  ✅ PostgreSQL version: $($pgVersion.Split("`n")[0])" -ForegroundColor Green
        } else {
            Write-Host "  ❌ PostgreSQL client not available" -ForegroundColor Red
        }
    } catch {
        Write-Host "  ❌ Error checking PostgreSQL: $_" -ForegroundColor Red
    }

    # Test 7: Port Availability
    Write-Host ""
    Write-Host "7. Checking port availability..." -ForegroundColor Yellow
    
    try {
        $portCheckCmd = "ssh -i `"$($script:Config.ssh.key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$targetHost `"netstat -tuln | grep -w $servicePort || echo 'PORT_AVAILABLE'`""
        $portResult = Invoke-Expression $portCheckCmd
        
        if ($LASTEXITCODE -eq 0 -and $portResult -like "*PORT_AVAILABLE*") {
            Write-Host "  ✅ Port $servicePort is available" -ForegroundColor Green
        } else {
            Write-Host "  ❌ Port $servicePort is in use" -ForegroundColor Red
        }
    } catch {
        Write-Host "  ❌ Error checking port availability: $_" -ForegroundColor Red
    }

    # Test 8: System Resources
    Write-Host ""
    Write-Host "8. Checking system resources..." -ForegroundColor Yellow
    
    try {
        $resourceCmd = "ssh -i `"$($script:Config.ssh.key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$targetHost `"df -h / && free -h`""
        $resourceResult = Invoke-Expression $resourceCmd
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  ✅ System resources checked" -ForegroundColor Green
            $lines = $resourceResult.Split("`n")
            foreach ($line in $lines) {
                if ($line -like "*/*" -or $line -like "*Mem:*") {
                    Write-Host "  $line" -ForegroundColor Gray
                }
            }
        } else {
            Write-Host "  ❌ Could not check system resources" -ForegroundColor Red
        }
    } catch {
        Write-Host "  ❌ System resources error: $_" -ForegroundColor Red
    }

    Write-Host ""
    Write-Host "✅ Auth-service server readiness test completed!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Next steps:" -ForegroundColor Cyan
    Write-Host "- If dependencies are missing, run menu option 8 (Install Dependencies)" -ForegroundColor White
    Write-Host "- If all dependencies are available, run menu option 9 (Build Auth-Service)" -ForegroundColor White
    
    return $true
}

# Export the function
Export-ModuleMember -Function Test-ServerReadiness
