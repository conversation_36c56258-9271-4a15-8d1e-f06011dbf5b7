# Test-ServerReadiness-Simple.psm1 - Simple module for testing server readiness

<#
.SYNOPSIS
    Provides functionality for testing server readiness for auth-service deployment.

.DESCRIPTION
    This module tests SSH connectivity, dependency availability, and server configuration
    for both database-service and auth-service applications.

.NOTES
    File Name      : Test-ServerReadiness-Simple.psm1
    Author         : Auth Service Deployment Team
    Prerequisite   : PowerShell 5.1 or later
    Copyright      : (c) 2025 Augment
#>

function Test-ServerReadiness {
    [CmdletBinding()]
    param()

    Write-Host "=== Testing Server Readiness ===" -ForegroundColor Cyan
    Write-Host ""

    # Check if configuration is loaded
    if ($null -eq $script:Config) {
        Write-Host "❌ Configuration is not loaded. Please load configuration first." -ForegroundColor Red
        return $false
    }

    # Load configuration if not available (same logic as Install-Dependencies)
    if ($null -eq $script:Config) {
        Write-Host "Configuration not loaded, attempting to load..." -ForegroundColor Yellow

        # Try auth-service config first, then database-service
        $authConfigPath = Join-Path -Path $PSScriptRoot -ChildPath "..\config\auth-service-development.json"
        $dbConfigPath = Join-Path -Path $PSScriptRoot -ChildPath "..\config\database-service-development.json"

        $configPath = if (Test-Path $authConfigPath) { $authConfigPath } else { $dbConfigPath }

        try {
            $configContent = Get-Content -Path $configPath -Raw -ErrorAction Stop
            $script:Config = ConvertFrom-Json -InputObject $configContent -ErrorAction Stop
            Write-Host "✅ Configuration loaded from: $configPath" -ForegroundColor Green
        } catch {
            Write-Host "❌ Failed to load configuration: $_" -ForegroundColor Red
            return $false
        }
    }

    # Determine service type with enhanced detection (same logic as Install-Dependencies)
    $isAuthService = $false

    if ($script:Config.project.name -eq "auth-service") {
        $isAuthService = $true
    } elseif ($script:Config.service.name -eq "auth-service") {
        $isAuthService = $true
    } elseif ($script:Config.database.name -like "*auth*") {
        $isAuthService = $true
    } elseif ($script:Config.ssh.host -eq "dev.chcit.org") {
        $isAuthService = $true
    }

    $serviceName = if ($isAuthService) { "auth-service" } else { "database-service" }
    $targetHost = $script:Config.ssh.host
    $installDir = if ($isAuthService) { "/opt/auth-service" } else { "/opt/database-service" }

    Write-Host "Service: $serviceName" -ForegroundColor Yellow
    Write-Host "Target Host: $targetHost" -ForegroundColor Yellow
    Write-Host "Install Directory: $installDir" -ForegroundColor Yellow
    Write-Host ""

    # Test 1: SSH Connectivity
    Write-Host "1. Testing SSH connectivity..." -ForegroundColor Yellow
    
    try {
        $sshCommand = "ssh -i `"$($script:Config.ssh.key_path)`" -o StrictHostKeyChecking=no -o ConnectTimeout=10 $($script:Config.ssh.username)@$targetHost `"echo 'SSH connection successful'`""
        $result = Invoke-Expression $sshCommand
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  ✅ SSH connection successful" -ForegroundColor Green
        } else {
            Write-Host "  ❌ SSH connection failed" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "  ❌ SSH connection error: $_" -ForegroundColor Red
        return $false
    }

    # Test 2: Basic System Information
    Write-Host ""
    Write-Host "2. Checking system information..." -ForegroundColor Yellow
    
    try {
        $sysInfoCmd = "ssh -i `"$($script:Config.ssh.key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$targetHost `"uname -a && lsb_release -a 2>/dev/null || cat /etc/os-release`""
        $sysInfo = Invoke-Expression $sysInfoCmd
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  ✅ System information retrieved" -ForegroundColor Green
            Write-Host "  $($sysInfo.Split("`n")[0])" -ForegroundColor Gray
        } else {
            Write-Host "  ❌ Could not retrieve system information" -ForegroundColor Red
        }
    } catch {
        Write-Host "  ❌ System information error: $_" -ForegroundColor Red
    }

    # Test 3: Check Development Tools
    Write-Host ""
    Write-Host "3. Checking development tools..." -ForegroundColor Yellow
    
    $tools = @(
        @{ Name = "GCC"; Command = "g++ --version 2>/dev/null | head -1 || echo 'NOT_INSTALLED'" },
        @{ Name = "CMake"; Command = "cmake --version 2>/dev/null | head -1 || echo 'NOT_INSTALLED'" },
        @{ Name = "PostgreSQL Client"; Command = "psql --version 2>/dev/null || echo 'NOT_INSTALLED'" },
        @{ Name = "Git"; Command = "git --version 2>/dev/null || echo 'NOT_INSTALLED'" }
    )

    foreach ($tool in $tools) {
        try {
            $toolCmd = "ssh -i `"$($script:Config.ssh.key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$targetHost `"$($tool.Command)`""
            $toolResult = Invoke-Expression $toolCmd
            
            if ($LASTEXITCODE -eq 0 -and $toolResult -notlike "*NOT_INSTALLED*") {
                Write-Host "  ✅ $($tool.Name): $($toolResult.Split("`n")[0])" -ForegroundColor Green
            } else {
                Write-Host "  ❌ $($tool.Name): Not installed" -ForegroundColor Red
            }
        } catch {
            Write-Host "  ❌ $($tool.Name): Error checking" -ForegroundColor Red
        }
    }

    # Test 4: Check Required Directories
    Write-Host ""
    Write-Host "4. Checking required directories..." -ForegroundColor Yellow

    try {
        $dirCheckCmd = "ssh -i `"$($script:Config.ssh.key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$targetHost `"test -d $installDir && echo 'EXISTS' || echo 'NOT_EXISTS'`""
        $dirResult = Invoke-Expression $dirCheckCmd

        if ($LASTEXITCODE -eq 0 -and $dirResult -like "*EXISTS*") {
            Write-Host "  ✅ Deployment directory '$installDir' exists" -ForegroundColor Green
        } else {
            Write-Host "  ❌ Deployment directory '$installDir' does not exist!" -ForegroundColor Red
            Write-Host "  The directory will need to be created during deployment." -ForegroundColor Yellow

            # Check if parent directory is writable
            $parentDir = Split-Path $installDir -Parent
            $parentCheckCmd = "ssh -i `"$($script:Config.ssh.key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$targetHost `"test -w $parentDir && echo 'WRITABLE' || echo 'NOT_WRITABLE'`""
            $parentResult = Invoke-Expression $parentCheckCmd

            if ($LASTEXITCODE -eq 0 -and $parentResult -like "*WRITABLE*") {
                Write-Host "  ✅ Parent directory '$parentDir' is writable" -ForegroundColor Green
            } else {
                Write-Host "  ❌ WARNING: Parent directory '$parentDir' is not writable!" -ForegroundColor Red
            }
        }
    } catch {
        Write-Host "  ❌ Error checking directories: $_" -ForegroundColor Red
    }

    # Test 5: Check Auth-Service Specific Dependencies
    if ($serviceName -eq "auth-service") {
        Write-Host ""
        Write-Host "5. Checking auth-service specific dependencies..." -ForegroundColor Yellow

        $authDeps = @(
            @{ Name = "Boost Libraries"; Command = "dpkg -l | grep libboost | head -1 || echo 'NOT_INSTALLED'" },
            @{ Name = "OpenSSL Dev"; Command = "dpkg -s libssl-dev 2>/dev/null | grep Status || echo 'NOT_INSTALLED'" },
            @{ Name = "JSON Library"; Command = "dpkg -s nlohmann-json3-dev 2>/dev/null | grep Status || echo 'NOT_INSTALLED'" },
            @{ Name = "PostgreSQL Dev"; Command = "dpkg -s libpq-dev 2>/dev/null | grep Status || echo 'NOT_INSTALLED'" }
        )

        foreach ($dep in $authDeps) {
            try {
                $depCmd = "ssh -i `"$($script:Config.ssh.key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$targetHost `"$($dep.Command)`""
                $depResult = Invoke-Expression $depCmd

                if ($LASTEXITCODE -eq 0 -and $depResult -notlike "*NOT_INSTALLED*") {
                    Write-Host "  ✅ $($dep.Name): Available" -ForegroundColor Green
                } else {
                    Write-Host "  ❌ $($dep.Name): Not installed" -ForegroundColor Red
                }
            } catch {
                Write-Host "  ❌ $($dep.Name): Error checking" -ForegroundColor Red
            }
        }
    }

    # Test 6: Check Disk Space and System Resources
    Write-Host ""
    Write-Host "6. Checking system resources..." -ForegroundColor Yellow
    
    try {
        $resourceCmd = "ssh -i `"$($script:Config.ssh.key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$targetHost `"df -h / && free -h`""
        $resourceResult = Invoke-Expression $resourceCmd
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  ✅ System resources checked" -ForegroundColor Green
            $lines = $resourceResult.Split("`n")
            foreach ($line in $lines) {
                if ($line -like "*/*" -or $line -like "*Mem:*") {
                    Write-Host "  $line" -ForegroundColor Gray
                }
            }
        } else {
            Write-Host "  ❌ Could not check system resources" -ForegroundColor Red
        }
    } catch {
        Write-Host "  ❌ System resources error: $_" -ForegroundColor Red
    }

    # Test 7: Check Requirements from Configuration
    Write-Host ""
    Write-Host "7. Checking for required software..." -ForegroundColor Yellow
    Write-Host "---------------------------------------" -ForegroundColor Gray

    if ($script:Config.dependencies) {
        Write-Host "Requirements loaded successfully." -ForegroundColor Green

        $missingDeps = @()
        foreach ($dep in $script:Config.dependencies) {
            $depName = $dep.name
            $checkCmd = if ($dep.check) { $dep.check } else { "which $($dep.name.ToLower()) >/dev/null 2>&1 && echo 'FOUND' || echo 'NOT_FOUND'" }

            try {
                $depCheckCmd = "ssh -i `"$($script:Config.ssh.key_path)`" -o StrictHostKeyChecking=no $($script:Config.ssh.username)@$targetHost `"$checkCmd`""
                $depCheckResult = Invoke-Expression $depCheckCmd

                if ($LASTEXITCODE -eq 0 -and $depCheckResult -like "*FOUND*" -or $depCheckResult -notlike "*NOT_FOUND*") {
                    Write-Host "  [OK] $depName`: Available" -ForegroundColor Green
                } else {
                    Write-Host "  [MISSING] $depName`: Missing" -ForegroundColor Red
                    $missingDeps += $depName
                }
            } catch {
                Write-Host "  [ERROR] $depName`: Error checking" -ForegroundColor Red
                $missingDeps += $depName
            }
        }

        if ($missingDeps.Count -gt 0) {
            Write-Host ""
            Write-Host "Missing dependencies: $($missingDeps -join ', ')" -ForegroundColor Red
        }
    } else {
        Write-Host "No requirements configuration found." -ForegroundColor Yellow
    }

    Write-Host ""
    Write-Host "✅ Server readiness test completed!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Next steps:" -ForegroundColor Cyan
    Write-Host "- If dependencies are missing, run menu option 8 (Install Dependencies)" -ForegroundColor White
    Write-Host "- If all dependencies are available, run menu option 9 (Build Project)" -ForegroundColor White

    return $true
}

# Export the function
Export-ModuleMember -Function Test-ServerReadiness
