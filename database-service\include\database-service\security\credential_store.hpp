#pragma once
#include <string>
#include <unordered_map>
#include <mutex>
#include <memory>

namespace dbservice::security {

/**
 * @class CredentialStore
 * @brief Secure storage for sensitive credentials
 * 
 * This class provides secure storage for sensitive credentials like API keys,
 * database passwords, etc. It encrypts the credentials before storing them
 * and decrypts them when retrieving.
 */
class CredentialStore {
public:
    /**
     * @brief Get the singleton instance
     * @return Reference to the singleton instance
     */
    static CredentialStore& getInstance();

    /**
     * @brief Get a shared_ptr to the singleton instance
     * @return Shared pointer to the singleton instance
     */
    static std::shared_ptr<CredentialStore> getSharedInstance();
    
    /**
     * @brief Initialize the credential store
     * @param encryptionKey Key used for encryption/decryption
     * @return True if initialization was successful
     */
    bool initialize(const std::string& encryptionKey);
    
    /**
     * @brief Store a credential
     * @param key Credential key
     * @param value Credential value
     * @return True if credential was stored successfully
     */
    bool storeCredential(const std::string& key, const std::string& value);
    
    /**
     * @brief Retrieve a credential
     * @param key Credential key
     * @param defaultValue Default value to return if credential is not found
     * @return Credential value or defaultValue if not found
     */
    std::string getCredential(const std::string& key, const std::string& defaultValue = "");
    
    /**
     * @brief Remove a credential
     * @param key Credential key
     * @return True if credential was removed successfully
     */
    bool removeCredential(const std::string& key);
    
    /**
     * @brief Check if a credential exists
     * @param key Credential key
     * @return True if credential exists
     */
    bool hasCredential(const std::string& key);
    
    /**
     * @brief Clear all credentials
     */
    void clear();
    
private:
    /**
     * @brief Constructor
     */
    CredentialStore();
    
    /**
     * @brief Destructor
     */
    ~CredentialStore();
    
    /**
     * @brief Encrypt a string
     * @param input String to encrypt
     * @return Encrypted string
     */
    std::string encrypt(const std::string& input);
    
    /**
     * @brief Decrypt a string
     * @param input String to decrypt
     * @return Decrypted string
     */
    std::string decrypt(const std::string& input);
    
    std::string encryptionKey_;
    std::unordered_map<std::string, std::string> credentials_;
    std::mutex mutex_;
    bool initialized_;
};

} // namespace dbservice::security
