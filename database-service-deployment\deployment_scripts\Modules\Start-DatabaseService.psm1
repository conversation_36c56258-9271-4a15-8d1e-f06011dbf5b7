# Start Database Service Module

# Import common module
Import-Module -Name (Join-Path $PSScriptRoot '..\Common.psm1') -Force
# Import Logger module
Import-Module -Name (Join-Path $PSScriptRoot 'Logger/Logger.psm1') -Force

# Safe logging wrapper to prevent empty string errors
function Write-SafeLog {
    param(
        [string]$Message,
        [string]$Level = "Info",
        [string]$Component = "DatabaseService"
    )
    if ([string]::IsNullOrEmpty($Message)) {
        $Message = "[Empty message]"
    }
    Write-Log -Message $Message -Level $Level -Component $Component
}

function Start-DatabaseService {
    try {
        Clear-Host
        # Enable UI Mode for menu display
        if (Get-Command -Name Enable-UIMode -ErrorAction SilentlyContinue) {
            Enable-UIMode
        }
        Write-SafeLog -Message "========================================================" -Level "Info"
        Write-SafeLog -Message "               Start Database Service                 " -Level "Info"
        Write-SafeLog -Message "========================================================" -Level "Info"
        Write-SafeLog -Message " " -Level "Info"

        # Debug: Check what configuration variables are available
        Write-SafeLog -Message "DEBUG: Checking configuration availability..." -Level "Info"
        Write-SafeLog -Message "DEBUG: global:Config is null: $($null -eq $global:Config)" -Level "Info"

        # Check if global Config is available
        if ($null -eq $global:Config) {
            Write-SafeLog -Message "Global configuration not found. Attempting to load..." -Level "Warning"
            if (Get-Command -Name Get-Configuration -ErrorAction SilentlyContinue) {
                $global:Config = Get-Configuration
                Write-SafeLog -Message "DEBUG: Loaded configuration, global:Config is now null: $($null -eq $global:Config)" -Level "Info"
            } else {
                Write-SafeLog -Message "Get-Configuration function not available." -Level "Error"
            }
        }

        if ($null -eq $global:Config) {
            Write-SafeLog -Message "Configuration is still null after loading attempt." -Level "Error"
        } else {
            Write-SafeLog -Message "Configuration loaded successfully." -Level "Info"
            Write-SafeLog -Message "DEBUG: SSH config is null: $($null -eq $global:Config.ssh)" -Level "Info"
        }

        if ($null -eq $global:Config -or $null -eq $global:Config.ssh) {
            Write-SafeLog -Message "SSH configuration is not set up. Please configure SSH settings first." -Level "Error"
            if (Get-Command -Name Wait-ForUser -ErrorAction SilentlyContinue) {
                Wait-ForUser
            } else {
                Write-Host "Press Enter to continue..."
                Read-Host | Out-Null
            }
            return
        }

        # Use global config
        $Config = $global:Config
    }
    catch {
        Write-SafeLog -Message "Error in Start-DatabaseService initialization: $($_.Exception.Message)" -Level "Error"
        Wait-ForUser
        return
    }

    Write-SafeLog -Message "Starting database service on $($Config.ssh.host)..." -Level "Info"

    # Start the database service
    Write-SafeLog -Message "Starting database service..." -Level "Info"
    $startCmd = "sudo systemctl start database-service"
    Invoke-RemoteCommand -Command $startCmd

    # Check if the service started successfully
    Write-SafeLog -Message "Checking service status..." -Level "Info"
    $statusCmd = "sudo systemctl is-active database-service"
    $serviceStatus = Invoke-RemoteCommand -Command $statusCmd -Silent
    if ([string]::IsNullOrEmpty($serviceStatus)) { $serviceStatus = "unknown" }

    if ($serviceStatus -eq "active") {
        Write-SafeLog -Message "Database service started successfully!" -Level "Info"

        # Show detailed status
        Write-SafeLog -Message "Service Status:" -Level "Info"
        $detailedStatusCmd = "sudo systemctl status database-service --no-pager"
        Invoke-RemoteCommand -Command $detailedStatusCmd
    } else {
        Write-SafeLog -Message "Failed to start database service!" -Level "Error"
        Write-SafeLog -Message "Service status: $serviceStatus" -Level "Info"

        # Show error logs
        Write-SafeLog -Message "Recent service logs:" -Level "Info"
        $logsCmd = "sudo journalctl -u database-service -n 10 --no-pager"
        Invoke-RemoteCommand -Command $logsCmd
    }
    
    # Disable UI Mode after menu display
    if (Get-Command -Name Disable-UIMode -ErrorAction SilentlyContinue) {
        Disable-UIMode
    }
    
    if (Get-Command -Name Wait-ForUser -ErrorAction SilentlyContinue) {
        Wait-ForUser
    } else {
        Write-Host "Press Enter to continue..."
        Read-Host | Out-Null
    }
    
    if (Get-Command -Name Show-MainMenu -ErrorAction SilentlyContinue) {
        Show-MainMenu
    }
}

# Export the function
Export-ModuleMember -Function Start-DatabaseService
