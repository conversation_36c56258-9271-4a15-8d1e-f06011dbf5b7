Running CMake configuration...
-- The CXX compiler identification is GNU 14.2.0
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /usr/bin/g++-14 - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found PostgreSQL: /usr/lib/x86_64-linux-gnu/libpq.so (found version "17.5")
-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.13")
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE
-- pqxx package not found, will attempt to use system libraries
-- Tests disabled. Use -DBUILD_TESTS=ON to enable.
-- 
-- === Database Service Configuration Summary ===
-- Version: 1.0.0
-- Build type: Release
-- C++ standard: 23
-- Compiler: GNU 14.2.0
-- Build tests: OFF
-- Code coverage: OFF
-- Install prefix: /usr/local
-- 
-- Dependencies:
--   Boost: 1.83.0
--   PostgreSQL: 
--   OpenSSL: 3.0.13
--   nlohmann/json: Found
--   pqxx: System library
-- ===============================================
-- 
-- Configuring done (0.4s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/database-service-build/build
Starting compilation...
[  9%] Building CXX object CMakeFiles/database-service.dir/src/api/api_server.cpp.o
[  9%] Building CXX object CMakeFiles/database-service.dir/src/api/route_controller.cpp.o
[ 13%] Building CXX object CMakeFiles/database-service.dir/src/core/connection.cpp.o
[ 18%] Building CXX object CMakeFiles/database-service.dir/src/core/connection_manager.cpp.o
[ 22%] Building CXX object CMakeFiles/database-service.dir/src/core/transaction.cpp.o
[ 27%] Building CXX object CMakeFiles/database-service.dir/src/database_service.cpp.o
[ 31%] Building CXX object CMakeFiles/database-service.dir/src/main.cpp.o
[ 36%] Building CXX object CMakeFiles/database-service.dir/src/metrics/database_metrics.cpp.o
[ 40%] Building CXX object CMakeFiles/database-service.dir/src/metrics/metrics_collector.cpp.o
[ 45%] Building CXX object CMakeFiles/database-service.dir/src/schema/schema_manager.cpp.o
[ 50%] Building CXX object CMakeFiles/database-service.dir/src/security/audit_logger.cpp.o
[ 54%] Building CXX object CMakeFiles/database-service.dir/src/security/credential_store.cpp.o
[ 59%] Building CXX object CMakeFiles/database-service.dir/src/security/jwt.cpp.o
[ 63%] Building CXX object CMakeFiles/database-service.dir/src/security/rate_limiter.cpp.o
[ 68%] Building CXX object CMakeFiles/database-service.dir/src/security/security_manager.cpp.o
[ 72%] Building CXX object CMakeFiles/database-service.dir/src/service/application_manager.cpp.o
[ 77%] Building CXX object CMakeFiles/database-service.dir/src/service/database_instance_manager.cpp.o
[ 81%] Building CXX object CMakeFiles/database-service.dir/src/utils/cache.cpp.o
[ 86%] Building CXX object CMakeFiles/database-service.dir/src/utils/config_manager.cpp.o
[ 90%] Building CXX object CMakeFiles/database-service.dir/src/utils/logger.cpp.o
[ 95%] Building CXX object CMakeFiles/database-service.dir/src/utils/thread_pool.cpp.o
[100%] Linking CXX executable bin/database-service
[100%] Built target database-service
