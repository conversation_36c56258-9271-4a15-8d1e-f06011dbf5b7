#include "auth_service.hpp"
#include "config_manager.hpp"
#include "database_manager.hpp"
#include "security_manager.hpp"
#include "http_server.hpp"
#include "user_manager.hpp"
#include <iostream>

AuthService::AuthService(std::unique_ptr<ConfigManager> config)
    : config_manager_(std::move(config)) {
    
    // Initialize components
    database_manager_ = std::make_unique<DatabaseManager>(config_manager_.get());
    security_manager_ = std::make_unique<SecurityManager>();
    user_manager_ = std::make_unique<UserManager>(database_manager_.get(), security_manager_.get());
    http_server_ = std::make_unique<HttpServer>(user_manager_.get());
}

AuthService::~AuthService() = default;

void AuthService::start(int port) {
    std::cout << "Auth Service starting..." << std::endl;
    
    // Initialize database connection
    database_manager_->initialize();
    
    // Start HTTP server
    http_server_->start(port);
    
    std::cout << "Auth Service started successfully on port " << port << std::endl;
}

void AuthService::stop() {
    std::cout << "Auth Service stopping..." << std::endl;
    
    if (http_server_) {
        http_server_->stop();
    }
    
    std::cout << "Auth Service stopped." << std::endl;
}
