#!/bin/bash

# Database Service Build Script
# Builds the database service with proper CMake configuration

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
BUILD_DIR="$PROJECT_ROOT/build"
BUILD_TYPE="${BUILD_TYPE:-Release}"
BUILD_TESTS="${BUILD_TESTS:-ON}"
ENABLE_COVERAGE="${ENABLE_COVERAGE:-OFF}"
INSTALL_PREFIX="${INSTALL_PREFIX:-/opt/database-service}"
JOBS="${JOBS:-$(nproc)}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        "INFO")
            echo -e "${GREEN}[INFO]${NC} ${timestamp} - $message"
            ;;
        "WARN")
            echo -e "${YELLOW}[WARN]${NC} ${timestamp} - $message"
            ;;
        "ERROR")
            echo -e "${RED}[ERROR]${NC} ${timestamp} - $message"
            ;;
        "DEBUG")
            echo -e "${BLUE}[DEBUG]${NC} ${timestamp} - $message"
            ;;
    esac
}

# Show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --build-type TYPE     Build type (Debug|Release|RelWithDebInfo|MinSizeRel)"
    echo "  --no-tests           Disable building tests"
    echo "  --enable-coverage    Enable code coverage"
    echo "  --install-prefix DIR Installation prefix"
    echo "  --jobs N             Number of parallel jobs"
    echo "  --clean              Clean build directory first"
    echo "  --install            Install after building"
    echo "  --help               Show this help message"
    echo ""
    echo "Environment variables:"
    echo "  BUILD_TYPE           Build type (default: Release)"
    echo "  BUILD_TESTS          Build tests (default: ON)"
    echo "  ENABLE_COVERAGE      Enable coverage (default: OFF)"
    echo "  INSTALL_PREFIX       Install prefix (default: /opt/database-service)"
    echo "  JOBS                 Parallel jobs (default: nproc)"
}

# Check dependencies
check_dependencies() {
    log "INFO" "Checking build dependencies"
    
    # Check CMake
    if ! command -v cmake &> /dev/null; then
        log "ERROR" "CMake is required but not installed"
        exit 1
    fi
    
    local cmake_version
    cmake_version=$(cmake --version | head -n1 | cut -d' ' -f3)
    log "INFO" "CMake version: $cmake_version"
    
    # Check compiler
    if ! command -v g++ &> /dev/null && ! command -v clang++ &> /dev/null; then
        log "ERROR" "C++ compiler (g++ or clang++) is required"
        exit 1
    fi
    
    if command -v g++ &> /dev/null; then
        local gcc_version
        gcc_version=$(g++ --version | head -n1 | cut -d' ' -f4)
        log "INFO" "GCC version: $gcc_version"
    fi
    
    if command -v clang++ &> /dev/null; then
        local clang_version
        clang_version=$(clang++ --version | head -n1 | cut -d' ' -f3)
        log "INFO" "Clang version: $clang_version"
    fi
    
    # Check required packages
    log "INFO" "Required packages will be checked by CMake"
}

# Clean build directory
clean_build() {
    if [[ -d "$BUILD_DIR" ]]; then
        log "INFO" "Cleaning build directory: $BUILD_DIR"
        rm -rf "$BUILD_DIR"
    fi
}

# Configure build
configure_build() {
    log "INFO" "Configuring build"
    log "INFO" "Build type: $BUILD_TYPE"
    log "INFO" "Build tests: $BUILD_TESTS"
    log "INFO" "Enable coverage: $ENABLE_COVERAGE"
    log "INFO" "Install prefix: $INSTALL_PREFIX"
    
    mkdir -p "$BUILD_DIR"
    cd "$BUILD_DIR"
    
    cmake \
        -DCMAKE_BUILD_TYPE="$BUILD_TYPE" \
        -DBUILD_TESTS="$BUILD_TESTS" \
        -DENABLE_COVERAGE="$ENABLE_COVERAGE" \
        -DCMAKE_INSTALL_PREFIX="$INSTALL_PREFIX" \
        "$PROJECT_ROOT"
}

# Build project
build_project() {
    log "INFO" "Building project with $JOBS parallel jobs"
    
    cd "$BUILD_DIR"
    cmake --build . --parallel "$JOBS"
    
    log "INFO" "Build completed successfully"
}

# Run tests
run_tests() {
    if [[ "$BUILD_TESTS" == "ON" ]]; then
        log "INFO" "Running tests"
        
        cd "$BUILD_DIR"
        ctest --output-on-failure --parallel "$JOBS"
        
        log "INFO" "Tests completed"
    else
        log "INFO" "Tests disabled, skipping"
    fi
}

# Install project
install_project() {
    log "INFO" "Installing to $INSTALL_PREFIX"
    
    cd "$BUILD_DIR"
    
    if [[ "$INSTALL_PREFIX" == "/opt/"* ]] || [[ "$INSTALL_PREFIX" == "/usr/"* ]]; then
        sudo cmake --install .
    else
        cmake --install .
    fi
    
    log "INFO" "Installation completed"
}

# Generate coverage report
generate_coverage() {
    if [[ "$ENABLE_COVERAGE" == "ON" ]]; then
        log "INFO" "Generating coverage report"
        
        cd "$BUILD_DIR"
        
        if command -v gcov &> /dev/null && command -v lcov &> /dev/null; then
            lcov --capture --directory . --output-file coverage.info
            lcov --remove coverage.info '/usr/*' --output-file coverage.info
            lcov --remove coverage.info '*/tests/*' --output-file coverage.info
            
            if command -v genhtml &> /dev/null; then
                genhtml coverage.info --output-directory coverage_html
                log "INFO" "Coverage report generated in coverage_html/"
            fi
        else
            log "WARN" "lcov/genhtml not available for coverage report"
        fi
    fi
}

# Main execution
main() {
    local clean_first=false
    local install_after=false
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --build-type)
                BUILD_TYPE="$2"
                shift 2
                ;;
            --no-tests)
                BUILD_TESTS="OFF"
                shift
                ;;
            --enable-coverage)
                ENABLE_COVERAGE="ON"
                shift
                ;;
            --install-prefix)
                INSTALL_PREFIX="$2"
                shift 2
                ;;
            --jobs)
                JOBS="$2"
                shift 2
                ;;
            --clean)
                clean_first=true
                shift
                ;;
            --install)
                install_after=true
                shift
                ;;
            --help)
                show_usage
                exit 0
                ;;
            *)
                log "ERROR" "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    log "INFO" "Starting Database Service build"
    
    # Execute build steps
    check_dependencies
    
    if [[ "$clean_first" == true ]]; then
        clean_build
    fi
    
    configure_build
    build_project
    run_tests
    generate_coverage
    
    if [[ "$install_after" == true ]]; then
        install_project
    fi
    
    log "INFO" "Build process completed successfully!"
    log "INFO" "Binary location: $BUILD_DIR/bin/database-service"
    
    if [[ "$install_after" == true ]]; then
        log "INFO" "Installed to: $INSTALL_PREFIX"
    else
        log "INFO" "To install, run: cmake --install $BUILD_DIR"
    fi
}

# Run main function
main "$@"
