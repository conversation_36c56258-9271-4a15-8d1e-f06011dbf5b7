#include "database-service/security/rate_limiter.hpp"
#include "database-service/utils/logger.hpp"
#include <format>
#include <algorithm>

namespace dbservice::security {

RateLimiter::RateLimiter() : initialized_(false) {
    // Default global configuration
    globalConfig_.requestsPerMinute = 60;
    globalConfig_.requestsPerHour = 1000;
    globalConfig_.requestsPerDay = 10000;
    globalConfig_.burstLimit = 10;
    globalConfig_.windowSize = std::chrono::seconds(60);

    // Initialize statistics
    stats_.totalRequests = 0;
    stats_.allowedRequests = 0;
    stats_.blockedRequests = 0;
    stats_.whitelistHits = 0;
    stats_.blacklistHits = 0;
    stats_.startTime = std::chrono::system_clock::now();
}

bool RateLimiter::initialize() {
    if (initialized_) {
        return true;
    }

    try {
        // Set up default endpoint configurations
        RateLimitConfig authConfig;
        authConfig.requestsPerMinute = 10;
        authConfig.requestsPerHour = 100;
        authConfig.requestsPerDay = 1000;
        authConfig.burstLimit = 5;
        authConfig.windowSize = std::chrono::seconds(60);
        endpointConfigs_["/api/auth/login"] = authConfig;

        RateLimitConfig queryConfig;
        queryConfig.requestsPerMinute = 100;
        queryConfig.requestsPerHour = 5000;
        queryConfig.requestsPerDay = 50000;
        queryConfig.burstLimit = 20;
        queryConfig.windowSize = std::chrono::seconds(60);
        endpointConfigs_["/api/query"] = queryConfig;
        endpointConfigs_["/api/execute"] = queryConfig;

        initialized_ = true;
        utils::Logger::info("RateLimiter initialized successfully");
        return true;

    } catch (const std::exception& e) {
        utils::Logger::error(std::format("RateLimiter initialization failed: {}", e.what()));
        return false;
    }
}

RateLimitResult RateLimiter::checkLimit(
    const std::string& clientId,
    const std::string& endpoint,
    const std::string& userAgent) {

    std::lock_guard<std::mutex> lock(mutex_);

    if (!initialized_) {
        return {false, 0, std::chrono::system_clock::now(), "Rate limiter not initialized"};
    }

    stats_.totalRequests++;

    // Check blacklist first
    if (isBlacklisted(clientId)) {
        stats_.blacklistHits++;
        stats_.blockedRequests++;
        return {false, 0, std::chrono::system_clock::now(), "Client is blacklisted"};
    }

    // Check whitelist
    if (isWhitelisted(clientId)) {
        stats_.whitelistHits++;
        stats_.allowedRequests++;
        return {true, -1, std::chrono::system_clock::now(), "Client is whitelisted"};
    }

    auto now = std::chrono::system_clock::now();
    auto& tracker = getClientTracker(clientId);
    const auto& config = getEndpointConfig(endpoint);

    // Check sliding window rate limit
    if (!checkSlidingWindow(tracker, config, now)) {
        stats_.blockedRequests++;
        auto resetTime = calculateResetTime(tracker, config);
        return {false, 0, resetTime, "Rate limit exceeded"};
    }

    // Check burst limit
    if (!checkBurstLimit(tracker, config, now)) {
        stats_.blockedRequests++;
        auto resetTime = calculateResetTime(tracker, config);
        return {false, 0, resetTime, "Burst limit exceeded"};
    }

    // Update tracker and allow request
    updateTracker(tracker, now);
    stats_.allowedRequests++;

    int remainingRequests = config.requestsPerMinute - tracker.requestCount;
    auto resetTime = calculateResetTime(tracker, config);

    return {true, remainingRequests, resetTime, "Request allowed"};
}

bool RateLimiter::configureEndpoint(const std::string& endpoint, const RateLimitConfig& config) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    endpointConfigs_[endpoint] = config;
    utils::Logger::info(std::format("Rate limit configured for endpoint: {}", endpoint));
    return true;
}

bool RateLimiter::configureGlobal(const RateLimitConfig& config) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    globalConfig_ = config;
    utils::Logger::info("Global rate limit configuration updated");
    return true;
}

bool RateLimiter::addToWhitelist(const std::string& clientId) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    whitelist_.insert(clientId);
    utils::Logger::info(std::format("Client added to whitelist: {}", clientId));
    return true;
}

bool RateLimiter::removeFromWhitelist(const std::string& clientId) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    auto removed = whitelist_.erase(clientId) > 0;
    if (removed) {
        utils::Logger::info(std::format("Client removed from whitelist: {}", clientId));
    }
    return removed;
}

bool RateLimiter::addToBlacklist(
    const std::string& clientId, 
    const std::string& reason,
    std::chrono::seconds duration) {

    std::lock_guard<std::mutex> lock(mutex_);
    
    auto expireTime = std::chrono::system_clock::now() + duration;
    if (duration.count() == 0) {
        // Permanent blacklist
        expireTime = std::chrono::system_clock::time_point::max();
    }
    
    blacklist_[clientId] = {reason, expireTime};
    utils::Logger::warning(std::format("Client added to blacklist: {} (reason: {})", clientId, reason));
    return true;
}

bool RateLimiter::removeFromBlacklist(const std::string& clientId) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    auto removed = blacklist_.erase(clientId) > 0;
    if (removed) {
        utils::Logger::info(std::format("Client removed from blacklist: {}", clientId));
    }
    return removed;
}

nlohmann::json RateLimiter::getStatistics() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    auto now = std::chrono::system_clock::now();
    auto uptime = std::chrono::duration_cast<std::chrono::seconds>(now - stats_.startTime).count();
    
    nlohmann::json stats;
    stats["total_requests"] = stats_.totalRequests;
    stats["allowed_requests"] = stats_.allowedRequests;
    stats["blocked_requests"] = stats_.blockedRequests;
    stats["whitelist_hits"] = stats_.whitelistHits;
    stats["blacklist_hits"] = stats_.blacklistHits;
    stats["uptime_seconds"] = uptime;
    stats["active_clients"] = clientTrackers_.size();
    stats["whitelist_size"] = whitelist_.size();
    stats["blacklist_size"] = blacklist_.size();
    
    if (stats_.totalRequests > 0) {
        stats["success_rate"] = static_cast<double>(stats_.allowedRequests) / stats_.totalRequests;
    } else {
        stats["success_rate"] = 1.0;
    }
    
    return stats;
}

bool RateLimiter::isWhitelisted(const std::string& clientId) {
    return whitelist_.find(clientId) != whitelist_.end();
}

bool RateLimiter::isBlacklisted(const std::string& clientId) {
    auto it = blacklist_.find(clientId);
    if (it == blacklist_.end()) {
        return false;
    }
    
    auto now = std::chrono::system_clock::now();
    if (isBlacklistExpired(it->second, now)) {
        blacklist_.erase(it);
        return false;
    }
    
    return true;
}

ClientTracker& RateLimiter::getClientTracker(const std::string& clientId) {
    auto it = clientTrackers_.find(clientId);
    if (it == clientTrackers_.end()) {
        ClientTracker tracker;
        tracker.clientId = clientId;
        tracker.requestCount = 0;
        tracker.windowStart = std::chrono::system_clock::now();
        tracker.lastRequest = std::chrono::system_clock::now();
        tracker.burstCount = 0;
        tracker.burstStart = std::chrono::system_clock::now();
        
        clientTrackers_[clientId] = tracker;
        return clientTrackers_[clientId];
    }
    
    return it->second;
}

const RateLimitConfig& RateLimiter::getEndpointConfig(const std::string& endpoint) {
    auto it = endpointConfigs_.find(endpoint);
    if (it != endpointConfigs_.end()) {
        return it->second;
    }
    
    return globalConfig_;
}

bool RateLimiter::checkSlidingWindow(ClientTracker& tracker, const RateLimitConfig& config, const std::chrono::system_clock::time_point& now) {
    // Reset window if expired
    auto windowDuration = std::chrono::duration_cast<std::chrono::seconds>(now - tracker.windowStart);
    if (windowDuration >= config.windowSize) {
        tracker.requestCount = 0;
        tracker.windowStart = now;
    }
    
    return tracker.requestCount < config.requestsPerMinute;
}

bool RateLimiter::checkBurstLimit(ClientTracker& tracker, const RateLimitConfig& config, const std::chrono::system_clock::time_point& now) {
    // Reset burst window if more than 1 second has passed
    auto burstDuration = std::chrono::duration_cast<std::chrono::seconds>(now - tracker.burstStart);
    if (burstDuration >= std::chrono::seconds(1)) {
        tracker.burstCount = 0;
        tracker.burstStart = now;
    }
    
    return tracker.burstCount < config.burstLimit;
}

void RateLimiter::updateTracker(ClientTracker& tracker, const std::chrono::system_clock::time_point& now) {
    tracker.requestCount++;
    tracker.burstCount++;
    tracker.lastRequest = now;
}

std::chrono::system_clock::time_point RateLimiter::calculateResetTime(const ClientTracker& tracker, const RateLimitConfig& config) {
    return tracker.windowStart + config.windowSize;
}

bool RateLimiter::isBlacklistExpired(const std::pair<std::string, std::chrono::system_clock::time_point>& entry, const std::chrono::system_clock::time_point& now) {
    return entry.second != std::chrono::system_clock::time_point::max() && now >= entry.second;
}

} // namespace dbservice::security
