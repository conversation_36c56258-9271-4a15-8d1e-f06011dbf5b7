{{ ... }}
# Setup Certificate Access Module

# Import common module
Import-Module -Name (Join-Path $PSScriptRoot '..\Common.psm1') -Force
# Import Logger module
Import-Module -Name (Join-Path $PSScriptRoot 'Logger/Logger.psm1') -Force

function Initialize-CertificateAccess {
    Clear-Host
    Write-Log -Message "========== Setup Certificate Access ==========" -Level "Info" -Component "Certificate"
    Write-Log -Message "               Setup Certificate Access                 " -Level "Info" -Component "Certificate"
    Write-Log -Message "========== Setup Certificate Access ==========" -Level "Info" -Component "Certificate"
    Write-Log -Message "" -Level "Info" -Component "Certificate"
    $certPath = Read-Host "Enter path to certificate file (PEM format)"
    if (-not (Test-Path $certPath)) {
        Write-Log -Message "Certificate file not found at $certPath" -Level "Error" -Component "Certificate"
        Wait-ForUser
        Show-MainMenu
        return
    }
    $script:Config.certificate.path = $certPath
    Write-Log -Message "Certificate path set to: $certPath" -Level "Success" -Component "Certificate"
    Save-Configuration
    Write-Log -Message "Certificate configuration updated." -Level "Success" -Component "Certificate"
    Wait-ForUser
    Show-MainMenu
}

# Export the main function
Export-ModuleMember -Function Initialize-CertificateAccess
