# ConfigValidator.psm1 - Configuration validation for database service deployment

<#
.SYNOPSIS
    Provides configuration validation and repair for database service deployment.

.DESCRIPTION
    This module implements validation and repair functionality for deployment configurations.
    It checks for required properties, validates values, and provides default values for missing properties.

.NOTES
    File Name      : ConfigValidator.psm1
    Author         : Database Service Deployment Team
    Prerequisite   : PowerShell 5.1 or later
    Copyright      : (c) 2025 Augment
#>

# Import the logger module
Import-Module -Name "$PSScriptRoot\Logger\Logger.psm1" -Force

Write-DebugLog -Message "ConfigValidator.psm1 loaded" -Component "ConfigValidator"

# Configuration schema version
$script:SchemaVersion = "1.0"

# Required configuration properties
$script:RequiredProperties = @{
    project = @(
        "name",
        "local_source_dir",
        "remote_build_dir",
        "remote_install_dir"
    );
    ssh = @(
        "host",
        "port",
        "username",
        "local_key_path"
    );
    service = @(
        "name",
        "user",
        "group"
    );
    database = @(
        "host",
        "port",
        "user",
        "password"
    )
}

<#
.SYNOPSIS
    Validates a configuration object against the required schema.

.DESCRIPTION
    Checks if a configuration object contains all required sections and properties.
    Returns a boolean indicating if the configuration is valid, or a hashtable with
    detailed information if the Detailed switch is specified.

.PARAMETER Config
    The configuration object to validate.

.PARAMETER Detailed
    If specified, returns a hashtable with detailed validation results.

.EXAMPLE
    $isValid = Test-Configuration -Config $config
    if (-not $isValid) {
        Write-Host "Configuration is invalid"
    }

.EXAMPLE
    $result = Test-Configuration -Config $config -Detailed
    if (-not $result.IsValid) {
        Write-Host "Missing properties: $($result.MissingProperties | ConvertTo-Json)"
    }

.RETURNS
    Boolean or hashtable with validation results.
#>
function Test-Configuration {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory=$true)]
        [PSCustomObject]$Config,

        [Parameter(Mandatory=$false)]
        [switch]$Detailed
    )

    Write-DebugLog -Message "Test-Configuration called with config version: $($Config.version)" -Component "ConfigValidator"

    try {
        $isValid = $true
        $missingProperties = @{}

        # Check if the configuration has a version
        if (-not (Get-Member -InputObject $Config -Name "version" -MemberType Properties)) {
            Write-Log -Message "Configuration is missing version information" -Level "Warning" -Component "ConfigValidator"
            $isValid = $false
        }

        # Check required sections and properties
        foreach ($section in $script:RequiredProperties.Keys) {
            if (-not (Get-Member -InputObject $Config -Name $section -MemberType Properties)) {
                Write-Log -Message "Configuration is missing required section: $section" -Level "Warning" -Component "ConfigValidator"
                $isValid = $false
                $missingProperties[$section] = $script:RequiredProperties[$section]
                continue
            }

            $sectionProperties = $Config.$section
            $missingInSection = @()

            foreach ($property in $script:RequiredProperties[$section]) {
                if (-not (Get-Member -InputObject $sectionProperties -Name $property -MemberType Properties) -or
                    $null -eq $sectionProperties.$property -or
                    [string]::IsNullOrWhiteSpace($sectionProperties.$property)) {

                    Write-Log -Message "Configuration is missing required property: ${section}.${property}" -Level "Warning" -Component "ConfigValidator"
                    $isValid = $false
                    $missingInSection += $property
                }
            }

            if ($missingInSection.Count -gt 0) {
                $missingProperties[$section] = $missingInSection
            }
        }

        # Return detailed information if requested
        if ($Detailed) {
            return @{
                IsValid = $isValid
                MissingProperties = $missingProperties
            }
        }

        return $isValid
    } catch {
        Write-Log -Message "Error validating configuration: $_" -Level "Error" -Component "ConfigValidator"

        if ($Detailed) {
            return @{
                IsValid = $false
                Error = $_.Exception.Message
            }
        }

        return $false
    }
}

<#
.SYNOPSIS
    Repairs a configuration object by adding missing properties with default values.

.DESCRIPTION
    Adds missing sections and properties to a configuration object with default values.
    Returns a hashtable with the repaired configuration and a flag indicating if it was modified.

.PARAMETER Config
    The configuration object to repair.

.EXAMPLE
    $result = Repair-Configuration -Config $config
    if ($result.Modified) {
        Write-Host "Configuration was repaired"
        $config = $result.Config
    }

.RETURNS
    Hashtable with the following keys:
    - Config: The repaired configuration object
    - Modified: Boolean indicating if the configuration was modified
    - Error: Error message if an error occurred (only present if an error occurred)
#>
function Repair-Configuration {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory=$true)]
        [PSCustomObject]$Config
    )

    try {
        $modified = $false

        # Add version information if missing
        if (-not (Get-Member -InputObject $Config -Name "version" -MemberType Properties)) {
            $Config | Add-Member -MemberType NoteProperty -Name "version" -Value @{
                "number" = "1.0"
                "schema" = $script:SchemaVersion
                "updated" = (Get-Date -Format "yyyy-MM-dd HH:mm:ss")
            }
            $modified = $true
            Write-Log -Message "Added missing version information" -Level "Info" -Component "ConfigValidator"
        }

        # Add missing sections and properties
        foreach ($section in $script:RequiredProperties.Keys) {
            if (-not (Get-Member -InputObject $Config -Name $section -MemberType Properties)) {
                $Config | Add-Member -MemberType NoteProperty -Name $section -Value @{}
                $modified = $true
                Write-Log -Message "Added missing section: $section" -Level "Info" -Component "ConfigValidator"
            }

            foreach ($property in $script:RequiredProperties[$section]) {
                if (-not (Get-Member -InputObject $Config.$section -Name $property -MemberType Properties) -or
                    $null -eq $Config.$section.$property -or
                    [string]::IsNullOrWhiteSpace($Config.$section.$property)) {

                    # Add default value based on property
                    $defaultValue = Get-DefaultPropertyValue -Section $section -Property $property

                    if (-not (Get-Member -InputObject $Config.$section -Name $property -MemberType Properties)) {
                        $Config.$section | Add-Member -MemberType NoteProperty -Name $property -Value $defaultValue
                    } else {
                        $Config.$section.$property = $defaultValue
                    }

                    $modified = $true
                    Write-Log -Message "Added default value for ${section}.${property}: $defaultValue" -Level "Info" -Component "ConfigValidator"
                }
            }
        }

        return @{
            Config = $Config
            Modified = $modified
        }
    } catch {
        Write-Log -Message "Error repairing configuration: $_" -Level "Error" -Component "ConfigValidator"
        return @{
            Config = $Config
            Modified = $false
            Error = $_.Exception.Message
        }
    }
}

<#
.SYNOPSIS
    Gets the default value for a configuration property.

.DESCRIPTION
    Returns the default value for a configuration property based on the section and property name.

.PARAMETER Section
    The configuration section name.

.PARAMETER Property
    The configuration property name.

.EXAMPLE
    $defaultValue = Get-DefaultPropertyValue -Section "project" -Property "name"

.RETURNS
    The default value for the specified property.
#>
function Get-DefaultPropertyValue {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory=$true)]
        [string]$Section,

        [Parameter(Mandatory=$true)]
        [string]$Property
    )

    # Default values for common properties
    switch ($Section) {
        "project" {
            switch ($Property) {
                "name" { return "auth-service" }
                "local_source_dir" { return "D:\Coding_Projects\auth-service\auth-service-app" }
                "remote_build_dir" { return "/home/<USER>/auth-service-build" }
                "remote_install_dir" { return "/opt/auth-service" }
                "description" { return "Authentication Service for CHCIT" }
                default { return "" }
            }
        }
        "ssh" {
            switch ($Property) {
                "host" { return "git.chcit.org" }
                "port" { return 22 }
                "username" { return "btaylor-admin" }
                "local_key_path" { return "$env:USERPROFILE\.ssh\id_rsa" }
                default { return "" }
            }
        }
        "service" {
            switch ($Property) {
                "name" { return "database-service" }
                "user" { return "database-svc" }
                "group" { return "database-svc" }
                "description" { return "Database Service for Project Tracker" }
                default { return "" }
            }
        }
        "database" {
            switch ($Property) {
                "host" { return "localhost" }
                "port" { return 5432 }
                "user" { return "database_service" }
                "password" { return "password123" }
                "name" { return "database_service" }
                default { return "" }
            }
        }
        default {
            return ""
        }
    }
}

# Export functions
Export-ModuleMember -Function Test-Configuration, Repair-Configuration, Get-DefaultPropertyValue
