﻿#include "http_server.hpp"
#include "user_manager.hpp"
#include "jwt_manager.hpp"
#include <nlohmann/json.hpp>
#include <iostream>
#include <thread>
#include <chrono>
#include <sstream>
#include <regex>

using json = nlohmann::json;

HttpServer::HttpServer(UserManager* user_manager, JWTManager* jwt_manager)
    : user_manager_(user_manager), jwt_manager_(jwt_manager), running_(false), server_port_(0) {
    initializeRoutes();
    std::cout << "HTTP Server initialized with OAuth 2.0 API endpoints" << std::endl;
}

HttpServer::~HttpServer() {
    stop();
}

void HttpServer::initializeRoutes() {
    // OAuth 2.0 API endpoints
    routes_["POST:/oauth/token"] = [this](const HttpRequest& req) { return handleTokenRequest(req); };
    routes_["POST:/oauth/refresh"] = [this](const HttpRequest& req) { return handleRefreshRequest(req); };
    routes_["POST:/oauth/validate"] = [this](const HttpRequest& req) { return handleValidateRequest(req); };
    routes_["POST:/oauth/revoke"] = [this](const HttpRequest& req) { return handleRevokeRequest(req); };
    routes_["GET:/health"] = [this](const HttpRequest& req) { return handleHealthCheck(req); };

    std::cout << "OAuth 2.0 API routes initialized:" << std::endl;
    std::cout << "  POST /oauth/token - Token generation" << std::endl;
    std::cout << "  POST /oauth/refresh - Token refresh" << std::endl;
    std::cout << "  POST /oauth/validate - Token validation" << std::endl;
    std::cout << "  POST /oauth/revoke - Token revocation" << std::endl;
    std::cout << "  GET /health - Health check" << std::endl;
}

void HttpServer::start(int port) {
    std::cout << "Starting HTTP server on port " << port << "..." << std::endl;
    running_ = true;
    server_port_ = port;

    std::cout << "HTTP server started. Listening for requests..." << std::endl;
    std::cout << "OAuth 2.0 API endpoints available at:" << std::endl;
    std::cout << "  http://localhost:" << port << "/oauth/token" << std::endl;
    std::cout << "  http://localhost:" << port << "/oauth/refresh" << std::endl;
    std::cout << "  http://localhost:" << port << "/oauth/validate" << std::endl;
    std::cout << "  http://localhost:" << port << "/oauth/revoke" << std::endl;
    std::cout << "  http://localhost:" << port << "/health" << std::endl;

    // Simulate HTTP server operation with sample requests
    std::cout << "\n=== Testing OAuth 2.0 API Endpoints ===" << std::endl;

    // Test 1: Health check
    HttpRequest health_req;
    health_req.method = "GET";
    health_req.path = "/health";
    auto health_response = processRequest(health_req);
    std::cout << "Health Check: " << health_response.status_code << " - " << health_response.body << std::endl;

    // Test 2: Token generation (simulated)
    HttpRequest token_req;
    token_req.method = "POST";
    token_req.path = "/oauth/token";
    token_req.body = R"({"username":"test_user","password":"test_password","grant_type":"password"})";
    auto token_response = processRequest(token_req);
    std::cout << "Token Generation: " << token_response.status_code << " - " << token_response.body.substr(0, 100) << "..." << std::endl;

    // Test 3: Token validation (using real token)
    HttpRequest validate_req;
    validate_req.method = "POST";
    validate_req.path = "/oauth/validate";

    // Extract the real access token from the token generation response
    std::string real_token = "";
    try {
        if (token_response.status_code == 200) {
            json token_json = json::parse(token_response.body);
            real_token = token_json["access_token"];
            validate_req.headers["Authorization"] = "Bearer " + real_token;
        } else {
            validate_req.headers["Authorization"] = "Bearer sample.jwt.token"; // Fallback to test invalid token
        }
    } catch (const std::exception& e) {
        validate_req.headers["Authorization"] = "Bearer sample.jwt.token"; // Fallback to test invalid token
    }

    auto validate_response = processRequest(validate_req);
    std::cout << "Token Validation: " << validate_response.status_code << " - " << validate_response.body << std::endl;

    // Simulate server running
    for (int i = 0; i < 5 && running_; ++i) {
        std::this_thread::sleep_for(std::chrono::seconds(1));
        std::cout << "OAuth 2.0 API server running... (" << (i+1) << "/5)" << std::endl;
    }

    std::cout << "HTTP server simulation complete." << std::endl;
}

void HttpServer::stop() {
    if (running_) {
        std::cout << "Stopping HTTP server..." << std::endl;
        running_ = false;
    }
}

HttpServer::HttpResponse HttpServer::processRequest(const HttpRequest& request) {
    std::string route_key = request.method + ":" + request.path;

    auto route_it = routes_.find(route_key);
    if (route_it != routes_.end()) {
        try {
            return route_it->second(request);
        } catch (const std::exception& e) {
            return createErrorResponse(500, "internal_server_error",
                                     "Internal server error: " + std::string(e.what()));
        }
    }

    return createErrorResponse(404, "not_found", "Endpoint not found");
}

HttpServer::HttpResponse HttpServer::handleTokenRequest(const HttpRequest& request) {
    try {
        auto params = parseJsonBody(request.body);

        // Extract credentials
        std::string username = params["username"];
        std::string password = params["password"];
        std::string grant_type = (params.find("grant_type") != params.end()) ? params["grant_type"] : "password";

        if (username.empty() || password.empty()) {
            return createErrorResponse(400, "invalid_request", "Missing username or password");
        }

        if (grant_type != "password") {
            return createErrorResponse(400, "unsupported_grant_type", "Only password grant type is supported");
        }

        // Authenticate user with database
        auto auth_result = user_manager_->authenticate_user(username, password);

        if (!auth_result.success) {
            return createErrorResponse(401, "invalid_grant", "Invalid username or password");
        }

        std::string user_id = auth_result.user_id;  // UUID
        std::vector<std::string> scopes = {"read", "write"};

        // Generate JWT token pair
        auto token_pair = jwt_manager_->generateTokenPair(user_id, scopes);

        if (!token_pair.success) {
            return createErrorResponse(500, "token_generation_failed", token_pair.error_message);
        }

        // Create OAuth 2.0 token response
        json response;
        response["access_token"] = token_pair.access_token.token;
        response["refresh_token"] = token_pair.refresh_token.token;
        response["token_type"] = "Bearer";
        response["expires_in"] = std::chrono::duration_cast<std::chrono::seconds>(
            token_pair.access_token.expires_at - std::chrono::system_clock::now()).count();
        response["scope"] = "read write";

        return createSuccessResponse(response.dump());

    } catch (const std::exception& e) {
        return createErrorResponse(400, "invalid_request", "Invalid request format: " + std::string(e.what()));
    }
}

HttpServer::HttpResponse HttpServer::handleRefreshRequest(const HttpRequest& request) {
    try {
        auto params = parseJsonBody(request.body);

        std::string refresh_token = params["refresh_token"];
        std::string grant_type = (params.find("grant_type") != params.end()) ? params["grant_type"] : "refresh_token";

        if (refresh_token.empty()) {
            return createErrorResponse(400, "invalid_request", "Missing refresh_token");
        }

        if (grant_type != "refresh_token") {
            return createErrorResponse(400, "unsupported_grant_type", "Only refresh_token grant type is supported");
        }

        // Refresh the token
        auto token_pair = jwt_manager_->refreshToken(refresh_token);

        if (!token_pair.success) {
            return createErrorResponse(400, "invalid_grant", token_pair.error_message);
        }

        // Create OAuth 2.0 token response
        json response;
        response["access_token"] = token_pair.access_token.token;
        response["refresh_token"] = token_pair.refresh_token.token;
        response["token_type"] = "Bearer";
        response["expires_in"] = std::chrono::duration_cast<std::chrono::seconds>(
            token_pair.access_token.expires_at - std::chrono::system_clock::now()).count();
        response["scope"] = "read write";

        return createSuccessResponse(response.dump());

    } catch (const std::exception& e) {
        return createErrorResponse(400, "invalid_request", "Invalid request format: " + std::string(e.what()));
    }
}

HttpServer::HttpResponse HttpServer::handleValidateRequest(const HttpRequest& request) {
    try {
        std::string token = extractBearerToken(request);

        if (token.empty()) {
            return createErrorResponse(401, "invalid_token", "Missing or invalid Authorization header");
        }

        // Validate the token
        auto validation = jwt_manager_->validateToken(token);

        if (!validation.valid) {
            return createErrorResponse(401, "invalid_token", validation.error_message);
        }

        // Create validation response
        json response;
        response["valid"] = true;
        response["user_id"] = validation.user_id;
        response["scopes"] = validation.scopes;
        response["expires_at"] = std::chrono::duration_cast<std::chrono::seconds>(
            validation.expires_at.time_since_epoch()).count();

        return createSuccessResponse(response.dump());

    } catch (const std::exception& e) {
        return createErrorResponse(400, "invalid_request", "Invalid request format: " + std::string(e.what()));
    }
}

HttpServer::HttpResponse HttpServer::handleRevokeRequest(const HttpRequest& request) {
    try {
        auto params = parseJsonBody(request.body);

        std::string token = params["token"];

        if (token.empty()) {
            return createErrorResponse(400, "invalid_request", "Missing token parameter");
        }

        // Revoke the token
        bool revoked = jwt_manager_->revokeToken(token);

        if (!revoked) {
            return createErrorResponse(400, "invalid_token", "Token could not be revoked");
        }

        // Create revocation response (empty body for success)
        HttpResponse response(200);
        response.body = "{}";

        return response;

    } catch (const std::exception& e) {
        return createErrorResponse(400, "invalid_request", "Invalid request format: " + std::string(e.what()));
    }
}

HttpServer::HttpResponse HttpServer::handleHealthCheck(const HttpRequest& request) {
    json response;
    response["status"] = "healthy";
    response["service"] = "auth-service";
    response["version"] = "1.0.0";
    response["oauth2_endpoints"] = {
        "/oauth/token",
        "/oauth/refresh",
        "/oauth/validate",
        "/oauth/revoke"
    };
    response["timestamp"] = std::chrono::duration_cast<std::chrono::seconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();

    return createSuccessResponse(response.dump());
}

HttpServer::HttpResponse HttpServer::createErrorResponse(int status_code, const std::string& error,
                                                        const std::string& description) {
    HttpResponse response(status_code);

    json error_json;
    error_json["error"] = error;
    if (!description.empty()) {
        error_json["error_description"] = description;
    }

    response.body = error_json.dump();
    return response;
}

HttpServer::HttpResponse HttpServer::createSuccessResponse(const std::string& json_body) {
    HttpResponse response(200);
    response.body = json_body;
    return response;
}

std::string HttpServer::extractBearerToken(const HttpRequest& request) {
    auto auth_it = request.headers.find("Authorization");
    if (auth_it == request.headers.end()) {
        return "";
    }

    const std::string& auth_header = auth_it->second;
    const std::string bearer_prefix = "Bearer ";

    if (auth_header.substr(0, bearer_prefix.length()) == bearer_prefix) {
        return auth_header.substr(bearer_prefix.length());
    }

    return "";
}

std::unordered_map<std::string, std::string> HttpServer::parseJsonBody(const std::string& body) {
    std::unordered_map<std::string, std::string> params;

    if (body.empty()) {
        return params;
    }

    try {
        json json_body = json::parse(body);

        for (auto& [key, value] : json_body.items()) {
            if (value.is_string()) {
                params[key] = value.get<std::string>();
            } else {
                params[key] = value.dump();
            }
        }
    } catch (const std::exception& e) {
        // If JSON parsing fails, return empty params
        std::cerr << "JSON parsing error: " << e.what() << std::endl;
    }

    return params;
}

HttpServer::HttpRequest HttpServer::parseRequest(const std::string& raw_request) {
    HttpRequest request;

    // This is a simplified parser for demonstration
    // In a real implementation, you'd use a proper HTTP library

    std::istringstream stream(raw_request);
    std::string line;

    // Parse request line
    if (std::getline(stream, line)) {
        std::istringstream request_line(line);
        request_line >> request.method >> request.path;
    }

    // Parse headers
    while (std::getline(stream, line) && !line.empty() && line != "\r") {
        size_t colon_pos = line.find(':');
        if (colon_pos != std::string::npos) {
            std::string key = line.substr(0, colon_pos);
            std::string value = line.substr(colon_pos + 1);

            // Trim whitespace
            key.erase(0, key.find_first_not_of(" \t"));
            key.erase(key.find_last_not_of(" \t") + 1);
            value.erase(0, value.find_first_not_of(" \t"));
            value.erase(value.find_last_not_of(" \t") + 1);

            request.headers[key] = value;
        }
    }

    // Parse body
    std::string body_line;
    while (std::getline(stream, body_line)) {
        request.body += body_line + "\n";
    }

    return request;
}

std::string HttpServer::generateResponse(const HttpResponse& response) {
    std::ostringstream response_stream;

    // Status line
    response_stream << "HTTP/1.1 " << response.status_code << " ";

    switch (response.status_code) {
        case 200: response_stream << "OK"; break;
        case 400: response_stream << "Bad Request"; break;
        case 401: response_stream << "Unauthorized"; break;
        case 404: response_stream << "Not Found"; break;
        case 500: response_stream << "Internal Server Error"; break;
        default: response_stream << "Unknown"; break;
    }

    response_stream << "\r\n";

    // Headers
    for (const auto& [key, value] : response.headers) {
        response_stream << key << ": " << value << "\r\n";
    }

    // Content-Length header
    response_stream << "Content-Length: " << response.body.length() << "\r\n";

    // Empty line before body
    response_stream << "\r\n";

    // Body
    response_stream << response.body;

    return response_stream.str();
}
