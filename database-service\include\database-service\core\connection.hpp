/**
 * @file connection.hpp
 * @brief Database connection management and query execution interface
 * 
 * This file defines the Connection class which provides a thread-safe wrapper
 * around PostgreSQL database connections with support for:
 * - Connection pooling
 * - SSL/TLS encryption
 * - Transaction management
 * - Synchronous and asynchronous query execution
 * - Parameterized queries
 * 
 * @note This class is thread-safe for concurrent method calls on different instances,
 *       but individual instances should not be shared between threads without
 *       external synchronization.
 */

#pragma once

// Standard Library Headers
#include <string>
#include <vector>
#include <memory>
#include <functional>
#include <concepts>
#include <span>  // C++20 feature for parameter passing
#include <mutex> // Thread safety

namespace dbservice::core {

// Forward declarations
class Transaction;

/**
 * @class Connection
 * @brief Thread-safe PostgreSQL database connection wrapper
 * 
 * This class provides a high-level, RAII-compliant interface for managing
 * PostgreSQL database connections. It supports both synchronous and asynchronous
 * query execution, transaction management, and connection pooling.
 * 
 * @note Connection instances are not thread-safe. For multi-threaded access,
 *       use a connection pool or external synchronization.
 * 
 * Example usage:
 * @code
 * dbservice::core::Connection conn("postgresql://user:pass@localhost/dbname");
 * if (conn.open()) {
 *     auto result = conn.executeQuery("SELECT * FROM users WHERE id = $1", {"42"});
 *     // Process results...
 * }
 * @endcode
 */
class Connection : public std::enable_shared_from_this<Connection> {
public:
    /**
     * @brief Construct a new database connection
     * 
     * @param connectionString PostgreSQL connection string in the format:
     *                         "postgresql://[user[:password]@][netloc][:port][/dbname][?param1=value1&...]"
     * @param useSSL If true, enforces SSL/TLS encryption for the connection
     * @throws std::invalid_argument if connectionString is empty
     * 
     * @note The connection is not established until open() is called.
     * @warning The connection string may contain sensitive credentials.
     *          Consider using environment variables or secure storage.
     */
    explicit Connection(std::string_view connectionString, bool useSSL = true);

    /**
     * @brief Destroy the Connection object
     * 
     * Automatically closes the connection if it's still open.
     * Any active transactions will be rolled back.
     */
    ~Connection() noexcept;
    
    // Prevent copying to avoid resource management issues
    Connection(const Connection&) = delete;
    Connection& operator=(const Connection&) = delete;
    
    // Allow move semantics for efficient resource transfer
    Connection(Connection&& other) noexcept;
    Connection& operator=(Connection&& other) noexcept;

    /**
     * @brief Open the database connection
     * 
     * Establishes a connection to the PostgreSQL server using the parameters
     * provided in the constructor. If the connection is already open,
     * this is a no-op and returns true.
     * 
     * @return bool 
     *         - true: Connection was successfully established
     *         - false: Connection failed (check logs for details)
     * 
     * @post If successful, the connection is ready for queries
     * @throws std::runtime_error if a fatal error occurs during connection
     * 
     * @note This method is idempotent and thread-safe.
     */
    bool open();

    /**
     * @brief Close the database connection
     * 
     * Closes the connection to the database server and releases associated
     * resources. If the connection is already closed, this is a no-op.
     * 
     * @note Any active transactions will be rolled back automatically.
     * @warning After calling close(), the connection cannot be reopened.
     *          Create a new Connection instance instead.
     * 
     * @post The connection is in a closed state and cannot be used for queries
     */
    void close() noexcept;

    /**
     * @brief Check if the connection is open and healthy
     * 
     * Verifies both the local open state and performs a quick check
     * with the database server to ensure the connection is still valid.
     * 
     * @return bool 
     *         - true: Connection is open and responsive
     *         - false: Connection is closed, broken, or unresponsive
     * 
     * @note This method does not throw exceptions.
     */
    [[nodiscard]] bool isOpen() const noexcept;

    /**
     * @brief Execute a parameterized SQL query and return all results
     * 
     * Executes the provided SQL query with the given parameters and returns
     * the complete result set. This is suitable for small to medium result sets.
     * 
     * @param query SQL query string with $1, $2, etc. placeholders
     * @param params Vector of parameter values (must match placeholders in query)
     * @return std::vector<std::vector<std::string>> 
     *         - Outer vector represents rows
     *         - Inner vector represents column values within each row
     *         - Empty vector on error or empty result set
     * 
     * @throws std::invalid_argument if query is empty
     * @throws std::runtime_error on database errors
     * 
     * @note For large result sets, consider using executeQueryWithCallback()
     *       to process rows incrementally.
     * @example
     *   auto result = conn.executeQuery(
     *     "SELECT name, email FROM users WHERE age > $1 AND status = $2",
     *     {"18", "active"}
     *   );
     */
    [[nodiscard]] std::vector<std::vector<std::string>> 
    executeQuery(std::string_view query, std::span<const std::string> params = {});

    /**
     * @brief Execute a query and process results incrementally via callback
     * 
     * Efficiently processes large result sets by invoking the provided callback
     * for each row as it's received from the database, minimizing memory usage.
     * 
     * @param query SQL query string with $1, $2, etc. placeholders
     * @param callback Callback function invoked for each row
     *                 - Parameter: vector of column values for the current row
     *                 - Return: true to continue processing, false to stop
     * @param params Vector of parameter values (must match placeholders in query)
     * 
     * @throws std::invalid_argument if query is empty or callback is null
     * @throws std::runtime_error on database errors
     * 
     * @note This method is more memory-efficient than executeQuery() for large result sets
     *       as it doesn't store all results in memory at once.
     * @warning The callback may be called from a background thread.
     *          Ensure thread safety in the callback implementation.
     * 
     * @example
     *   conn.executeQueryWithCallback(
     *     "SELECT id, name FROM large_table WHERE category = $1",
     *     [](const auto& row) {
     *         std::cout << "ID: " << row[0] << ", Name: " << row[1] << '\n';
     *         return true; // Continue processing
     *     },
     *     {"books"}
     *   );
     */
    void executeQueryWithCallback(
        std::string_view query,
        std::function<bool(const std::vector<std::string>&)> callback,
        std::span<const std::string> params = {});

    /**
     * @brief Execute a non-query SQL statement (INSERT, UPDATE, DELETE, etc.)
     * 
     * Executes a SQL statement that doesn't return a result set, such as
     * INSERT, UPDATE, DELETE, or DDL statements.
     * 
     * @param statement SQL statement to execute with $1, $2, etc. placeholders
     * @param params Vector of parameter values (must match placeholders in statement)
     * @return int 
     *         - Number of rows affected by the statement
     *         - -1 if an error occurred (check logs for details)
     * 
     * @throws std::invalid_argument if statement is empty
     * @throws std::runtime_error on database errors
     * 
     * @note For statements that don't affect rows (e.g., CREATE TABLE),
     *       the return value will be 0 on success.
     * @warning This method executes within the current transaction context.
     *          Remember to commit or rollback as needed.
     */
    int executeNonQuery(std::string_view statement, std::span<const std::string> params = {});

    /**
     * @brief Begin a new database transaction
     * 
     * Starts a new transaction with the default isolation level (READ COMMITTED).
     * The transaction will remain active until explicitly committed or rolled back,
     * or until the connection is closed.
     * 
     * @return std::shared_ptr<Transaction> 
     *         - Non-null: Successfully started transaction
     *         - nullptr: Failed to start transaction (check logs for details)
     * 
     * @throws std::runtime_error if called within an active transaction
     *         or if the connection is not open
     * 
     * @note The returned Transaction object manages the transaction's lifecycle.
     *       When the last reference to the Transaction is destroyed,
     *       the transaction will be rolled back if not already committed.
     * 
     * @example
     *   auto txn = conn.beginTransaction();
     *   if (txn) {
     *       try {
     *           conn.executeNonQuery("UPDATE accounts SET balance = balance - 100 WHERE id = 1");
     *           conn.executeNonQuery("UPDATE accounts SET balance = balance + 100 WHERE id = 2");
     *           txn->commit();
     *       } catch (...) {
     *           txn->rollback();
     *           throw;
     *       }
     *   }
     */
    [[nodiscard]] std::shared_ptr<Transaction> beginTransaction();

private:
    /**
     * @brief Validate that the connection is open and ready
     * @throws std::runtime_error if connection is not valid
     */
    void validateConnection() const;

    /**
     * @brief Prepare a statement
     * @param statement Statement to prepare
     * @param params Statement parameters
     * @return Prepared statement
     */
    void* prepareStatement(std::string_view statement, std::span<const std::string> params);

    std::string connectionString_;
    bool useSSL_;
    void* connection_; // PGconn*
    bool isOpen_;
    mutable std::mutex mutex_; // Thread safety
};

} // namespace dbservice::core
