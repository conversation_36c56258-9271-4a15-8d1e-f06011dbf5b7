# Build-AuthUI.psm1 - Module for building the auth service UI

<#
.SYNOPSIS
    Provides functionality for building the auth service React/TypeScript UI.

.DESCRIPTION
    This module handles the build process for the auth service UI,
    including dependency installation, compilation, and preparation for deployment.

.NOTES
    File Name      : Build-AuthUI.psm1
    Author         : Auth Service Deployment Team
    Prerequisite   : PowerShell 5.1 or later
    Copyright      : (c) 2025 Augment
#>

function Build-AuthUI {
    [CmdletBinding()]
    param()

    Write-Host "=== Building Auth Service UI ===" -ForegroundColor Cyan
    Write-Host ""

    # Check if configuration is loaded
    if ($null -eq $script:Config) {
        Write-Host "❌ Configuration is not loaded. Please load configuration first." -ForegroundColor Red
        return $false
    }

    # UI source directory (to be created)
    $uiSourceDir = "D:\Coding_Projects\auth-service\auth-service-ui"
    
    Write-Host "UI Source Directory: $uiSourceDir" -ForegroundColor Yellow
    Write-Host ""

    # Check if UI source directory exists
    if (-not (Test-Path $uiSourceDir)) {
        Write-Host "❌ Auth service UI source directory not found: $uiSourceDir" -ForegroundColor Red
        Write-Host ""
        Write-Host "To create the auth service UI:" -ForegroundColor Cyan
        Write-Host "1. Create directory: $uiSourceDir" -ForegroundColor White
        Write-Host "2. Initialize React/TypeScript project" -ForegroundColor White
        Write-Host "3. Implement auth service UI components" -ForegroundColor White
        Write-Host ""
        Write-Host "Example commands:" -ForegroundColor Yellow
        Write-Host "  mkdir `"$uiSourceDir`"" -ForegroundColor Gray
        Write-Host "  cd `"$uiSourceDir`"" -ForegroundColor Gray
        Write-Host "  npx create-react-app . --template typescript" -ForegroundColor Gray
        Write-Host ""
        return $false
    }

    Write-Host "✅ UI source directory found" -ForegroundColor Green

    # Check for package.json
    $packageJsonPath = Join-Path $uiSourceDir "package.json"
    if (-not (Test-Path $packageJsonPath)) {
        Write-Host "❌ package.json not found. This doesn't appear to be a Node.js project." -ForegroundColor Red
        return $false
    }

    Write-Host "✅ package.json found" -ForegroundColor Green

    # Install dependencies
    Write-Host ""
    Write-Host "Installing UI dependencies..." -ForegroundColor Yellow
    
    try {
        Set-Location $uiSourceDir
        $installResult = npm install
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Dependencies installed successfully" -ForegroundColor Green
        } else {
            Write-Host "❌ Failed to install dependencies" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ Error installing dependencies: $_" -ForegroundColor Red
        return $false
    } finally {
        Set-Location $PSScriptRoot
    }

    # Build the UI
    Write-Host ""
    Write-Host "Building auth service UI..." -ForegroundColor Yellow
    
    try {
        Set-Location $uiSourceDir
        $buildResult = npm run build
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ UI built successfully" -ForegroundColor Green
        } else {
            Write-Host "❌ Failed to build UI" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ Error building UI: $_" -ForegroundColor Red
        return $false
    } finally {
        Set-Location $PSScriptRoot
    }

    # Check build output
    $buildDir = Join-Path $uiSourceDir "build"
    if (Test-Path $buildDir) {
        $buildFiles = Get-ChildItem $buildDir -Recurse -File
        Write-Host ""
        Write-Host "✅ Build output created: $($buildFiles.Count) files" -ForegroundColor Green
        Write-Host "Build directory: $buildDir" -ForegroundColor Gray
    } else {
        Write-Host "❌ Build directory not found" -ForegroundColor Red
        return $false
    }

    Write-Host ""
    Write-Host "✅ Auth service UI build completed successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Next steps:" -ForegroundColor Cyan
    Write-Host "- Run menu option 19 (Deploy UI) to deploy to server" -ForegroundColor White
    
    return $true
}

# Export the function
Export-ModuleMember -Function Build-AuthUI
