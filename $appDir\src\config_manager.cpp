#include "config_manager.hpp"
#include <fstream>

ConfigManager::ConfigManager(const std::string& config_file) {
    load_config(config_file);
}

void ConfigManager::load_config(const std::string& config_file) {
    std::ifstream file(config_file);
        config_ = nlohmann::json{{"database", {{"host", "***********"}, {"port", 5432}, {"name", "auth_service"}, {"user", "auth_service"}, {"password", "password2311"}}}, {"server", {{"port", 8082}, {"log_level", "info"}}}};
        return;
    }
    file >> config_;
}

std::string ConfigManager::get_database_host() const { return config_["database"]["host"]; }
int ConfigManager::get_database_port() const { return config_["database"]["port"]; }
std::string ConfigManager::get_database_name() const { return config_["database"]["name"]; }
std::string ConfigManager::get_database_user() const { return config_["database"]["user"]; }
std::string ConfigManager::get_database_password() const { return config_["database"]["password"]; }
int ConfigManager::get_server_port() const { return config_["server"]["port"]; }
std::string ConfigManager::get_log_level() const { return config_["server"]["log_level"]; }
