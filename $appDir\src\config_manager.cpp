#include "config_manager.hpp"
#include <fstream>
#include <stdexcept>

ConfigManager::ConfigManager(const std::string& config_file) {
    load_config(config_file);
}

void ConfigManager::load_config(const std::string& config_file) {
    std::ifstream file(config_file);
        // Create default configuration
        config_ = nlohmann::json{
            {"database", {
                {"host", "localhost"},
                {"port", 5432},
                {"name", "auth_service_dev"},
                {"user", "auth_service_dev"},
                {"password", "password2311"}
            }},
            {"server", {
                {"port", 8082},
                {"log_level", "info"}
            }}
        };
        return;
    }
    
    file >> config_;
}

std::string ConfigManager::get_database_host() const {
    return config_["database"]["host"].get<std::string>();
}

int ConfigManager::get_database_port() const {
    return config_["database"]["port"].get<int>();
}

std::string ConfigManager::get_database_name() const {
    return config_["database"]["name"].get<std::string>();
}

std::string ConfigManager::get_database_user() const {
    return config_["database"]["user"].get<std::string>();
}

std::string ConfigManager::get_database_password() const {
    return config_["database"]["password"].get<std::string>();
}

int ConfigManager::get_server_port() const {
    return config_["server"]["port"].get<int>();
}

std::string ConfigManager::get_log_level() const {
    return config_["server"]["log_level"].get<std::string>();
}
